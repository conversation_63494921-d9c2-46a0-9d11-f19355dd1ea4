2025-07-30 07:26:47 +0000 [MT] Initial pipeline context: <IDEDistributionProcessingPipelineContext: 0x11610c9b0; archive(resolved)="<IDEArchive: 0x1149ede10>", distributionTask(resolved)="2", distributionDestination(resolved)="1", distributionMethod(resolved)="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team(resolved)="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	Chain (14, self inclusive):
	<IDEDistributionProcessingPipelineContext: 0x11610c9b0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionProcessingPipelineContext: 0x12ae8ffe0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x11688d890; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x12aede180; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x13b2bde30; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x116144f90; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x13b2bb2f0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x13b2b79d0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x1161431b0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="<IDEProvisioningBasicTeam: 0x13b2bb570; teamID='TCDD8WRJJ4', teamName='(null)'>">
	<IDEDistributionContext: 0x116141570; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="(null)">
	<IDEDistributionContext: 0x1161403c0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="(null)">
	<IDEDistributionContext: 0x11613ed60; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="(null)">
	<IDEDistributionContext: 0x15c2058f0; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x12aeba6d0>", team="(null)">
	<IDEDistributionContext: 0x1149eea60; archive = "<IDEArchive: 0x1149ede10>", distributionMethod="(null)", team="(null)">
</IDEDistributionProcessingPipelineContext: 0x11610c9b0>
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionCreateDestRootStep
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionCopyItemStep
2025-07-30 07:26:47 +0000 [MT] Running /usr/bin/ditto '-V' '/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app' '/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root/Payload/VantisInstructor.app'
2025-07-30 07:26:47 +0000  >>> Copying /Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app 
2025-07-30 07:26:47 +0000  copying file ./_CodeSignature/CodeResources ... 
2025-07-30 07:26:47 +0000  5281 bytes for ./_CodeSignature/CodeResources
2025-07-30 07:26:47 +0000  copying file ./QuizManagementAPIDebug.md ... 
2025-07-30 07:26:47 +0000  5638 bytes for ./QuizManagementAPIDebug.md
2025-07-30 07:26:47 +0000  copying file ./APP_ICON_SETUP.md ... 
2025-07-30 07:26:47 +0000  1464 bytes for ./APP_ICON_SETUP.md
2025-07-30 07:26:47 +0000  copying file ./BeVietnamPro-SemiBold.ttf ... 
2025-07-30 07:26:47 +0000  136736 bytes for ./BeVietnamPro-SemiBold.ttf
2025-07-30 07:26:47 +0000  copying file ./AuthenticationTestPlan.md ... 
2025-07-30 07:26:47 +0000  5428 bytes for ./AuthenticationTestPlan.md
2025-07-30 07:26:47 +0000  copying file ./<EMAIL> ... 
2025-07-30 07:26:47 +0000  5576 bytes for ./<EMAIL>
2025-07-30 07:26:47 +0000  copying file ./BeVietnamPro-Regular.ttf ... 
2025-07-30 07:26:47 +0000  132948 bytes for ./BeVietnamPro-Regular.ttf
2025-07-30 07:26:47 +0000  copying file ./capture_quiz_logs.sh ... 
2025-07-30 07:26:47 +0000  5765 bytes for ./capture_quiz_logs.sh
2025-07-30 07:26:47 +0000  copying file ./BeVietnamPro-Medium.ttf ... 
2025-07-30 07:26:47 +0000  135980 bytes for ./BeVietnamPro-Medium.ttf
2025-07-30 07:26:47 +0000  copying file ./CHECKIN_FEATURE.md ... 
2025-07-30 07:26:47 +0000  5662 bytes for ./CHECKIN_FEATURE.md
2025-07-30 07:26:47 +0000  copying file ./README.md ... 
2025-07-30 07:26:47 +0000  1494 bytes for ./README.md
2025-07-30 07:26:47 +0000  copying file ./BeVietnamPro-Bold.ttf ... 
2025-07-30 07:26:47 +0000  140300 bytes for ./BeVietnamPro-Bold.ttf
2025-07-30 07:26:47 +0000  copying file ./Assets.car ... 
2025-07-30 07:26:47 +0000  885504 bytes for ./Assets.car
2025-07-30 07:26:47 +0000  copying file ./AppIcon76x76@2x~ipad.png ... 
2025-07-30 07:26:47 +0000  7593 bytes for ./AppIcon76x76@2x~ipad.png
2025-07-30 07:26:47 +0000  copying file ./VantisInstructor ... 
2025-07-30 07:26:47 +0000  5765536 bytes for ./VantisInstructor
2025-07-30 07:26:47 +0000  copying file ./embedded.mobileprovision ... 
16838 bytes for ./embedded.mobileprovision
copying file ./logo-vantis-navicon.png ... 
6973 bytes for ./logo-vantis-navicon.png
copying file ./Info.plist ... 
2025-07-30 07:26:47 +0000  1878 bytes for ./Info.plist
2025-07-30 07:26:47 +0000  copying file ./PkgInfo ... 
2025-07-30 07:26:47 +0000  8 bytes for ./PkgInfo
2025-07-30 07:26:47 +0000 [MT] /usr/bin/ditto exited with 0
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionEmbedProfileStep
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionInfoPlistStep
2025-07-30 07:26:47 +0000 [MT] Skipping step: IDEDistributionInfoPlistStep because it said so
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionAppThinningPlistStep
2025-07-30 07:26:47 +0000 [MT] Skipping step: IDEDistributionAppThinningPlistStep because it said so
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionSymbolsStep
2025-07-30 07:26:47 +0000 [MT] Skipping step: IDEDistributionSymbolsStep because it said so
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionAppThinningStep
2025-07-30 07:26:47 +0000 [MT] Skipping step: IDEDistributionAppThinningStep because it said so
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionArchThinningStep
2025-07-30 07:26:47 +0000 [MT] Item /Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-07-30 07:26:47 +0000 [MT] Archs to thin for item /Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app are ["arm64e"]
2025-07-30 07:26:47 +0000 [MT] Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root/Payload/VantisInstructor.app/VantisInstructor' '-verify_arch' 'arm64e'
2025-07-30 07:26:47 +0000 [MT] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-07-30 07:26:47 +0000 [MT] Skipping architecture thinning for item "VantisInstructor" because arch "arm64e" wasn't found
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionODRStep
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionStripXattrsStep
2025-07-30 07:26:47 +0000 [MT] Skipping stripping extended attributes because the codesign step will strip them.
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionCodesignStep
2025-07-30 07:26:47 +0000 [MT] Entitlements for <IDEDistributionItem: 0x13b2b9100; bundleID='com.vantisedu.vantisinstructor', path='<DVTFilePath:0x1149ee340:'/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x12aed2c80; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x11646fd20; name='Apple Development: LE ANH TUAN (X9662DKKZ7)', hash='9D777A990A8CC34011396B8B8A25819A811E7B92', serialNumber='7FB761386CFE22938B0AFA98A496024A', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-07-25 02:38:18 +0000''>', entitlements='{
    "application-identifier" = "TCDD8WRJJ4.com.vantisedu.vantisinstructor";
    "com.apple.developer.team-identifier" = TCDD8WRJJ4;
    "get-task-allow" = 1;
}', teamID='TCDD8WRJJ4', identifier='com.vantisedu.vantisinstructor', executablePath='<DVTFilePath:0x13b290e80:'/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app/VantisInstructor'>', hardenedRuntime='0'>'>: {
    "application-identifier" = "TCDD8WRJJ4.com.vantisedu.vantisinstructor";
    "com.apple.developer.team-identifier" = TCDD8WRJJ4;
    "get-task-allow" = 1;
}
2025-07-30 07:26:47 +0000 [MT] Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-07-30 07:26:47 +0000 [MT] Entitlements for <IDEDistributionItem: 0x13b2b9100; bundleID='com.vantisedu.vantisinstructor', path='<DVTFilePath:0x1149ee340:'/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x12aed2c80; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x1164c7c60; name='Apple Development: LE ANH TUAN (X9662DKKZ7)', hash='9D777A990A8CC34011396B8B8A25819A811E7B92', serialNumber='7FB761386CFE22938B0AFA98A496024A', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-07-25 02:38:18 +0000''>', entitlements='{
    "application-identifier" = "TCDD8WRJJ4.com.vantisedu.vantisinstructor";
    "com.apple.developer.team-identifier" = TCDD8WRJJ4;
    "get-task-allow" = 1;
}', teamID='TCDD8WRJJ4', identifier='com.vantisedu.vantisinstructor', executablePath='<DVTFilePath:0x13b290e80:'/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app/VantisInstructor'>', hardenedRuntime='0'>'> are: {
    "application-identifier" = "TCDD8WRJJ4.com.vantisedu.vantisinstructor";
    "com.apple.developer.team-identifier" = TCDD8WRJJ4;
    "get-task-allow" = 1;
}
2025-07-30 07:26:47 +0000 [MT] Writing entitlements for <IDEDistributionItem: 0x13b2b9100; bundleID='com.vantisedu.vantisinstructor', path='<DVTFilePath:0x1149ee340:'/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x12aed2c80; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x1164c7fc0; name='Apple Development: LE ANH TUAN (X9662DKKZ7)', hash='9D777A990A8CC34011396B8B8A25819A811E7B92', serialNumber='7FB761386CFE22938B0AFA98A496024A', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-07-25 02:38:18 +0000''>', entitlements='{
    "application-identifier" = "TCDD8WRJJ4.com.vantisedu.vantisinstructor";
    "com.apple.developer.team-identifier" = TCDD8WRJJ4;
    "get-task-allow" = 1;
}', teamID='TCDD8WRJJ4', identifier='com.vantisedu.vantisinstructor', executablePath='<DVTFilePath:0x13b290e80:'/Users/<USER>/Develop/ios-apps/eb-lms-vantis-instructor-app/VantisInstructor.xcarchive/Products/Applications/VantisInstructor.app/VantisInstructor'>', hardenedRuntime='0'>'> to: /var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/entitlements~~~XyhlJW
2025-07-30 07:26:47 +0000 [MT] invoking codesign: <NSConcreteTask: 0x1164cf930; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    9D777A990A8CC34011396B8B8A25819A811E7B92,
    "--entitlements",
    "/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/entitlements~~~XyhlJW",
    "--preserve-metadata=identifier,flags,runtime",
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root/Payload/VantisInstructor.app"
)'>
2025-07-30 07:26:47 +0000 [MT] codesign output: /var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root/Payload/VantisInstructor.app: replacing existing signature
/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root/Payload/VantisInstructor.app: signed app bundle with Mach-O thin (arm64) [com.vantisedu.vantisinstructor]
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionZipODRItemStep
2025-07-30 07:26:47 +0000 [MT] Skipping step: IDEDistributionZipODRItemStep because it said so
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionCreateIPAStep
2025-07-30 07:26:47 +0000 [MT] Running /usr/bin/ditto '-V' '-c' '-k' '--norsrc' '/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root' '/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Packages/VantisInstructor.ipa'
2025-07-30 07:26:47 +0000  >>> Copying /var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root 
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/_CodeSignature/CodeResources ... 
2025-07-30 07:26:47 +0000  5281 bytes for ./Payload/VantisInstructor.app/_CodeSignature/CodeResources
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/QuizManagementAPIDebug.md ... 
2025-07-30 07:26:47 +0000  5638 bytes for ./Payload/VantisInstructor.app/QuizManagementAPIDebug.md
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/APP_ICON_SETUP.md ... 
2025-07-30 07:26:47 +0000  1464 bytes for ./Payload/VantisInstructor.app/APP_ICON_SETUP.md
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/BeVietnamPro-SemiBold.ttf ... 
2025-07-30 07:26:47 +0000  136736 bytes for ./Payload/VantisInstructor.app/BeVietnamPro-SemiBold.ttf
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/AuthenticationTestPlan.md ... 
2025-07-30 07:26:47 +0000  5428 bytes for ./Payload/VantisInstructor.app/AuthenticationTestPlan.md
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/<EMAIL> ... 
2025-07-30 07:26:47 +0000  5576 bytes for ./Payload/VantisInstructor.app/<EMAIL>
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/BeVietnamPro-Regular.ttf ... 
2025-07-30 07:26:47 +0000  132948 bytes for ./Payload/VantisInstructor.app/BeVietnamPro-Regular.ttf
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/capture_quiz_logs.sh ... 
2025-07-30 07:26:47 +0000  5765 bytes for ./Payload/VantisInstructor.app/capture_quiz_logs.sh
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/BeVietnamPro-Medium.ttf ... 
2025-07-30 07:26:47 +0000  135980 bytes for ./Payload/VantisInstructor.app/BeVietnamPro-Medium.ttf
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/CHECKIN_FEATURE.md ... 
2025-07-30 07:26:47 +0000  5662 bytes for ./Payload/VantisInstructor.app/CHECKIN_FEATURE.md
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/README.md ... 
2025-07-30 07:26:47 +0000  1494 bytes for ./Payload/VantisInstructor.app/README.md
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/BeVietnamPro-Bold.ttf ... 
2025-07-30 07:26:47 +0000  140300 bytes for ./Payload/VantisInstructor.app/BeVietnamPro-Bold.ttf
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/Assets.car ... 
2025-07-30 07:26:47 +0000  885504 bytes for ./Payload/VantisInstructor.app/Assets.car
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/AppIcon76x76@2x~ipad.png ... 
2025-07-30 07:26:47 +0000  7593 bytes for ./Payload/VantisInstructor.app/AppIcon76x76@2x~ipad.png
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/VantisInstructor ... 
2025-07-30 07:26:47 +0000  5765536 bytes for ./Payload/VantisInstructor.app/VantisInstructor
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/embedded.mobileprovision ... 
16838 bytes for ./Payload/VantisInstructor.app/embedded.mobileprovision
copying file ./Payload/VantisInstructor.app/logo-vantis-navicon.png ... 
2025-07-30 07:26:47 +0000  6973 bytes for ./Payload/VantisInstructor.app/logo-vantis-navicon.png
2025-07-30 07:26:47 +0000  copying file ./Payload/VantisInstructor.app/Info.plist ... 
2025-07-30 07:26:47 +0000  1878 bytes for ./Payload/VantisInstructor.app/Info.plist
copying file ./Payload/VantisInstructor.app/PkgInfo ... 
2025-07-30 07:26:47 +0000  8 bytes for ./Payload/VantisInstructor.app/PkgInfo
2025-07-30 07:26:47 +0000 [MT] /usr/bin/ditto exited with 0
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionAppStoreInformationStep
2025-07-30 07:26:47 +0000 [MT] Skipping step: IDEDistributionAppStoreInformationStep because it said so
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionGenerateProcessedDistributionItems
2025-07-30 07:26:47 +0000 [MT] IDEDistributionItem init <DVTFilePath:0x1164ad4a0:'/var/folders/tm/18c4y8653jn72155cc1ztxcm0000gn/T/XcodeDistPipeline.~~~qILWuV/Root/Payload/VantisInstructor.app'>
2025-07-30 07:26:47 +0000 [MT] Processing step: IDEDistributionCreateManifestStep
