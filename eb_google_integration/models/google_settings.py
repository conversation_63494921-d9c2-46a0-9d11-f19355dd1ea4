# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import uuid
from datetime import datetime, timedelta

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

try:
    from google.oauth2 import service_account
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_API_IMPORT_ERROR = None
except ImportError as err:
    GOOGLE_API_IMPORT_ERROR = err
    _logger.error("Không thể import thư viện Google API: %s", err)


class GoogleSettings(models.Model):
    _name = 'eb.google.settings'
    _description = 'Cài đặt Google API'

    name = fields.Char(string='Tên', required=True, default='Google API Settings')
    active = fields.Boolean(string='<PERSON>ích hoạt', default=True)

    # Service Account
    service_account_json = fields.Text(
        string='Service Account JSON Key',
        help='JSON key của Service Account từ Google Cloud Console',
    )
    service_account_email = fields.Char(
        string='Email Service Account',
        compute='_compute_service_account_info',
        store=True,
    )
    service_account_project_id = fields.Char(
        string='Project ID',
        compute='_compute_service_account_info',
        store=True,
    )

    # Calendar settings
    calendar_user_email = fields.Char(
        string='Email người dùng Calendar',
        help='Email của người dùng mà Service Account sẽ thay mặt để tạo sự kiện',
    )

    # Status
    last_check_date = fields.Datetime(string='Lần kiểm tra cuối', readonly=True)
    is_valid = fields.Boolean(string='Hợp lệ', readonly=True, default=False)
    status_message = fields.Text(string='Thông báo trạng thái', readonly=True)

    @api.depends('service_account_json')
    def _compute_service_account_info(self):
        for record in self:
            if record.service_account_json:
                try:
                    sa_info = json.loads(record.service_account_json)
                    record.service_account_email = sa_info.get('client_email', '')
                    record.service_account_project_id = sa_info.get('project_id', '')
                except (json.JSONDecodeError, ValueError):
                    record.service_account_email = ''
                    record.service_account_project_id = ''
            else:
                record.service_account_email = ''
                record.service_account_project_id = ''

    def action_check_connection(self):
        """Kiểm tra kết nối với Google API"""
        self.ensure_one()

        if GOOGLE_API_IMPORT_ERROR:
            raise UserError(_(
                "Không thể kết nối với Google API. Vui lòng cài đặt các thư viện cần thiết:\n"
                "pip install google-api-python-client google-auth google-auth-httplib2\n\n"
                "Chi tiết lỗi: %s") % GOOGLE_API_IMPORT_ERROR
            )

        if not self.service_account_json:
            raise UserError(_("Vui lòng cung cấp Service Account JSON key"))

        if not self.calendar_user_email:
            raise UserError(_("Vui lòng cung cấp email người dùng Calendar"))

        try:
            # Thử kết nối với Google Calendar API
            credentials = self._get_credentials()
            service = build('calendar', 'v3', credentials=credentials)

            # Thử lấy danh sách lịch để kiểm tra kết nối
            calendar_list = service.calendarList().list().execute()

            self.write({
                'last_check_date': fields.Datetime.now(),
                'is_valid': True,
                'status_message': _("Kết nối thành công với Google Calendar API")
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Thành công"),
                    'message': _("Kết nối thành công với Google Calendar API"),
                    'sticky': False,
                    'type': 'success',
                }
            }

        except HttpError as error:
            self.write({
                'last_check_date': fields.Datetime.now(),
                'is_valid': False,
                'status_message': _("Lỗi kết nối: %s") % error
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Lỗi"),
                    'message': _("Không thể kết nối với Google Calendar API: %s") % error,
                    'sticky': False,
                    'type': 'danger',
                }
            }
        except Exception as e:
            self.write({
                'last_check_date': fields.Datetime.now(),
                'is_valid': False,
                'status_message': _("Lỗi không xác định: %s") % e
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Lỗi"),
                    'message': _("Lỗi không xác định: %s") % e,
                    'sticky': False,
                    'type': 'danger',
                }
            }

    def _get_credentials(self):
        """Lấy credentials từ Service Account JSON key"""
        self.ensure_one()

        if not self.service_account_json:
            raise ValidationError(_("Không có Service Account JSON key"))

        try:
            service_account_info = json.loads(self.service_account_json)

            credentials = service_account.Credentials.from_service_account_info(
                service_account_info,
                scopes=['https://www.googleapis.com/auth/calendar'],
                subject=self.calendar_user_email
            )

            return credentials
        except Exception as e:
            _logger.error("Lỗi khi tạo credentials: %s", e)
            raise ValidationError(_("Không thể tạo credentials từ Service Account JSON key: %s") % e)

    def create_google_calendar_event(self, summary, description, start_time, end_time, attendees=None, location=None):
        """
        Tạo sự kiện Google Calendar với Google Meet

        Args:
            summary (str): Tiêu đề sự kiện
            description (str): Mô tả sự kiện
            start_time (datetime): Thời gian bắt đầu
            end_time (datetime): Thời gian kết thúc
            attendees (list): Danh sách email người tham dự
            location (str): Địa điểm

        Returns:
            dict: Thông tin sự kiện đã tạo
        """
        self.ensure_one()

        if GOOGLE_API_IMPORT_ERROR:
            raise UserError(_(
                "Không thể kết nối với Google API. Vui lòng cài đặt các thư viện cần thiết:\n"
                "pip install google-api-python-client google-auth google-auth-httplib2\n\n"
                "Chi tiết lỗi: %s") % GOOGLE_API_IMPORT_ERROR
            )

        if not self.is_valid:
            self.action_check_connection()
            if not self.is_valid:
                raise UserError(_("Không thể kết nối với Google Calendar API. Vui lòng kiểm tra cài đặt."))

        try:
            credentials = self._get_credentials()
            service = build('calendar', 'v3', credentials=credentials)

            # Chuẩn bị danh sách người tham dự
            attendee_list = []
            if attendees:
                for email in attendees:
                    if email:  # Chỉ thêm email hợp lệ
                        attendee_list.append({'email': email})

            # Tạo ID duy nhất cho yêu cầu tạo conference
            request_id = str(uuid.uuid4())

            # Chuẩn bị dữ liệu sự kiện
            event = {
                'summary': summary,
                'description': description,
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'Asia/Ho_Chi_Minh',
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'Asia/Ho_Chi_Minh',
                },
                'conferenceData': {
                    'createRequest': {
                        'requestId': request_id,
                        'conferenceSolutionKey': {'type': 'hangoutsMeet'}
                    }
                },
            }

            # Thêm người tham dự nếu có
            if attendee_list:
                event['attendees'] = attendee_list

            # Thêm địa điểm nếu có
            if location:
                event['location'] = location

            # Tạo sự kiện với conferenceDataVersion=1 để tạo Google Meet link
            # Thêm tham số sendUpdates='none' để không gửi email thông báo
            created_event = service.events().insert(
                calendarId='primary',
                body=event,
                conferenceDataVersion=1,
                sendUpdates='none'  # Không gửi email thông báo cho bất kỳ ai
            ).execute()

            return created_event

        except HttpError as error:
            _logger.error("Lỗi khi tạo sự kiện Google Calendar: %s", error)
            raise UserError(_("Không thể tạo sự kiện Google Calendar: %s") % error)
        except Exception as e:
            _logger.error("Lỗi không xác định khi tạo sự kiện Google Calendar: %s", e)
            raise UserError(_("Lỗi không xác định khi tạo sự kiện Google Calendar: %s") % e)

    def get_google_meet_link(self, summary, description, start_time, end_time, attendees=None, location=None):
        """
        Tạo Google Meet link thông qua sự kiện Google Calendar

        Returns:
            str: Google Meet link
        """
        event = self.create_google_calendar_event(
            summary=summary,
            description=description,
            start_time=start_time,
            end_time=end_time,
            attendees=attendees,
            location=location
        )

        # Lấy Google Meet link từ sự kiện đã tạo
        meet_link = event.get('hangoutLink', '')
        if not meet_link:
            # Thử lấy từ conferenceData nếu không có hangoutLink
            conference_data = event.get('conferenceData', {})
            entry_points = conference_data.get('entryPoints', [])
            for entry in entry_points:
                if entry.get('entryPointType') == 'video':
                    meet_link = entry.get('uri', '')
                    break

        return meet_link
