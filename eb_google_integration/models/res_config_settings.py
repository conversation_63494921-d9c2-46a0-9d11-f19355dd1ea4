# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models, _


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'
    
    google_settings_id = fields.Many2one(
        'eb.google.settings',
        string='Cài đặt Google API',
        config_parameter='eb_google_integration.default_google_settings_id',
    )
    google_service_account_json = fields.Text(
        related='google_settings_id.service_account_json',
        readonly=False,
    )
    google_calendar_user_email = fields.Char(
        related='google_settings_id.calendar_user_email',
        readonly=False,
    )
    google_is_valid = fields.Boolean(
        related='google_settings_id.is_valid',
        readonly=True,
    )
    google_status_message = fields.Text(
        related='google_settings_id.status_message',
        readonly=True,
    )
    
    @api.model
    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        
        # Lấy ID cài đặt Google API mặc định
        google_settings_id = int(self.env['ir.config_parameter'].sudo().get_param(
            'eb_google_integration.default_google_settings_id', '0'))
        
        # Nếu không có cài đặt mặc định, tạo mới
        if not google_settings_id:
            google_settings = self.env['eb.google.settings'].sudo().create({
                'name': 'Google API Settings',
            })
            google_settings_id = google_settings.id
            self.env['ir.config_parameter'].sudo().set_param(
                'eb_google_integration.default_google_settings_id', str(google_settings_id))
        
        res.update({
            'google_settings_id': google_settings_id,
        })
        
        return res
    
    def action_check_google_connection(self):
        """Kiểm tra kết nối với Google API"""
        self.ensure_one()
        
        if not self.google_settings_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Lỗi"),
                    'message': _("Không có cài đặt Google API"),
                    'sticky': False,
                    'type': 'danger',
                }
            }
        
        return self.google_settings_id.action_check_connection()
