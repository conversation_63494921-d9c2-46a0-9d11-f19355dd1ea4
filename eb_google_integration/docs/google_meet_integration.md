# Tí<PERSON> hợ<PERSON> Google Meet trong LMS

Tài liệu này hướng dẫn cách cấu hình và sử dụng tính năng tạo Google Meet link tự động trong hệ thống LMS.

## Tổng quan

Tính năng tạo Google Meet link tự động cho phép:
- Tạo link Google Meet thực tế thông qua Google Calendar API
- Tự động thêm người tham dự vào cuộc họp
- Đồng bộ thời gian buổi học với lịch Google Calendar

## Yêu cầu

1. **Service Account từ Google Cloud Platform**
   - Cần tạo một Service Account trong Google Cloud Console
   - Service Account cần có quyền truy cập Google Calendar API
   - Cần tải xuống JSON key của Service Account

2. **Cấu hình Domain-Wide Delegation**
   - Service Account cần được cấp quyền để thay mặt người dùng tạo sự kiện
   - Cần cấu hình Domain-Wide Delegation trong Google Workspace Admin Console

## C<PERSON>ch cấu hình

### 1. Tạo Service Account

1. <PERSON><PERSON><PERSON> cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo một dự án mới hoặc sử dụng dự án hiện có
3. Bật Google Calendar API
4. Tạo Service Account:
   - Vào IAM & Admin > Service Accounts
   - Nhấn "Create Service Account"
   - Đặt tên và mô tả cho Service Account
   - Cấp quyền "Editor" cho Service Account
   - Tạo và tải xuống JSON key

### 2. Cấu hình Domain-Wide Delegation

1. Truy cập [Google Workspace Admin Console](https://admin.google.com/)
2. Vào Security > API Controls > Domain-wide Delegation
3. Nhấn "Add new"
4. Nhập Client ID của Service Account (có trong JSON key)
5. Thêm scope: `https://www.googleapis.com/auth/calendar`

### 3. Cấu hình trong Odoo

1. Cài đặt module `eb_google_integration`
2. Vào Settings > Google Integration
3. Dán nội dung JSON key vào trường "Service Account JSON Key"
4. Nhập email của người dùng mà Service Account sẽ thay mặt để tạo sự kiện
5. Nhấn "Check Connection" để kiểm tra kết nối
6. Lưu cấu hình

## Cách sử dụng

### Tạo buổi học với Google Meet link

1. Vào Courses > Tạo buổi học
2. Chọn loại địa điểm là "Trực tuyến" hoặc "Kết hợp"
3. Chọn phương thức tạo link là "Tự động tạo link"
4. Chọn nền tảng là "Google Meet"
5. Nhấn "Tạo/Làm mới Preview" để xem trước buổi học
6. Nhấn nút tạo link (biểu tượng camera) để tạo link Google Meet
7. Nhấn "Tạo buổi học" để tạo buổi học với link Google Meet

### Tạo link Google Meet cho buổi học đã tồn tại

1. Mở buổi học cần tạo link
2. Chọn loại địa điểm là "Trực tuyến" hoặc "Kết hợp"
3. Chọn nền tảng là "Google Meet"
4. Nhấn nút "Tạo liên kết học trực tuyến"

## Xử lý sự cố

### Link không được tạo

1. Kiểm tra kết nối với Google API trong Settings > Google Integration
2. Kiểm tra JSON key và email người dùng
3. Đảm bảo Service Account có quyền Domain-Wide Delegation
4. Kiểm tra log lỗi trong Odoo

### Lỗi xác thực

1. Kiểm tra JSON key có hợp lệ không
2. Đảm bảo đã cấu hình Domain-Wide Delegation đúng cách
3. Kiểm tra email người dùng có tồn tại trong Google Workspace không

## Lưu ý

- Mỗi lần tạo link Google Meet, một sự kiện mới sẽ được tạo trong Google Calendar
- Người tham dự sẽ nhận được email mời tham gia từ Google Calendar
- Link Google Meet có thể được sử dụng ngay sau khi tạo
