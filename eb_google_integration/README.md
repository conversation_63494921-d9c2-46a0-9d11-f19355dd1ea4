# EarnBase Google Integration

Module này cung cấp tích hợp với Google API cho các module EarnBase.

## Tính năng

- Tích hợp Google Calendar API
- Tạo Google Meet link tự động
- Quản lý Service Account

## Cài đặt

### Yêu cầu

Module này yêu cầu các thư viện Python sau:
- google-api-python-client
- google-auth
- google-auth-httplib2

Cài đặt các thư viện:

```bash
pip install google-api-python-client google-auth google-auth-httplib2
```

### Cấu hình

1. Cài đặt module
2. Vào **Settings > EarnBase > Google Integration**
3. Cấu hình Service Account JSON key

## Hướng dẫn chi tiết tạo Service Account cho Google Calendar API

### 1. Tạo Service Account trong Google Cloud Console

1. **Tạo dự án mới trong Google Cloud Console**:
   - <PERSON><PERSON><PERSON> cậ<PERSON> [Google Cloud Console](https://console.cloud.google.com)
   - <PERSON><PERSON><PERSON><PERSON> vào "Tạo dự án" (Create Project)
   - Đặt tên cho dự án và nhấp "Tạo"

2. **Bật các API cần thiết**:
   - Trong dự án vừa tạo, đi đến "API & Services" > "Library"
   - Tìm và bật "Google Calendar API"
   - Tìm và bật "Google Meet API" (nếu có)

3. **Cấu hình màn hình chấp thuận OAuth**:
   - Đi đến "API & Services" > "OAuth consent screen"
   - Chọn "Internal" cho User Type (nếu bạn đang sử dụng Google Workspace)
   - Điền thông tin cần thiết như tên ứng dụng, email hỗ trợ, và thông tin liên hệ
   - Nhấp "Save and Continue" qua các bước

4. **Tạo Service Account**:
   - Đi đến "API & Services" > "Credentials"
   - Nhấp "Create Credentials" > "Service account"
   - Nhập tên cho service account
   - Nhấp "Create and Continue" > "Done"

5. **Tạo khóa cho Service Account**:
   - Trong danh sách service accounts, nhấp vào service account vừa tạo
   - Chọn tab "Keys"
   - Nhấp "Add Key" > "Create new key"
   - Chọn định dạng "JSON" và nhấp "Create"
   - File JSON chứa khóa riêng tư sẽ được tải xuống máy tính của bạn
   - **Lưu ý**: Đây là lần duy nhất bạn có thể tải xuống khóa riêng tư, hãy lưu trữ nó an toàn

6. **Lấy Client ID của Service Account**:
   - Trong trang chi tiết của service account, mở rộng "Advanced settings"
   - Sao chép "Client ID" (đây là một chuỗi số dài)

### 2. Cấu hình Domain-Wide Delegation trong Google Workspace Admin Console

1. **Đăng nhập vào Google Workspace Admin Console**:
   - Truy cập [admin.google.com](https://admin.google.com) với tài khoản Super Admin

2. **Cấu hình Domain-Wide Delegation**:
   - Đi đến Menu > Security > Access and data control > API controls
   - Nhấp vào "Manage Domain Wide Delegation"
   - Nhấp "Add new"
   - Nhập Client ID của service account vào trường "Client ID"
   - Trong trường "OAuth Scopes", thêm các scopes cần thiết:
     - `https://www.googleapis.com/auth/calendar` (để truy cập Google Calendar)
     - `https://www.googleapis.com/auth/calendar.events` (để tạo sự kiện)
   - Nhấp "Authorize"

3. **Xác minh cấu hình**:
   - Sau khi thêm, kiểm tra lại bằng cách di chuột qua Client ID và nhấp "View details"
   - Đảm bảo tất cả các scopes đã được liệt kê

### 3. Cấu hình trong Odoo

1. **Cài đặt module eb_google_integration**:
   - Đảm bảo module đã được cài đặt trong hệ thống Odoo

2. **Cấu hình Google API Settings**:
   - Đi đến Settings > Google Integration
   - Dán nội dung file JSON key vào trường "Service Account JSON Key"
   - Nhập email của người dùng mà Service Account sẽ thay mặt để tạo sự kiện vào trường "Email người dùng Calendar"
   - Nhấp "Check Connection" để kiểm tra kết nối
   - Lưu cấu hình

### Lưu ý quan trọng

1. **Bảo mật**:
   - Lưu trữ file JSON key một cách an toàn
   - Chỉ cấp quyền tối thiểu cần thiết cho service account
   - Định kỳ kiểm tra và xóa các service account không còn sử dụng

2. **Thời gian áp dụng**:
   - Sau khi cấu hình Domain-Wide Delegation, có thể mất đến 24 giờ để thay đổi có hiệu lực trên tất cả tài khoản người dùng, nhưng thường nhanh hơn

3. **Xử lý lỗi**:
   - Hệ thống có cơ chế fallback để tạo link giả lập nếu không thể kết nối với Google API
   - Kiểm tra logs trong Odoo nếu gặp vấn đề

4. **Yêu cầu về email**:
   - Email người dùng Calendar phải là người dùng hợp lệ trong tổ chức Google Workspace của bạn
   - Service account không phải là thành viên của tổ chức Google Workspace và không bị ảnh hưởng bởi các chính sách của tổ chức

5. **Xử lý khi gặp chính sách `iam.disableServiceAccountKeyCreation`**:
   - Nhiều tổ chức áp dụng chính sách `iam.disableServiceAccountKeyCreation` để ngăn việc tạo khóa Service Account nhằm tăng cường bảo mật
   - Nếu bạn gặp lỗi "Service account key creation is disabled by the Organization Policy", bạn cần liên hệ với quản trị viên có quyền "Organization Policy Administrator" (roles/orgpolicy.policyAdmin) để tạm thời vô hiệu hóa chính sách này
   - Thay vì sử dụng Service Account key, bạn có thể xem xét các phương pháp thay thế an toàn hơn:

### Phương pháp thay thế cho Service Account Key

1. **Sử dụng Workload Identity Federation** (khuyến nghị):
   - Cho phép ứng dụng bên ngoài Google Cloud xác thực với Google API mà không cần Service Account key
   - Tham khảo: [Workload Identity Federation](https://cloud.google.com/iam/docs/workload-identity-federation)

2. **Sử dụng Google Cloud VM với Service Account đính kèm**:
   - Nếu ứng dụng của bạn chạy trên Google Cloud VM, bạn có thể đính kèm Service Account vào VM
   - VM sẽ tự động nhận được thông tin xác thực tạm thời mà không cần key

3. **Sử dụng Google Cloud Run với Service Account**:
   - Tương tự như VM, bạn có thể chạy ứng dụng trên Cloud Run với Service Account được chỉ định

4. **Sử dụng Temporary Service Account Credentials**:
   - Tạo thông tin xác thực tạm thời với thời gian sống ngắn thay vì sử dụng key dài hạn

## Sử dụng

Module này được sử dụng bởi các module khác như eb_lms để tạo Google Meet link tự động.

## Hỗ trợ

Liên hệ: <EMAIL>
