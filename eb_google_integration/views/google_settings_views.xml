<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_google_settings_form" model="ir.ui.view">
        <field name="name">eb.google.settings.form</field>
        <field name="model">eb.google.settings</field>
        <field name="arch" type="xml">
            <form string="Google API Settings">
                <header>
                    <button name="action_check_connection" string="Check Connection" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active"/>
                            <field name="calendar_user_email" placeholder="<EMAIL>"/>
                        </group>
                        <group>
                            <field name="is_valid"/>
                            <field name="last_check_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Service Account" name="service_account">
                            <group>
                                <field name="service_account_json" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                            <group>
                                <field name="service_account_email"/>
                                <field name="service_account_project_id"/>
                            </group>
                        </page>
                        <page string="Status" name="status">
                            <group>
                                <field name="status_message"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_google_settings_list" model="ir.ui.view">
        <field name="name">eb.google.settings.list</field>
        <field name="model">eb.google.settings</field>
        <field name="arch" type="xml">
            <list string="Google API Settings">
                <field name="name"/>
                <field name="service_account_email"/>
                <field name="calendar_user_email"/>
                <field name="is_valid"/>
                <field name="last_check_date"/>
            </list>
        </field>
    </record>

    <!-- Menu items and actions moved to res_config_settings_views.xml -->
</odoo>
