# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Instructor Leave Management Schemas

Schemas for instructor leave request management including:
- Leave request creation and updates
- Leave balance information
- Leave request responses
"""

from typing import List, Optional
from datetime import date, datetime
from pydantic import BaseModel, Field, field_validator

from odoo.addons.eb_lms.schemas.shared.base_schemas import LMSResponseBase, LMSListResponse


class LeaveRequestCreate(BaseModel):
    """Leave request creation schema."""
    start_date: date = Field(
        ...,
        description="<PERSON>ày bắt đầu nghỉ",
        examples=["2025-07-20", "2025-08-01", "2025-12-25"]
    )
    end_date: date = Field(
        ...,
        description="<PERSON><PERSON><PERSON> kế<PERSON> thúc nghỉ",
        examples=["2025-07-21", "2025-08-03", "2025-12-26"]
    )
    leave_type: str = Field(
        "full_day",
        description="Loại nghỉ",
        examples=["full_day", "half_day", "hours"]
    )
    half_day_period: Optional[str] = Field(
        None,
        description="Buổi nghỉ (nếu nghỉ nửa ngày)",
        examples=["morning", "afternoon"]
    )
    time_from: Optional[float] = Field(
        None,
        description="Từ giờ (nếu nghỉ theo giờ)",
        examples=[8.0, 9.5, 14.0]
    )
    time_to: Optional[float] = Field(
        None,
        description="Đến giờ (nếu nghỉ theo giờ)",
        examples=[12.0, 17.5, 18.0]
    )
    reason_type: str = Field(
        "personal",
        description="Loại lý do",
        examples=["sick", "personal", "family", "emergency", "vacation"]
    )
    reason: str = Field(
        ...,
        min_length=10,
        max_length=1000,
        description="Lý do nghỉ chi tiết",
        examples=[
            "Cần nghỉ để chăm sóc con nhỏ bị ốm",
            "Có việc gia đình cấp bách cần xử lý",
            "Đi khám sức khỏe định kỳ"
        ]
    )

    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v, info):
        """Validate end date is after start date."""
        if 'start_date' in info.data and v < info.data['start_date']:
            raise ValueError("Ngày kết thúc phải sau ngày bắt đầu")
        return v

    @field_validator('start_date')
    @classmethod
    def validate_start_date(cls, v):
        """Validate start date is in the future."""
        if v <= date.today():
            raise ValueError("Ngày bắt đầu phải trong tương lai")
        return v


class LeaveRequestUpdate(BaseModel):
    """Leave request update schema."""
    start_date: Optional[date] = Field(None, description="Ngày bắt đầu nghỉ")
    end_date: Optional[date] = Field(None, description="Ngày kết thúc nghỉ")
    leave_type: Optional[str] = Field(None, description="Loại nghỉ")
    half_day_period: Optional[str] = Field(None, description="Buổi nghỉ")
    time_from: Optional[float] = Field(None, description="Từ giờ")
    time_to: Optional[float] = Field(None, description="Đến giờ")
    reason_type: Optional[str] = Field(None, description="Loại lý do")
    reason: Optional[str] = Field(None, min_length=10, max_length=1000, description="Lý do nghỉ")


class LeaveRequestInfo(BaseModel):
    """Leave request information schema."""
    id: int = Field(..., description="ID yêu cầu nghỉ")
    code: str = Field(..., description="Mã yêu cầu nghỉ")
    name: str = Field(..., description="Tên yêu cầu nghỉ")
    instructor_id: int = Field(..., description="ID giảng viên")
    instructor_name: str = Field(..., description="Tên giảng viên")
    request_date: date = Field(..., description="Ngày tạo yêu cầu")
    start_date: date = Field(..., description="Ngày bắt đầu nghỉ")
    end_date: date = Field(..., description="Ngày kết thúc nghỉ")
    leave_type: str = Field(..., description="Loại nghỉ")
    half_day_period: Optional[str] = Field(None, description="Buổi nghỉ")
    time_from: Optional[float] = Field(None, description="Từ giờ")
    time_to: Optional[float] = Field(None, description="Đến giờ")
    reason_type: str = Field(..., description="Loại lý do")
    reason: str = Field(..., description="Lý do nghỉ")
    state: str = Field(..., description="Trạng thái", examples=["draft", "submitted", "approved", "rejected"])
    approver_id: Optional[int] = Field(None, description="ID người phê duyệt")
    approver_name: Optional[str] = Field(None, description="Tên người phê duyệt")
    approval_date: Optional[date] = Field(None, description="Ngày phê duyệt")
    rejection_reason: Optional[str] = Field(None, description="Lý do từ chối")
    affected_lesson_count: int = Field(0, description="Số buổi học bị ảnh hưởng")
    makeup_lesson_count: int = Field(0, description="Số buổi học bù")
    created_at: Optional[datetime] = Field(None, description="Ngày tạo")
    updated_at: Optional[datetime] = Field(None, description="Ngày cập nhật")


class LeaveBalance(BaseModel):
    """Leave balance information schema."""
    instructor_id: int = Field(..., description="ID giảng viên")
    instructor_name: str = Field(..., description="Tên giảng viên")
    annual_leave_total: int = Field(12, description="Tổng số ngày nghỉ phép năm")
    annual_leave_used: int = Field(0, description="Số ngày nghỉ phép đã sử dụng")
    annual_leave_remaining: int = Field(12, description="Số ngày nghỉ phép còn lại")
    sick_leave_used: int = Field(0, description="Số ngày nghỉ ốm đã sử dụng")
    personal_leave_used: int = Field(0, description="Số ngày nghỉ việc riêng đã sử dụng")
    total_leave_days_this_year: int = Field(0, description="Tổng số ngày nghỉ năm nay")
    pending_requests: int = Field(0, description="Số yêu cầu đang chờ duyệt")
    approved_requests: int = Field(0, description="Số yêu cầu đã được duyệt")


# Response schemas
class LeaveRequestResponse(LMSResponseBase[LeaveRequestInfo]):
    """Single leave request response."""

    @classmethod
    def create_leave_request_response(cls, leave_request: LeaveRequestInfo, message: str = "Thông tin yêu cầu nghỉ"):
        return cls.success_response(data=leave_request, message=message)


class LeaveRequestListResponse(LMSListResponse[LeaveRequestInfo]):
    """Leave request list response."""

    @classmethod
    def create_leave_request_list_response(
        cls,
        leave_requests: List[LeaveRequestInfo],
        page: int = 1,
        page_size: int = 20,
        total_count: int = 0
    ):
        return cls.create_list_response(
            items=leave_requests,
            page=page,
            page_size=page_size,
            total_count=total_count,
            message=f"Tìm thấy {total_count} yêu cầu nghỉ"
        )


class LeaveBalanceResponse(LMSResponseBase[LeaveBalance]):
    """Leave balance response."""

    @classmethod
    def create_leave_balance_response(cls, balance: LeaveBalance):
        return cls.success_response(
            data=balance,
            message="Thông tin số ngày nghỉ còn lại"
        )


class LeaveRequestCreateResponse(LMSResponseBase[LeaveRequestInfo]):
    """Leave request creation response."""

    @classmethod
    def create_leave_request_created_response(cls, leave_request: LeaveRequestInfo):
        return cls.success_response(
            data=leave_request,
            message="Tạo yêu cầu nghỉ thành công"
        )


class LeaveRequestUpdateResponse(LMSResponseBase[LeaveRequestInfo]):
    """Leave request update response."""

    @classmethod
    def create_leave_request_updated_response(cls, leave_request: LeaveRequestInfo):
        return cls.success_response(
            data=leave_request,
            message="Cập nhật yêu cầu nghỉ thành công"
        )


class LeaveRequestDeleteResponse(LMSResponseBase[dict]):
    """Leave request deletion response."""

    @classmethod
    def create_leave_request_deleted_response(cls, request_id: int):
        return cls.success_response(
            data={"deleted_id": request_id},
            message="Hủy yêu cầu nghỉ thành công"
        )
