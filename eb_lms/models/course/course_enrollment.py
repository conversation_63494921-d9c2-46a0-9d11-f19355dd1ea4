# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from datetime import timedelta
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import random
import string
import logging

_logger = logging.getLogger(__name__)


class CourseEnrollment(models.Model):
    """
    Đăng ký khóa học.

    Lưu trữ thông tin đăng ký khóa học của học viên. Hỗ trợ cả đăng ký trực tiếp và đăng ký từ website.
    """

    _name = "eb.course.enrollment"
    _description = "Course Enrollment"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _rec_name = "name"  # Thay đổi từ student_id sang name
    _order = "enrollment_date desc, id desc"

    # ===== SMS SUPPORT METHODS =====
    def _phone_get_number_fields(self):
        """Return phone fields for SMS functionality"""
        return ['visitor_phone']  # Use visitor_phone for direct phone access

    def _mail_get_partner_fields(self, introspect_fields=False):
        """Return partner fields for SMS functionality"""
        return ['student_id']  # Student is linked to partner through user_id

    # Thông tin cơ bản hiển thị
    name = fields.Char(
        string="Tên đăng ký",
        compute="_compute_name",
        store=True,
        help="Display name for the enrollment",
    )

    # Thông tin khách/học viên
    student_id = fields.Many2one(
        "eb.student.student",
        string="Học viên",
        required=False,  # Thay đổi thành không bắt buộc
        tracking=True,
        help="Student enrolled in the course, may be empty for website enrollments",
    )
    is_website_enrollment = fields.Boolean(
        string="Đăng ký từ website",
        default=False,
        help="True if this enrollment was created from website",
    )

    # Thông tin tạm thời khi đăng ký từ website
    visitor_name = fields.Char(
        string="Tên người đăng ký", tracking=True, help="Name of the website visitor"
    )
    visitor_email = fields.Char(
        string="Email", tracking=True, help="Email of the website visitor"
    )
    visitor_phone = fields.Char(
        string="Số điện thoại",
        tracking=True,
        help="Phone number of the website visitor",
    )

    # Liên kết với partner (khách hàng)
    partner_id = fields.Many2one(
        "res.partner",
        string="Khách hàng",
        tracking=True,
        help="Partner/Customer associated with this enrollment",
    )

    # Liên kết với đơn hàng
    sale_order_id = fields.Many2one(
        "sale.order",
        string="Đơn hàng",
        tracking=True,
        readonly=True,
        help="Đơn hàng liên kết với đăng ký khóa học",
    )

    # Liên kết với cơ hội
    opportunity_id = fields.Many2one(
        "crm.lead",
        string="Cơ hội",
        tracking=True,
        readonly=True,
        domain=[("type", "=", "opportunity")],
        help="Cơ hội liên kết với đăng ký khóa học",
    )

    # Các trường khác
    course_id = fields.Many2one(
        "eb.course.course",
        string="Khóa học",
        required=True,
        tracking=True,
        help="Khóa học được đăng ký",
    )
    company_id = fields.Many2one(
        "res.company",
        string="Công ty",
        default=lambda self: self.env.company,
        help="Công ty quản lý đăng ký",
    )
    enrollment_date = fields.Datetime(
        string="Ngày đăng ký",
        default=fields.Datetime.now,
        required=True,
        tracking=True,
        help="Ngày giờ đăng ký",
    )
    enrollment_number = fields.Char(
        string="Mã đăng ký",
        readonly=True,
        copy=False,
        help="Mã đăng ký duy nhất",
    )
    state = fields.Selection(
        [
            ("unpaid", "Chưa thanh toán"),
            ("partial", "Thanh toán một phần"),
            ("paid", "Đã thanh toán"),
            ("refunded", "Đã hoàn tiền"),
            ("cancelled", "Đã hủy"),
        ],
        string="Trạng thái",
        default="unpaid",
        tracking=True,
        required=True,
        help="Trạng thái của đăng ký",
    )
    payment_ids = fields.One2many(
        "eb.student.payment",
        "enrollment_id",
        string="Các khoản thanh toán",
        help="Các khoản thanh toán cho đăng ký này",
    )
    refund_request_ids = fields.One2many(
        "eb.refund.request",
        "enrollment_id",
        string="Yêu cầu hoàn phí",
        help="Các yêu cầu hoàn phí liên quan đến đăng ký này",
    )
    refund_request_count = fields.Integer(
        string="Số yêu cầu hoàn phí",
        compute="_compute_refund_request_count",
        store=True,
        help="Số lượng yêu cầu hoàn phí liên quan đến đăng ký này",
    )
    notes = fields.Text(
        string="Ghi chú",
        help="Ghi chú bổ sung về đăng ký",
    )
    reminder_sent = fields.Boolean(
        string="Đã gửi nhắc nhở",
        default=False,
        help="Đánh dấu đã gửi email nhắc nhở hoàn tất đăng ký"
    )
    confirm_date = fields.Datetime(
        string="Ngày xác nhận",
        readonly=True,
        tracking=True,
        help="Ngày giờ xác nhận đăng ký",
    )
    completion_date = fields.Datetime(
        string="Ngày hoàn thành",
        readonly=True,
        tracking=True,
        help="Ngày giờ hoàn thành khóa học",
    )
    cancellation_date = fields.Datetime(
        string="Ngày hủy",
        readonly=True,
        tracking=True,
        help="Ngày giờ hủy đăng ký",
    )
    cancellation_reason = fields.Text(
        string="Lý do hủy",
        help="Lý do hủy đăng ký",
    )
    currency_id = fields.Many2one(
        "res.currency",
        string="Tiền tệ",
        default=lambda self: self.env.company.currency_id.id,
        help="Tiền tệ sử dụng cho đăng ký khóa học",
    )
    payment_amount = fields.Monetary(
        string="Số tiền cần thanh toán",
        currency_field="currency_id",
        help="Tổng số tiền cần thanh toán",
    )
    paid_amount = fields.Monetary(
        string="Số tiền đã thanh toán",
        compute="_compute_paid_amount",
        currency_field="currency_id",
        store=True,
        help="Số tiền đã thanh toán",
    )
    balance_amount = fields.Monetary(
        string="Số tiền còn lại",
        compute="_compute_balance_amount",
        currency_field="currency_id",
        store=True,
        help="Số tiền còn phải thanh toán",
    )
    amount_total = fields.Monetary(
        string="Tổng số tiền",
        related="payment_amount",
        currency_field="currency_id",
        store=False,
        help="Tổng số tiền cần thanh toán cho đăng ký này",
    )
    class_id = fields.Many2one(
        "eb.class.class",
        string="Lớp học được phân",
        readonly=True,
        tracking=True,
        help="Lớp học đã được phân cho học viên sau khi xác nhận đăng ký",
    )
    selected_location_id = fields.Many2one(
        "eb.facility.location",
        string="Địa điểm được chọn",
        tracking=True,
        help="Địa điểm học mà học viên đã chọn khi đăng ký",
    )
    location_name = fields.Char(
        string="Tên địa điểm",
        related="selected_location_id.name",
        store=True,
        help="Tên địa điểm được chọn",
    )
    active = fields.Boolean(
        string="Hoạt động",
        default=True,
        help="Nếu không được chọn, đăng ký sẽ bị ẩn khỏi các view",
    )

    # Progress tracking fields - computed
    progress_percentage = fields.Float(
        string="Tiến độ (%)",
        compute="_compute_progress_percentage",
        store=True,
        help="Phần trăm tiến độ hoàn thành khóa học",
    )
    lessons_completed = fields.Integer(
        string="Số bài học đã hoàn thành",
        compute="_compute_lessons_completed",
        store=True,
        help="Số lượng bài học đã hoàn thành",
    )
    current_grade = fields.Float(
        string="Điểm hiện tại",
        compute="_compute_current_grade",
        store=True,
        help="Điểm số hiện tại của học viên",
    )
    average_score = fields.Float(
        string="Điểm trung bình",
        compute="_compute_average_score",
        store=True,
        help="Điểm trung bình của học viên",
    )
    attendance_rate = fields.Float(
        string="Tỷ lệ điểm danh (%)",
        compute="_compute_attendance_rate",
        store=True,
        help="Tỷ lệ điểm danh của học viên",
    )

    # Đã xóa ràng buộc độc nhất student_course_unique để cho phép nhiều lần hoàn tiền
    # cho cùng một học viên và khóa học
    _sql_constraints = []

    @api.depends("student_id", "visitor_name", "course_id", "selected_location_id")
    def _compute_name(self):
        """Tính toán tên hiển thị cho đăng ký."""
        for record in self:
            base_name = ""
            if record.student_id:
                base_name = f"{record.student_id.name} - {record.course_id.name}"
            elif record.visitor_name:
                base_name = f"{record.visitor_name} - {record.course_id.name}"
            else:
                base_name = f"Đăng ký mới - {record.course_id.name}"

            # Thêm thông tin địa điểm nếu có
            if record.selected_location_id:
                base_name += f" ({record.selected_location_id.name})"

            record.name = base_name

    @api.constrains('class_id', 'selected_location_id')
    def _check_location_class_consistency(self):
        """Kiểm tra tính nhất quán giữa địa điểm được chọn và lớp học."""
        for record in self:
            if record.class_id and record.selected_location_id:
                # Kiểm tra lớp học có thuộc địa điểm đã chọn không
                if record.class_id.expected_location_id != record.selected_location_id:
                    raise ValidationError(_(
                        "Lớp học '%s' không thuộc địa điểm '%s' đã chọn. "
                        "Lớp học này thuộc địa điểm '%s'."
                    ) % (
                        record.class_id.name,
                        record.selected_location_id.name,
                        record.class_id.expected_location_id.name if record.class_id.expected_location_id else "Chưa xác định"
                    ))

    @api.constrains('selected_location_id')
    def _check_location_availability(self):
        """Kiểm tra địa điểm có khả dụng không."""
        for record in self:
            if record.selected_location_id:
                if not record.selected_location_id.active:
                    raise ValidationError(_(
                        "Địa điểm '%s' đã chọn không còn hoạt động."
                    ) % record.selected_location_id.name)

                # Kiểm tra địa điểm có thuộc khóa học không
                if record.course_id:
                    available_locations = record.course_id.get_available_locations()
                    if record.selected_location_id not in available_locations:
                        raise ValidationError(_(
                            "Địa điểm '%s' không khả dụng cho khóa học '%s'."
                        ) % (record.selected_location_id.name, record.course_id.name))

    @api.ondelete(at_uninstall=False)
    def _unlink_except_paid_enrollment(self):
        """Ngăn chặn việc xóa đăng ký đã thanh toán hoặc đang trong quá trình thanh toán."""
        for enrollment in self:
            if enrollment.state in ['paid', 'partial']:
                raise ValidationError(_(
                    "Không thể xóa đăng ký đã thanh toán hoặc đang trong quá trình thanh toán. "
                    "Vui lòng sử dụng chức năng 'Hủy đăng ký' thay vì xóa."
                ))
            if enrollment.sale_order_id and enrollment.sale_order_id.state not in ['cancel', 'draft']:
                raise ValidationError(_(
                    "Không thể xóa đăng ký đã có đơn hàng được xác nhận. "
                    "Vui lòng sử dụng chức năng 'Hủy đăng ký' thay vì xóa."
                ))
            if enrollment.payment_ids.filtered(lambda p: p.state not in ['unpaid', 'cancelled']):
                raise ValidationError(_(
                    "Không thể xóa đăng ký đã có khoản thanh toán. "
                    "Vui lòng sử dụng chức năng 'Hủy đăng ký' thay vì xóa."
                ))

    @api.model_create_multi
    def create(self, vals_list):
        """Override to handle enrollment creation."""
        enrollments = super(CourseEnrollment, self).create(vals_list)

        # Kiểm tra và tự động chuyển trạng thái khóa học khi đạt số lượng học viên tối thiểu
        for enrollment in enrollments:
            enrollment._check_course_minimum_students()

            # Tự động thêm học viên vào lớp nếu có class_id và student_id
            enrollment._auto_assign_to_class()



            # Note: Instructor new student notification has been removed as it was redundant
            # Instructors can see new students in their dashboard and class management views

            # Gửi SMS xác nhận đăng ký nếu có số điện thoại và SMS được bật
            if enrollment.student_id and (enrollment.student_id.mobile or enrollment.student_id.phone):
                try:
                    sms_utils = self.env['eb.lms.sms.utils']
                    sms_utils.send_enrollment_confirmation_sms(enrollment.id)
                except Exception as e:
                    _logger.error("Lỗi khi gửi SMS xác nhận đăng ký cho enrollment %s: %s", enrollment.id, e)

        return enrollments

    @api.depends('class_id.lesson_ids')
    def _compute_progress_percentage(self):
        """Tính toán phần trăm tiến độ hoàn thành"""
        for record in self:
            if not record.class_id or not record.student_id:
                record.progress_percentage = 0.0
                continue

            total_lessons = len(record.class_id.lesson_ids)
            if total_lessons == 0:
                record.progress_percentage = 0.0
                continue

            # Count attended lessons for this student
            attended_lessons = self.env["eb.attendance.attendance"].search_count([
                ('user_id', '=', record.student_id.user_id.id),
                ('lesson_id', 'in', record.class_id.lesson_ids.ids),
                ('status', '=', 'present'),
                ('role', '=', 'student')
            ])

            record.progress_percentage = (attended_lessons / total_lessons) * 100

    @api.depends('class_id.lesson_ids')
    def _compute_lessons_completed(self):
        """Tính toán số bài học đã hoàn thành"""
        for record in self:
            if not record.class_id or not record.student_id:
                record.lessons_completed = 0
                continue

            # Count attended lessons for this student
            record.lessons_completed = self.env["eb.attendance.attendance"].search_count([
                ('user_id', '=', record.student_id.user_id.id),
                ('lesson_id', 'in', record.class_id.lesson_ids.ids),
                ('status', '=', 'present'),
                ('role', '=', 'student')
            ])

    @api.depends('student_id', 'class_id')
    def _compute_current_grade(self):
        """Tính toán điểm hiện tại dựa trên kết quả đánh giá thực tế"""
        for record in self:
            if not record.student_id or not record.class_id:
                record.current_grade = 0.0
                continue

            # Lấy tất cả kết quả đánh giá của học viên trong lớp này
            grade_results = self.env['eb.grade.result'].search([
                ('student_id', '=', record.student_id.id),
                ('class_id', '=', record.class_id.id),
                ('status', '=', 'graded'),
                ('is_published', '=', True),
            ])

            if not grade_results:
                # Nếu chưa có kết quả đánh giá, tính dựa trên attendance rate
                if record.attendance_rate >= 90:
                    record.current_grade = 9.0
                elif record.attendance_rate >= 80:
                    record.current_grade = 8.0
                elif record.attendance_rate >= 70:
                    record.current_grade = 7.0
                elif record.attendance_rate >= 60:
                    record.current_grade = 6.0
                else:
                    record.current_grade = 5.0
            else:
                # Tính điểm trung bình có trọng số từ các đánh giá
                total_weighted_score = 0.0
                total_weight = 0.0

                for result in grade_results:
                    weight = result.weight or 1.0
                    # Chuyển điểm về thang 10
                    normalized_score = (result.final_score / result.max_score) * 10 if result.max_score > 0 else 0.0
                    total_weighted_score += normalized_score * weight
                    total_weight += weight

                if total_weight > 0:
                    record.current_grade = total_weighted_score / total_weight
                else:
                    record.current_grade = 0.0

    @api.depends('student_id', 'class_id')
    def _compute_average_score(self):
        """Tính toán điểm trung bình từ tất cả các đánh giá"""
        for record in self:
            if not record.student_id or not record.class_id:
                record.average_score = 0.0
                continue

            # Lấy tất cả kết quả đánh giá của học viên trong lớp này
            grade_results = self.env['eb.grade.result'].search([
                ('student_id', '=', record.student_id.id),
                ('class_id', '=', record.class_id.id),
                ('status', '=', 'graded'),
                ('is_published', '=', True),
            ])

            if not grade_results:
                record.average_score = record.current_grade
            else:
                # Tính điểm trung bình đơn giản (không có trọng số)
                total_score = 0.0
                count = 0

                for result in grade_results:
                    # Chuyển điểm về thang 10
                    normalized_score = (result.final_score / result.max_score) * 10 if result.max_score > 0 else 0.0
                    total_score += normalized_score
                    count += 1

                if count > 0:
                    record.average_score = total_score / count
                else:
                    record.average_score = 0.0

    @api.depends('class_id.lesson_ids')
    def _compute_attendance_rate(self):
        """Tính toán tỷ lệ điểm danh"""
        for record in self:
            if not record.class_id or not record.student_id:
                record.attendance_rate = 0.0
                continue

            # Get lessons that have been conducted (past lessons)
            conducted_lessons = record.class_id.lesson_ids.filtered(
                lambda l: l.start_datetime and l.start_datetime <= fields.Datetime.now()
            )

            if not conducted_lessons:
                record.attendance_rate = 0.0
                continue

            # Count attended lessons
            attended_lessons = self.env["eb.attendance.attendance"].search_count([
                ('user_id', '=', record.student_id.user_id.id),
                ('lesson_id', 'in', conducted_lessons.ids),
                ('status', '=', 'present'),
                ('role', '=', 'student')
            ])

            record.attendance_rate = (attended_lessons / len(conducted_lessons)) * 100

    def get_assessment_details(self):
        """Lấy chi tiết các đánh giá của học viên"""
        self.ensure_one()

        if not self.student_id or not self.class_id:
            return []

        # Lấy tất cả kết quả đánh giá của học viên trong lớp này
        grade_results = self.env['eb.grade.result'].search([
            ('student_id', '=', self.student_id.id),
            ('class_id', '=', self.class_id.id),
            ('is_published', '=', True),
        ], order='create_date desc')

        assessment_details = []
        for result in grade_results:
            assessment = result.assessment_id
            # Chuyển điểm về thang 10
            normalized_score = (result.final_score / result.max_score) * 10 if result.max_score > 0 else 0.0

            assessment_details.append({
                'id': result.id,
                'assessment_id': assessment.id,
                'assessment_name': assessment.name,
                'assessment_type': assessment.assessment_type,
                'assessment_date': assessment.assessment_date,
                'score': result.score,
                'max_score': result.max_score,
                'final_score': result.final_score,
                'normalized_score': normalized_score,  # Điểm trên thang 10
                'bonus_score': result.bonus_score,
                'score_percentage': result.score_percentage,
                'is_passed': result.is_passed,
                'weight': result.weight,
                'weighted_score': result.weighted_score,
                'status': result.status,
                'submission_date': result.submission_date,
                'graded_date': result.graded_date,
                'feedback': result.feedback,
                'attempt_number': result.attempt_number,
            })

        return assessment_details

    def get_grade_summary(self):
        """Lấy tóm tắt điểm số của học viên"""
        self.ensure_one()

        assessment_details = self.get_assessment_details()

        if not assessment_details:
            return {
                'total_assessments': 0,
                'completed_assessments': 0,
                'passed_assessments': 0,
                'current_grade': self.current_grade,
                'average_score': self.average_score,
                'pass_rate': 0.0,
                'grade_breakdown': [],
            }

        # Thống kê tổng quan
        total_assessments = len(assessment_details)
        completed_assessments = len([a for a in assessment_details if a['status'] == 'graded'])
        passed_assessments = len([a for a in assessment_details if a['is_passed']])
        pass_rate = (passed_assessments / completed_assessments * 100) if completed_assessments > 0 else 0.0

        # Phân loại theo loại đánh giá
        grade_breakdown = {}
        for detail in assessment_details:
            assessment_type = detail['assessment_type']
            if assessment_type not in grade_breakdown:
                grade_breakdown[assessment_type] = {
                    'type': assessment_type,
                    'count': 0,
                    'total_score': 0.0,
                    'average_score': 0.0,
                    'passed_count': 0,
                }

            breakdown = grade_breakdown[assessment_type]
            breakdown['count'] += 1
            breakdown['total_score'] += detail['normalized_score']
            if detail['is_passed']:
                breakdown['passed_count'] += 1

        # Tính điểm trung bình cho từng loại
        for breakdown in grade_breakdown.values():
            if breakdown['count'] > 0:
                breakdown['average_score'] = breakdown['total_score'] / breakdown['count']
                breakdown['pass_rate'] = (breakdown['passed_count'] / breakdown['count']) * 100

        return {
            'total_assessments': total_assessments,
            'completed_assessments': completed_assessments,
            'passed_assessments': passed_assessments,
            'current_grade': self.current_grade,
            'average_score': self.average_score,
            'pass_rate': pass_rate,
            'grade_breakdown': list(grade_breakdown.values()),
        }

    def write(self, vals):
        """Override to handle enrollment state changes."""
        result = super(CourseEnrollment, self).write(vals)

        # Kiểm tra và tự động chuyển trạng thái khóa học khi trạng thái đăng ký thay đổi
        if 'state' in vals:
            for enrollment in self:
                enrollment._check_course_minimum_students()

        # Tự động thêm học viên vào lớp khi có thay đổi về student_id hoặc class_id
        if 'student_id' in vals or 'class_id' in vals:
            for enrollment in self:
                enrollment._auto_assign_to_class()

        return result

    def _check_course_minimum_students(self):
        """Kiểm tra và tự động chuyển trạng thái khóa học khi đạt số lượng học viên tối thiểu."""
        self.ensure_one()
        course = self.course_id

        # Chỉ kiểm tra nếu khóa học đang ở trạng thái "Mở đăng ký"
        if not course.is_enrollment_open:
            return

        # Chỉ kiểm tra nếu có thiết lập số lượng học viên tối thiểu
        if course.min_students <= 0:
            return

        # Đếm số lượng đăng ký hợp lệ (không bao gồm đã hủy hoặc hoàn tiền)
        valid_enrollment_count = self.env["eb.course.enrollment"].search_count([
            ("course_id", "=", course.id),
            ("state", "not in", ["cancelled", "refunded"]),
        ])

        # Nếu đạt số lượng học viên tối thiểu, tự động chuyển sang trạng thái "Đang diễn ra"
        if valid_enrollment_count >= course.min_students:
            in_progress_stage = self.env.ref("eb_lms.course_stage_in_progress", raise_if_not_found=False)
            if in_progress_stage and course.stage_id != in_progress_stage:
                course.write({"stage_id": in_progress_stage.id})
                course.message_post(
                    body=_("Khóa học đã tự động chuyển sang trạng thái 'Đang diễn ra' vì đã đạt số lượng học viên tối thiểu (%s/%s).")
                    % (valid_enrollment_count, course.min_students)
                )

    def _auto_assign_to_class(self):
        """Tự động thêm học viên vào lớp nếu có class_id và student_id."""
        self.ensure_one()

        # Chỉ thực hiện nếu có cả class_id và student_id
        if not self.class_id or not self.student_id:
            return

        # Kiểm tra xem học viên đã có trong lớp chưa
        existing_class_student = self.env['eb.class.student'].search([
            ('class_id', '=', self.class_id.id),
            ('student_id', '=', self.student_id.id)
        ], limit=1)

        if existing_class_student:
            _logger.info(f"Student {self.student_id.id} already exists in class {self.class_id.id}")
            return

        # Tạo bản ghi eb.class.student
        try:
            class_student_vals = {
                'class_id': self.class_id.id,
                'student_id': self.student_id.id,
                'enrollment_date': fields.Date.context_today(self),
                'state': 'active',
                'payment_status': self.state,  # Sử dụng trạng thái thanh toán từ enrollment
                'notes': f'Tự động thêm từ đăng ký {self.enrollment_number or self.id}'
            }

            class_student = self.env['eb.class.student'].create(class_student_vals)

            _logger.info(f"Auto-assigned student {self.student_id.id} to class {self.class_id.id} (class_student_id: {class_student.id})")

            # Ghi log vào enrollment
            self.message_post(
                body=_("Học viên đã được tự động thêm vào lớp học: %s") % self.class_id.name
            )

        except Exception as e:
            _logger.error(f"Failed to auto-assign student {self.student_id.id} to class {self.class_id.id}: {e}")
            # Không raise exception để không làm gián đoạn quá trình tạo enrollment

    @api.depends("refund_request_ids")
    def _compute_refund_request_count(self):
        """Tính toán số lượng yêu cầu hoàn phí."""
        for record in self:
            record.refund_request_count = len(record.refund_request_ids)

    @api.depends("payment_ids.amount", "payment_ids.state", "payment_ids.is_refund")
    def _compute_paid_amount(self):
        """Tính toán số tiền đã thanh toán từ các khoản thanh toán."""
        for record in self:
            # Lấy các khoản thanh toán đã ghi sổ (không bao gồm hoàn tiền)
            valid_payments = record.payment_ids.filtered(
                lambda p: p.state == "posted" and not p.is_refund
            )

            # Lấy các khoản hoàn tiền
            refunds = record.payment_ids.filtered(
                lambda p: p.is_refund
            )

            # Tính tổng số tiền đã thanh toán (đã trừ hoàn tiền)
            record.paid_amount = sum(
                payment.amount for payment in valid_payments
            ) - sum(refund.amount for refund in refunds)

            # Cập nhật trạng thái đăng ký dựa trên số tiền đã thanh toán
            self._update_enrollment_state(record)

    def _auto_create_student_and_user(self, record):
        """Tự động tạo user và học viên từ thông tin đăng ký."""
        _logger.info(f"Auto creating student and user for enrollment {record.id}")

        # Kiểm tra duplicate email trước khi tạo
        if record.visitor_email:
            # Kiểm tra trong eb.student.student
            existing_student = self.env["eb.student.student"].search(
                [("email", "=", record.visitor_email)], limit=1
            )

            if existing_student:
                # Liên kết với học viên hiện có
                record.write({"student_id": existing_student.id})
                record.message_post(
                    body=_("Đăng ký đã được tự động liên kết với học viên hiện có: %s")
                    % existing_student.name
                )
                _logger.info(f"Enrollment {record.id} linked to existing student {existing_student.id}")

                # Tạo user cho student hiện có nếu chưa có
                if not existing_student.user_id:
                    self._create_user_for_existing_student(existing_student)

                return existing_student

            # Kiểm tra duplicate trong res.users
            existing_user = self.env['res.users'].search([
                ('login', '=', record.visitor_email),
                ('active', '=', True)
            ], limit=1)

            if existing_user:
                _logger.error(f"Email {record.visitor_email} already exists as user {existing_user.id} - {existing_user.name}")
                raise ValidationError(_(
                    "Email '%s' đã được sử dụng bởi tài khoản: %s\n\n"
                    "Không thể tạo học viên mới với email này."
                ) % (record.visitor_email, existing_user.name))

        # Nếu không tìm thấy học viên hiện có, tạo mới
        Partner = self.env["res.partner"]
        Student = self.env["eb.student.student"]
        User = self.env["res.users"]

        # Tìm hoặc tạo partner
        existing_partner = Partner.search(
            [("email", "=", record.visitor_email)], limit=1
        )

        if not existing_partner:
            # Tạo partner mới
            partner_vals = {
                "name": record.visitor_name,
                "email": record.visitor_email,
                "phone": record.visitor_phone,
                "company_type": "person",
            }
            existing_partner = Partner.create(partner_vals)
            _logger.info(f"Created new partner {existing_partner.id} for enrollment {record.id}")

        # Tạo user mới
        try:
            # Kiểm tra xem đã có user với email này chưa
            existing_user = User.search([("login", "=", record.visitor_email)], limit=1)

            if not existing_user:
                # Tạo mật khẩu ngẫu nhiên
                password = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))

                # Tạo user mới
                user_vals = {
                    "name": record.visitor_name,
                    "login": record.visitor_email,
                    "email": record.visitor_email,
                    "partner_id": existing_partner.id,
                    "password": password,
                    # Thêm nhóm "Portal" để user có thể đăng nhập vào website
                    "groups_id": [(6, 0, [self.env.ref('base.group_portal').id])],
                }
                new_user = User.with_context(no_reset_password=True).create(user_vals)
                _logger.info(f"Created new user {new_user.id} for enrollment {record.id}")

                # Không gửi email thông báo tài khoản từ Odoo core
                # template = self.env.ref('auth_signup.mail_template_user_signup_account_created', raise_if_not_found=False)
                # if template:
                #     template.with_context(lang=new_user.lang).send_mail(new_user.id, force_send=True)
                #     _logger.info(f"Sent account creation email to {new_user.login}")
                _logger.info(f"Skipped sending account creation email from Odoo core to {new_user.login}")
            else:
                _logger.info(f"Using existing user {existing_user.id} for enrollment {record.id}")
                new_user = existing_user
                password = None
        except Exception as e:
            _logger.error(f"Error creating user for enrollment {record.id}: {e}")
            new_user = False

        # Tạo học viên mới
        student_vals = {
            "name": record.visitor_name,
            "partner_id": existing_partner.id,
            "email": record.visitor_email,
            "phone": record.visitor_phone,
            "mobile": record.visitor_phone,  # Sử dụng visitor_phone cho cả phone và mobile
            "user_id": new_user.id if new_user else False,
        }

        new_student = Student.with_context(no_reset_password=True).create(student_vals)
        record.write({"student_id": new_student.id})
        _logger.info(f"Created new student {new_student.id} for enrollment {record.id}")

        record.message_post(
            body=_("Đăng ký đã được tự động chuyển đổi thành học viên mới: %s")
            % new_student.name
        )

        # Gửi SMS xác nhận đăng ký nếu có số điện thoại và SMS được bật
        if new_student.mobile or new_student.phone:
            try:
                sms_utils = self.env['eb.lms.sms.utils']
                sms_utils.send_enrollment_confirmation_sms(record.id)
                _logger.info(f"Sent enrollment confirmation SMS for enrollment {record.id}")
            except Exception as e:
                _logger.error("Lỗi khi gửi SMS xác nhận đăng ký cho enrollment %s: %s", record.id, e)

        # Gửi email thông báo tài khoản mới từ module eb_lms
        try:
            # Gửi email thông báo tài khoản mới
            mail_utils = self.env['eb.lms.mail.utils']
            if hasattr(mail_utils, 'send_new_student_account_notification'):
                mail_utils.send_new_student_account_notification(record.id, password)
                _logger.info(f"Sent new student account notification email for enrollment {record.id}")
        except Exception as e:
            _logger.error(f"Failed to send new student account notification email: {e}")

        return new_student

    def _create_user_for_existing_student(self, student):
        """Tạo user cho học viên đã tồn tại nhưng chưa có user."""
        _logger.info(f"Creating user for existing student {student.id} - {student.name}")

        if student.user_id:
            _logger.info(f"Student {student.id} already has user {student.user_id.id}")
            return student.user_id

        if not student.email:
            _logger.warning(f"Cannot create user for student {student.id} - no email")
            return False

        User = self.env["res.users"]

        # Kiểm tra xem đã có user với email này chưa
        existing_user = User.search([("login", "=", student.email)], limit=1)

        if existing_user:
            # Liên kết user hiện có với student
            student.write({"user_id": existing_user.id})
            _logger.info(f"Linked existing user {existing_user.id} to student {student.id}")
            return existing_user

        # Tạo user mới
        try:
            # Tạo mật khẩu ngẫu nhiên
            password = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))

            user_vals = {
                "name": student.name,
                "login": student.email,
                "email": student.email,
                "partner_id": student.partner_id.id,
                "password": password,
                # Thêm nhóm "Portal" để user có thể đăng nhập vào website
                "groups_id": [(6, 0, [self.env.ref('base.group_portal').id])],
            }
            new_user = User.with_context(no_reset_password=True).create(user_vals)

            # Liên kết user mới với student
            student.write({"user_id": new_user.id})

            _logger.info(f"Created new user {new_user.id} for student {student.id}")

            # Gửi email thông báo tài khoản mới
            try:
                mail_utils = self.env['eb.lms.mail.utils']
                if hasattr(mail_utils, 'send_new_student_account_notification'):
                    # Tìm enrollment để gửi email
                    enrollment = self.search([('student_id', '=', student.id)], limit=1)
                    if enrollment:
                        mail_utils.send_new_student_account_notification(enrollment.id, password)
                        _logger.info(f"Sent new student account notification email for student {student.id}")
            except Exception as e:
                _logger.error(f"Failed to send new student account notification email: {e}")

            return new_user

        except Exception as e:
            _logger.error(f"Error creating user for student {student.id}: {e}")
            return False

    def _check_duplicate_email_in_system(self, email):
        """Kiểm tra email đã tồn tại trong hệ thống hay chưa."""
        if not email:
            return

        # Kiểm tra trong res.partner (trừ partner hiện tại nếu đang update)
        existing_partner = self.env['res.partner'].search([
            ('email', '=', email),
            ('customer_rank', '>', 0)  # Chỉ kiểm tra customer
        ], limit=1)

        # Kiểm tra trong eb.student.student
        existing_student = self.env['eb.student.student'].search([
            ('email', '=', email)
        ], limit=1)

        # Kiểm tra trong res.users
        existing_user = self.env['res.users'].search([
            ('login', '=', email),
            ('active', '=', True)
        ], limit=1)

        if existing_partner or existing_student or existing_user:
            error_details = []
            if existing_partner:
                error_details.append(f"Khách hàng: {existing_partner.name}")
            if existing_student:
                error_details.append(f"Học viên: {existing_student.name}")
            if existing_user:
                error_details.append(f"Tài khoản: {existing_user.name}")

            raise ValidationError(_(
                "Email '%s' đã được sử dụng trong hệ thống:\n%s\n\n"
                "Vui lòng sử dụng email khác hoặc liên hệ admin để được hỗ trợ."
            ) % (email, '\n'.join(error_details)))

    def _update_enrollment_state(self, record):
        """Cập nhật trạng thái đăng ký dựa trên số tiền đã thanh toán."""
        # Chỉ cập nhật trạng thái nếu đăng ký không ở trạng thái đã hủy hoặc đã hoàn tiền
        if record.state in ["cancelled", "refunded"]:
            return

        # Tính toán tỷ lệ thanh toán
        payment_ratio = 0
        if record.payment_amount > 0:
            payment_ratio = record.paid_amount / record.payment_amount

        # Lưu trạng thái cũ để kiểm tra sự thay đổi
        old_state = record.state
        payment_state_changed = False
        new_state = record.state

        # Xác định trạng thái mới dựa trên tỷ lệ thanh toán
        if payment_ratio >= 0.999:  # Đã thanh toán đầy đủ (cho phép sai số nhỏ do làm tròn)
            if record.state != "paid":
                new_state = "paid"
                payment_state_changed = True
        elif payment_ratio > 0:  # Đã thanh toán một phần
            if record.state != "partial":
                new_state = "partial"
                payment_state_changed = True
        else:  # Chưa thanh toán
            if record.state != "unpaid":
                new_state = "unpaid"

        # Cập nhật trạng thái nếu có thay đổi
        if payment_state_changed:
            record.write({"state": new_state})

        # Nếu trạng thái chuyển từ unpaid/partial sang paid
        if new_state == "paid" and old_state in ["unpaid", "partial"]:
            if not record.student_id:
                # Trường hợp 1: Chưa có student - tạo student và user
                if record.visitor_email and record.visitor_name:
                    _logger.info(f"Creating student and user for enrollment {record.id} - visitor_email: {record.visitor_email}, visitor_name: {record.visitor_name}")
                    self._auto_create_student_and_user(record)
                    # Sau khi tạo student, tự động thêm vào lớp nếu có class_id
                    record._auto_assign_to_class()
                else:
                    _logger.warning(f"Cannot create student and user for enrollment {record.id} - missing visitor info: visitor_email={record.visitor_email}, visitor_name={record.visitor_name}")
            elif record.student_id and not record.student_id.user_id:
                # Trường hợp 2: Có student nhưng chưa có user - tạo user cho student hiện có
                _logger.info(f"Creating user for existing student {record.student_id.id} in enrollment {record.id}")
                self._create_user_for_existing_student(record.student_id)
        elif record.state == "paid" and record.student_id:
            _logger.info(f"Enrollment {record.id} already has student_id {record.student_id.id}, checking if user exists")
            # Nếu đã có student nhưng student chưa có user, tạo user
            if not record.student_id.user_id and record.student_id.email:
                self._create_user_for_existing_student(record.student_id)

        # Gửi email xác nhận thanh toán nếu trạng thái thanh toán thay đổi
        if payment_state_changed and record.state in ["paid", "partial"]:
            try:
                # Sử dụng tiện ích mail từ eb_lms.mail.utils
                mail_utils = self.env['eb.lms.mail.utils']
                if hasattr(mail_utils, 'send_payment_confirmation'):
                    mail_utils.send_payment_confirmation(record.id)
                    _logger.info(f"Sent payment confirmation email for enrollment {record.id}")

                    # Ghi log
                    record.message_post(
                        body=_("Đã gửi email xác nhận thanh toán cho học viên.")
                    )

                # Gửi SMS xác nhận thanh toán nếu có số điện thoại và SMS được bật
                if record.student_id and (record.student_id.mobile or record.student_id.phone):
                    try:
                        sms_utils = self.env['eb.lms.sms.utils']
                        sms_utils.send_payment_confirmation_sms(record.id)
                        record.message_post(
                            body=_("Đã gửi SMS xác nhận thanh toán cho học viên.")
                        )
                    except Exception as sms_e:
                        _logger.error("Lỗi khi gửi SMS xác nhận thanh toán cho enrollment %s: %s", record.id, sms_e)
                        record.message_post(
                            body=_("Không thể gửi SMS xác nhận thanh toán: %s") % str(sms_e)
                        )

            except Exception as e:
                _logger.error(f"Failed to send payment confirmation email: {e}")

                # Ghi log lỗi
                record.message_post(
                    body=_("Không thể gửi email xác nhận thanh toán: %s") % str(e)
                )

    def update_refund_status(self, source_name, source_id, is_full_refund=True):
        """
        Cập nhật trạng thái đăng ký khi hoàn tiền.

        Args:
            source_name: Tên nguồn hoàn tiền (ví dụ: "yêu cầu hoàn phí", "hóa đơn")
            source_id: ID hoặc mã của nguồn hoàn tiền
            is_full_refund: Có phải hoàn tiền toàn bộ không

        Returns:
            True nếu thành công
        """
        self.ensure_one()

        # Lưu trạng thái cũ để xử lý các đối tượng liên quan
        old_state = self.state

        # Kiểm tra xem đã hoàn tiền toàn bộ hay một phần
        if self.paid_amount <= 0 or is_full_refund:
            new_state = "refunded"
        else:
            new_state = "partial"

        # Cập nhật trạng thái
        self.write({
            "state": new_state,
            "cancellation_date": fields.Datetime.now() if new_state == "refunded" else False,
            "notes": f"{self.notes or ''}\nHoàn tiền từ {source_name} {source_id}",
        })

        # Ghi log
        self.message_post(
            body=_("Trạng thái đăng ký đã thay đổi từ %s sang %s do hoàn tiền.") % (old_state, new_state)
        )

        return True

    def get_class_students(self):
        """
        Lấy danh sách học viên trong lớp liên quan đến đăng ký này.

        Returns:
            Recordset của eb.class.student
        """
        self.ensure_one()

        return self.env["eb.class.student"].search([
            ("student_id", "=", self.student_id.id),
            ("class_id.course_id", "=", self.course_id.id),
            ("state", "not in", ["completed", "cancelled", "refunded"]),
        ])

    def get_future_attendances(self):
        """
        Lấy danh sách điểm danh trong tương lai của học viên.

        Returns:
            Recordset của eb.attendance.attendance
        """
        self.ensure_one()

        return self.env["eb.attendance.attendance"].search([
            ("student_id", "=", self.student_id.id),
            ("lesson_id.class_id.course_id", "=", self.course_id.id),
            ("attendance_date", ">=", fields.Date.context_today(self)),
            ("state", "in", ["draft", "planned"]),
        ])

    def get_future_lesson_participants(self):
        """
        Lấy danh sách tham gia buổi học trong tương lai của học viên.

        Returns:
            Recordset của eb.lesson.participant
        """
        self.ensure_one()

        # Tìm các buổi học trong tương lai
        future_lessons = self.env["eb.lesson.lesson"].search([
            ("class_id.course_id", "=", self.course_id.id),
            ("start_datetime", ">=", fields.Datetime.now()),
            ("state", "in", ["draft", "confirmed", "in_progress"]),
        ])

        participants = self.env["eb.lesson.participant"]
        for lesson in future_lessons:
            lesson_participants = self.env["eb.lesson.participant"].search([
                ("lesson_id", "=", lesson.id),
                ("student_id", "=", self.student_id.id),
                ("state", "not in", ["cancelled", "refunded"]),
            ])
            participants |= lesson_participants

        return participants

    def get_course_material_access(self):
        """
        Lấy danh sách quyền truy cập tài liệu khóa học của học viên.

        Returns:
            Recordset của eb.course.material.access
        """
        self.ensure_one()

        return self.env["eb.course.material.access"].search([
            ("student_id", "=", self.student_id.id),
            ("course_id", "=", self.course_id.id),
            ("state", "!=", "revoked"),
        ])

    @api.depends("payment_amount", "paid_amount")
    def _compute_balance_amount(self):
        """Tính toán số tiền còn lại phải thanh toán."""
        for record in self:
            record.balance_amount = record.payment_amount - record.paid_amount

    @api.onchange("course_id")
    def _onchange_course(self):
        """
        Khi thay đổi khóa học, cập nhật giá trị mặc định.
        """
        if self.course_id:
            # Lấy giá từ các trường compute của khóa học
            course = self.course_id
            if course.has_discount:
                self.payment_amount = course.discounted_price  # Giá đã giảm bao gồm thuế
            else:
                self.payment_amount = course.original_price  # Giá gốc bao gồm thuế

    # Phương thức action_confirm đã được loại bỏ vì không còn cần thiết
    # Đăng ký được tạo với trạng thái mặc định là 'unpaid' (chờ thanh toán)
    # và không cần thêm bước xác nhận đăng ký

    def send_enrollment_confirmation_email(self):
        """Gửi email xác nhận đăng ký khóa học sử dụng layout từ eb_mail."""
        self.ensure_one()

        try:
            # Sử dụng tiện ích mail từ eb_lms.mail.utils
            return self.env['eb.lms.mail.utils'].send_enrollment_confirmation(self.id)
        except Exception as e:
            _logger.error(f"Failed to send enrollment confirmation email: {e}")

        return True

    def action_send_enrollment_reminder(self):
        """Action để admin gửi nhắc nhở hoàn tất đăng ký từ form view."""
        self.ensure_one()

        # Kiểm tra trạng thái enrollment
        if self.state not in ['unpaid', 'partial']:
            raise UserError(_("Chỉ có thể gửi nhắc nhở cho đăng ký chưa thanh toán hoặc thanh toán một phần."))

        # Kiểm tra có email để gửi không
        recipient_email = self.student_id.email if self.student_id else self.visitor_email
        if not recipient_email:
            raise UserError(_("Không có email để gửi nhắc nhở. Vui lòng cập nhật email cho học viên."))

        # Gửi nhắc nhở
        if self.send_enrollment_reminder():
            # Đánh dấu đã gửi reminder (nếu chưa gửi)
            if not self.reminder_sent:
                self.reminder_sent = True

            # Hiển thị thông báo thành công
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Thành công"),
                    'message': _("Đã gửi email nhắc nhở đến %s") % recipient_email,
                    'type': 'success',
                    'sticky': False,
                }
            }
        else:
            # Hiển thị thông báo lỗi
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Lỗi"),
                    'message': _("Không thể gửi email nhắc nhở. Vui lòng kiểm tra cấu hình email."),
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_send_enrollment_reminder_bulk(self):
        """Action để gửi nhắc nhở hàng loạt từ list view."""
        # Lọc chỉ những enrollment có thể gửi nhắc nhở
        valid_enrollments = self.filtered(lambda e: e.state in ['unpaid', 'partial'])

        if not valid_enrollments:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Thông báo"),
                    'message': _("Không có đăng ký nào phù hợp để gửi nhắc nhở."),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Lọc những enrollment có email
        enrollments_with_email = valid_enrollments.filtered(
            lambda e: (e.student_id and e.student_id.email) or e.visitor_email
        )

        if not enrollments_with_email:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Lỗi"),
                    'message': _("Không có đăng ký nào có email để gửi nhắc nhở."),
                    'type': 'danger',
                    'sticky': True,
                }
            }

        # Gửi nhắc nhở cho từng enrollment
        success_count = 0
        error_count = 0

        for enrollment in enrollments_with_email:
            try:
                if enrollment.send_enrollment_reminder():
                    if not enrollment.reminder_sent:
                        enrollment.reminder_sent = True
                    success_count += 1
                else:
                    error_count += 1
            except Exception as e:
                _logger.error(f"Lỗi khi gửi nhắc nhở hàng loạt cho enrollment {enrollment.enrollment_number}: {str(e)}")
                error_count += 1

        # Hiển thị kết quả
        if success_count > 0:
            message = _("Đã gửi nhắc nhở thành công cho %s đăng ký.") % success_count
            if error_count > 0:
                message += _(" Có %s đăng ký gặp lỗi.") % error_count

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Hoàn thành"),
                    'message': message,
                    'type': 'success' if error_count == 0 else 'warning',
                    'sticky': False,
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Lỗi"),
                    'message': _("Không thể gửi nhắc nhở cho bất kỳ đăng ký nào."),
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def send_enrollment_reminder(self):
        """Gửi email nhắc nhở hoàn tất đăng ký khóa học."""
        self.ensure_one()

        try:
            # Sử dụng tiện ích mail từ eb_lms.mail.utils
            return self.env['eb.lms.mail.utils'].send_enrollment_reminder(self.id)
        except Exception as e:
            _logger.error(f"Failed to send enrollment reminder email: {e}")
            return False

    def send_enrollment_cancellation_email(self):
        """Gửi email thông báo hủy đăng ký khóa học sử dụng layout từ eb_mail."""
        self.ensure_one()

        try:
            # Sử dụng tiện ích mail từ eb_lms.mail.utils
            return self.env['eb.lms.mail.utils'].send_enrollment_cancellation(self.id)
        except Exception as e:
            _logger.error(f"Failed to send enrollment cancellation email: {e}")

        return True

    def send_course_completion_notification(self, completion_stats=None):
        """Gửi thông báo hoàn thành khóa học cho học viên sử dụng layout từ eb_mail."""
        self.ensure_one()

        try:
            # Sử dụng tiện ích mail từ eb_lms.mail.utils
            return self.env['eb.lms.mail.utils'].send_student_course_completion_notification(
                self.id, completion_stats
            )
        except Exception as e:
            _logger.error(f"Failed to send course completion notification: {e}")

        return True

    def send_payment_reminder(self, payment_url=None):
        """Gửi nhắc nhở thanh toán cho học viên sử dụng layout từ eb_mail."""
        self.ensure_one()

        try:
            # Sử dụng tiện ích mail từ eb_lms.mail.utils
            return self.env['eb.lms.mail.utils'].send_payment_reminder(self.id, payment_url)
        except Exception as e:
            _logger.error(f"Failed to send payment reminder: {e}")

        return True

    def action_cancel(self):
        """Hủy đăng ký khóa học."""
        for record in self:
            if record.state not in ("cancelled", "refunded"):
                # Kiểm tra xem đã thanh toán chưa
                has_payment = record.state in ["partial", "paid"] and record.paid_amount > 0

                if has_payment:
                    # Đã thanh toán, tạo yêu cầu hoàn tiền
                    RefundRequest = self.env["eb.refund.request"]

                    # Tìm khoản thanh toán gốc
                    original_payment = record.payment_ids.filtered(
                        lambda p: p.state == "posted" and not p.is_refund
                    )

                    # Tạo yêu cầu hoàn tiền
                    refund_vals = {
                        "student_id": record.student_id.id,
                        "enrollment_id": record.id,
                        "request_amount": record.paid_amount,
                        "approved_amount": record.paid_amount,
                        "reason": _("Hủy đăng ký khóa học"),
                        "source": "system",
                        "refund_method": "original",
                        "state": "submitted",  # Đặt trạng thái là đã gửi
                    }

                    # Nếu có khoản thanh toán gốc, liên kết với yêu cầu hoàn tiền
                    if original_payment:
                        refund_vals["payment_id"] = original_payment[0].id

                    refund_request = RefundRequest.create(refund_vals)

                    # Ghi log
                    record.message_post(
                        body=_("Đã tạo yêu cầu hoàn tiền %s do hủy đăng ký.") % refund_request.name
                    )

                    # Cập nhật trạng thái đăng ký
                    record.write({
                        "state": "cancelled",
                        "cancellation_date": fields.Datetime.now(),
                        "cancellation_reason": "Hủy theo yêu cầu",
                    })
                else:
                    # Chưa thanh toán, hủy đăng ký và các đối tượng liên quan
                    record.write({
                        "state": "cancelled",
                        "cancellation_date": fields.Datetime.now(),
                        "cancellation_reason": "Hủy theo yêu cầu",
                    })

                    # Cập nhật trạng thái của đơn hàng liên quan
                    if record.sale_order_id and record.sale_order_id.state not in ['cancel', 'done']:
                        try:
                            # Thử hủy đơn hàng
                            record.sale_order_id.with_context(from_enrollment_cancel=True).action_cancel()
                            record.message_post(body=_("Đơn hàng %s đã được hủy tự động.") % record.sale_order_id.name)
                        except Exception as e:
                            # Ghi log lỗi nhưng vẫn tiếp tục hủy đăng ký
                            _logger.error(f"Không thể hủy đơn hàng {record.sale_order_id.name}: {str(e)}")
                            record.message_post(body=_("Không thể hủy đơn hàng %s: %s") % (record.sale_order_id.name, str(e)))

                    # Cập nhật trạng thái của hóa đơn liên quan
                    if record.sale_order_id and record.sale_order_id.invoice_ids:
                        for invoice in record.sale_order_id.invoice_ids.filtered(lambda inv: inv.state not in ['cancel']):
                            try:
                                invoice.button_cancel()
                                record.message_post(body=_("Hóa đơn %s đã được hủy tự động.") % invoice.name)
                            except Exception as e:
                                # Ghi log lỗi nhưng vẫn tiếp tục hủy đăng ký
                                _logger.error(f"Không thể hủy hóa đơn {invoice.name}: {str(e)}")
                                record.message_post(body=_("Không thể hủy hóa đơn %s: %s") % (invoice.name, str(e)))

                    # Cập nhật trạng thái của các khoản thanh toán liên quan
                    for payment in record.payment_ids.filtered(lambda p: p.state not in ['cancelled', 'refunded']):
                        try:
                            payment.action_cancel()
                            record.message_post(body=_("Khoản thanh toán %s đã được hủy tự động.") % payment.name)
                        except Exception as e:
                            # Ghi log lỗi nhưng vẫn tiếp tục hủy đăng ký
                            _logger.error(f"Không thể hủy khoản thanh toán {payment.name}: {str(e)}")
                            record.message_post(body=_("Không thể hủy khoản thanh toán %s: %s") % (payment.name, str(e)))

                    # Cập nhật trạng thái của các giao dịch thanh toán liên quan
                    transactions = self.env['payment.transaction']

                    # Tìm các giao dịch từ đơn hàng
                    if record.sale_order_id:
                        order_transactions = self.env['payment.transaction'].search([
                            ('sale_order_ids', 'in', record.sale_order_id.ids),
                            ('state', 'in', ['draft', 'pending', 'authorized'])
                        ])
                        transactions |= order_transactions

                    # Tìm các giao dịch từ khoản thanh toán học viên
                    for payment in record.payment_ids:
                        if payment.transaction_id and payment.transaction_id.state in ['draft', 'pending', 'authorized']:
                            transactions |= payment.transaction_id

                    # Hủy các giao dịch thanh toán
                    for transaction in transactions:
                        try:
                            transaction.write({
                                'state': 'cancel',
                                'last_state_change': fields.Datetime.now(),
                            })
                            record.message_post(body=_("Giao dịch thanh toán %s đã được hủy tự động.") % transaction.reference)
                        except Exception as e:
                            # Ghi log lỗi nhưng vẫn tiếp tục hủy đăng ký
                            _logger.error(f"Không thể hủy giao dịch thanh toán {transaction.reference}: {str(e)}")
                            record.message_post(body=_("Không thể hủy giao dịch thanh toán %s: %s") % (transaction.reference, str(e)))

                # Ghi log hủy
                record.message_post(body=_("Đăng ký đã bị hủy."))

                # Gửi email thông báo hủy đăng ký
                try:
                    record.send_enrollment_cancellation_email()
                except Exception as e:
                    _logger.error(f"Không thể gửi email thông báo hủy đăng ký {record.enrollment_number}: {str(e)}")

        return True

    def action_view_payments(self):
        """Hiển thị danh sách các khoản thanh toán của đăng ký."""
        self.ensure_one()
        return {
            "name": _("Payments"),
            "type": "ir.actions.act_window",
            "res_model": "eb.student.payment",
            "view_mode": "list,form",
            "domain": [("enrollment_id", "=", self.id)],
            "context": {"default_enrollment_id": self.id, "create": False},
        }

    def action_create_payment(self):
        """Tạo khoản thanh toán mới cho đăng ký."""
        self.ensure_one()

        # Kiểm tra số tiền còn lại
        if self.balance_amount <= 0:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Payment Completed"),
                    "message": _("This enrollment is already fully paid."),
                    "sticky": False,
                    "type": "info",
                },
            }

        # Thay đổi: Hiển thị thông báo thay vì mở form tạo thanh toán
        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": _("Thanh toán khóa học"),
                "message": _("Các khoản thanh toán học viên chỉ được tạo tự động từ quy trình đăng ký và thanh toán khóa học. Vui lòng sử dụng nút 'Thanh toán' để chuyển đến trang thanh toán."),
                "sticky": True,
                "type": "warning",
                "next": {
                    "type": "ir.actions.act_window_close"
                }
            },
        }

    def action_view_sale_order(self):
        """Xem đơn hàng liên kết với đăng ký."""
        self.ensure_one()
        if not self.sale_order_id:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("No Sales Order"),
                    "message": _("There is no sales order linked to this enrollment."),
                    "sticky": False,
                    "type": "warning",
                },
            }

        return {
            "name": _("Sales Order"),
            "type": "ir.actions.act_window",
            "res_model": "sale.order",
            "res_id": self.sale_order_id.id,
            "view_mode": "form",
            "target": "current",
        }

    def action_view_refund_requests(self):
        """Hiển thị danh sách yêu cầu hoàn phí liên quan đến đăng ký."""
        self.ensure_one()

        if not self.refund_request_ids:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Không có yêu cầu hoàn phí"),
                    "message": _("Không có yêu cầu hoàn phí nào liên quan đến đăng ký này."),
                    "sticky": False,
                    "type": "warning",
                },
            }

        action = {
            "name": _("Yêu cầu hoàn phí"),
            "type": "ir.actions.act_window",
            "res_model": "eb.refund.request",
            "context": {"create": False},
        }

        if len(self.refund_request_ids) == 1:
            action.update({
                "view_mode": "form",
                "res_id": self.refund_request_ids.id,
            })
        else:
            action.update({
                "view_mode": "list,form",
                "domain": [("id", "in", self.refund_request_ids.ids)],
            })

        return action

    def action_view_invoices(self):
        """Hiển thị danh sách hóa đơn liên quan đến đăng ký khóa học."""
        self.ensure_one()

        if not self.sale_order_id:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("No Sales Order"),
                    "message": _("There is no sales order linked to this enrollment."),
                    "sticky": False,
                    "type": "warning",
                },
            }

        invoices = self.sale_order_id.invoice_ids

        if not invoices:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("No Invoices"),
                    "message": _("There are no invoices linked to this enrollment."),
                    "sticky": False,
                    "type": "warning",
                },
            }

        action = {
            "name": _("Invoices"),
            "type": "ir.actions.act_window",
            "res_model": "account.move",
            "context": {"create": False},
        }

        if len(invoices) == 1:
            action.update({
                "view_mode": "form",
                "res_id": invoices.id,
            })
        else:
            action.update({
                "view_mode": "list,form",
                "domain": [("id", "in", invoices.ids)],
            })

        return action

    def action_confirm_payment(self):
        """Xác nhận thanh toán đầy đủ cho đăng ký."""
        self.ensure_one()

        # Kiểm tra nếu đã thanh toán đầy đủ
        if self.state == 'paid':
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Đã thanh toán"),
                    "message": _("Đăng ký này đã được thanh toán đầy đủ."),
                    "sticky": False,
                    "type": "info",
                },
            }

        # Tìm payment transaction liên quan đến đăng ký này
        transaction = False
        if self.sale_order_id:
            transaction = self.env['payment.transaction'].search([
                ('sale_order_ids', 'in', self.sale_order_id.ids),
                ('state', 'in', ['draft', 'pending', 'authorized'])
            ], limit=1)

        # Tìm student payment liên quan đến đăng ký này từ trường payment_ids
        student_payment = self.payment_ids.filtered(
            lambda p: p.state in ['unpaid', 'pending']
        )

        # Tìm hóa đơn liên quan đến đăng ký này
        invoice = False
        draft_invoice = False
        if self.sale_order_id:
            # Tìm hóa đơn nháp
            draft_invoice = self.sale_order_id.invoice_ids.filtered(
                lambda inv: inv.state == 'draft' and inv.move_type == 'out_invoice'
            )
            if draft_invoice and len(draft_invoice) > 1:
                draft_invoice = draft_invoice[0]

            # Tìm hóa đơn đã xác nhận
            posted_invoice = self.sale_order_id.invoice_ids.filtered(
                lambda inv: inv.state == 'posted' and inv.payment_state != 'paid'
            )
            if posted_invoice and len(posted_invoice) > 1:
                posted_invoice = posted_invoice[0]

            # Ưu tiên sử dụng hóa đơn nháp (sẽ xác nhận sau)
            invoice = draft_invoice or posted_invoice

        # Nếu có payment transaction, cập nhật trạng thái thành 'done'
        account_payment = False
        if transaction:
            # Cập nhật trạng thái giao dịch
            transaction.write({
                'state': 'done',
                'last_state_change': fields.Datetime.now(),
            })

            # Tạo account.payment nếu cần
            if transaction.provider_id and transaction.provider_id.journal_id:
                # Tìm payment method line phù hợp
                payment_method_line = transaction.provider_id.journal_id.inbound_payment_method_line_ids.filtered(
                    lambda line: line.payment_provider_id == transaction.provider_id
                )

                if not payment_method_line:
                    # Nếu không tìm thấy, sử dụng bất kỳ payment method line nào
                    payment_method_line = transaction.provider_id.journal_id.inbound_payment_method_line_ids[0] if transaction.provider_id.journal_id.inbound_payment_method_line_ids else False

                if payment_method_line:
                    # Tạo payment values
                    payment_values = {
                        'amount': transaction.amount,
                        'payment_type': 'inbound',
                        'currency_id': transaction.currency_id.id,
                        'partner_id': transaction.partner_id.commercial_partner_id.id,
                        'partner_type': 'customer',
                        'journal_id': transaction.provider_id.journal_id.id,
                        'company_id': transaction.provider_id.company_id.id,
                        'payment_method_line_id': payment_method_line.id,
                        'payment_token_id': transaction.token_id.id if transaction.token_id else False,
                        'payment_transaction_id': transaction.id,
                        'memo': transaction.reference,
                        'payment_reference': transaction.reference,
                    }

                    # Thêm invoice_ids nếu có
                    if transaction.invoice_ids:
                        payment_values['invoice_ids'] = [(6, 0, transaction.invoice_ids.ids)]

                    # Tạo account.payment
                    try:
                        account_payment = self.env['account.payment'].create(payment_values)
                        account_payment.action_post()
                        _logger.info("Created and posted account.payment %s for transaction %s", account_payment.id, transaction.id)

                        # Thêm bước đối chiếu với invoice nếu có
                        if transaction.invoice_ids:
                            for invoice in transaction.invoice_ids:
                                # Kiểm tra xem invoice đã được đăng tải chưa, nếu chưa thì xác nhận
                                if invoice.state != 'posted':
                                    _logger.info("Invoice %s is not posted (state: %s), posting it now",
                                                invoice.id, invoice.state)
                                    try:
                                        invoice.action_post()
                                        _logger.info("Successfully posted invoice %s", invoice.id)
                                    except Exception as e:
                                        _logger.error("Failed to post invoice %s: %s", invoice.id, str(e))
                                        return

                                # Kiểm tra xem payment đã được đăng tải chưa
                                if not account_payment.move_id or account_payment.move_id.state != 'posted':
                                    _logger.warning("Cannot reconcile because payment %s is not posted or has no move_id",
                                                   account_payment.id)
                                    return

                                try:
                                    # Tìm các account.move.line cần đối chiếu
                                    payment_lines = account_payment.move_id.line_ids.filtered(
                                        lambda line: (
                                            line.account_id.account_type in ('asset_receivable', 'liability_payable')
                                            and not line.reconciled
                                        )
                                    )
                                    invoice_lines = invoice.line_ids.filtered(
                                        lambda line: (
                                            line.account_id.account_type in ('asset_receivable', 'liability_payable')
                                            and not line.reconciled
                                        )
                                    )

                                    # Đối chiếu payment với invoice
                                    if payment_lines and invoice_lines:
                                        (payment_lines + invoice_lines).reconcile()
                                        _logger.info("Reconciled payment %s with invoice %s", account_payment.id, invoice.id)

                                        # Kiểm tra trạng thái thanh toán của hóa đơn
                                        invoice.invalidate_recordset(['payment_state'])
                                        _logger.info("Invoice payment state after reconciliation: %s", invoice.payment_state)
                                except Exception as e:
                                    _logger.error("Error reconciling payment %s with invoice %s: %s",
                                                 account_payment.id, invoice.id, str(e))
                    except Exception as e:
                        _logger.error("Error creating account.payment for transaction %s: %s", transaction.id, str(e))



        # Nếu có student payment, cập nhật trạng thái thành 'posted'
        if student_payment:
            # Lấy payment đầu tiên nếu có nhiều
            if len(student_payment) > 1:
                student_payment = student_payment[0]

            student_payment.write({
                'state': 'posted',
                'payment_date': fields.Date.context_today(self),
                'notes': _("Thanh toán thủ công được xác nhận bởi admin"),
                'invoice_id': invoice.id if invoice else False,
            })
        else:
            # Tìm payment method mặc định nếu không có student_payment
            default_payment_method = False
            if not student_payment or not student_payment.payment_method_id:
                default_payment_method = self.env["payment.method"].search([("code", "=", "cash")], limit=1)
                if not default_payment_method:
                    default_payment_method = self.env["payment.method"].search([], limit=1)

            # Tạo khoản thanh toán mới với số tiền còn lại
            payment_vals = {
                'course_id': self.course_id.id,
                'enrollment_id': self.id,
                'student_id': self.student_id.id if self.student_id else False,
                'payment_date': fields.Date.context_today(self),
                'amount': self.balance_amount,
                'payment_method_id': (student_payment.payment_method_id.id if student_payment and student_payment.payment_method_id else
                                      default_payment_method.id if default_payment_method else False),
                'state': 'posted',  # Đặt trạng thái là đã ghi sổ
                'notes': _("Thanh toán thủ công được xác nhận bởi admin"),
            }

            # Thêm transaction_id nếu có
            if transaction:
                payment_vals['transaction_id'] = transaction.id

            # Thêm invoice_id nếu có
            if transaction and transaction.invoice_ids:
                payment_vals['invoice_id'] = transaction.invoice_ids[0].id
            elif invoice:
                payment_vals['invoice_id'] = invoice.id

            # Tạo khoản thanh toán
            student_payment = self.env['eb.student.payment'].create(payment_vals)

        # Xác nhận hóa đơn nháp nếu có
        if draft_invoice:
            try:
                draft_invoice.action_post()
                _logger.info("Posted draft invoice %s for enrollment %s", draft_invoice.id, self.id)
                # Cập nhật biến invoice để sử dụng hóa đơn đã xác nhận
                invoice = draft_invoice
            except Exception as e:
                _logger.error("Error posting draft invoice %s for enrollment %s: %s", draft_invoice.id, self.id, str(e))

        # Tạo account.payment nếu chưa được tạo từ transaction và có hóa đơn
        if not account_payment and invoice:
            try:
                # Sử dụng account.payment.register wizard để tạo thanh toán
                # Đây là cách tiêu chuẩn trong Odoo để tạo thanh toán từ hóa đơn

                # Tìm journal phù hợp cho thanh toán
                journal = self.env['account.journal'].search([
                    ('type', 'in', ['bank', 'cash']),
                    ('company_id', '=', self.company_id.id)
                ], limit=1)

                if not journal:
                    _logger.error("No payment journal found for enrollment %s", self.id)
                    return

                # Tạo payment register wizard
                context = {
                    'active_model': 'account.move',
                    'active_ids': [invoice.id],
                }

                payment_register = self.env['account.payment.register'].with_context(**context).create({
                    'amount': self.balance_amount,
                    'journal_id': journal.id,
                    'payment_date': fields.Date.context_today(self),
                    'communication': _("Thanh toán thủ công cho đăng ký %s") % self.name,
                })

                # Tạo thanh toán từ wizard và đối chiếu tự động
                action = payment_register.action_create_payments()

                # Lấy payment_id từ action
                account_payment = False
                if action and action.get('res_id'):
                    account_payment = self.env['account.payment'].browse(action['res_id'])
                elif action and action.get('domain'):
                    payment_ids = action['domain'][0][2]  # Lấy danh sách ID từ domain [('id', 'in', [ids])]
                    if payment_ids:
                        account_payment = self.env['account.payment'].browse(payment_ids[0])

                if account_payment:
                    _logger.info("Created and posted manual account.payment %s for enrollment %s", account_payment.id, self.id)

                    # Cập nhật student_payment với thông tin account_payment
                    if student_payment:
                        student_payment.write({
                            'notes': _("%s\nĐã tạo thanh toán kế toán #%s") % (
                                student_payment.notes or "", account_payment.name
                            ),
                        })

                    # Đảm bảo hóa đơn được đánh dấu là đã thanh toán
                    if invoice and invoice.payment_state != 'paid':
                        _logger.info("Checking payment state for invoice %s after payment creation", invoice.id)
                        # Kiểm tra trạng thái thanh toán của hóa đơn
                        invoice.invalidate_recordset(['payment_state'])
                        if invoice.payment_state != 'paid':
                            _logger.warning("Invoice %s is not marked as paid after payment creation. Payment state: %s",
                                           invoice.id, invoice.payment_state)

                            # Thực hiện đối chiếu thủ công giữa payment và invoice
                            try:
                                # Kiểm tra xem invoice đã được đăng tải chưa, nếu chưa thì xác nhận
                                if invoice.state != 'posted':
                                    _logger.info("Invoice %s is not posted (state: %s), posting it now",
                                                invoice.id, invoice.state)
                                    try:
                                        invoice.action_post()
                                        _logger.info("Successfully posted invoice %s", invoice.id)
                                    except Exception as e:
                                        _logger.error("Failed to post invoice %s: %s", invoice.id, str(e))
                                        return

                                # Kiểm tra xem payment đã được đăng tải chưa
                                if not account_payment.move_id or account_payment.move_id.state != 'posted':
                                    _logger.warning("Cannot reconcile because payment %s is not posted or has no move_id",
                                                   account_payment.id)
                                    return

                                # Tìm các account.move.line cần đối chiếu
                                payment_lines = account_payment.move_id.line_ids.filtered(
                                    lambda line: (
                                        line.account_id.account_type in ('asset_receivable', 'liability_payable')
                                        and not line.reconciled
                                    )
                                )
                                invoice_lines = invoice.line_ids.filtered(
                                    lambda line: (
                                        line.account_id.account_type in ('asset_receivable', 'liability_payable')
                                        and not line.reconciled
                                    )
                                )

                                # Đối chiếu payment với hóa đơn
                                if payment_lines and invoice_lines:
                                    (payment_lines + invoice_lines).reconcile()
                                    _logger.info(
                                        "Manually reconciled payment %s with invoice %s",
                                        account_payment.id, invoice.id
                                    )

                                    # Kiểm tra lại trạng thái thanh toán
                                    invoice.invalidate_recordset(['payment_state'])
                                    _logger.info(
                                        "Invoice payment state after manual reconciliation: %s",
                                        invoice.payment_state
                                    )
                            except Exception as e:
                                _logger.error("Error during manual reconciliation: %s", str(e))
            except Exception as e:
                _logger.error("Error creating manual account.payment for enrollment %s: %s", self.id, str(e))

        # Cập nhật trạng thái đăng ký
        self._compute_paid_amount()

        # Xác nhận đơn hàng nếu có
        if self.sale_order_id and self.sale_order_id.state in ['draft', 'sent']:
            self.sale_order_id.action_confirm()

        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": _("Thanh toán thành công"),
                "message": _("Đã xác nhận thanh toán cho đăng ký này."),
                "sticky": False,
                "type": "success",
                "next": {"type": "ir.actions.act_window_close"},
            },
        }

    def action_go_to_payment(self):
        """Điều hướng đến trang thanh toán (cho website hoặc nội bộ)."""
        self.ensure_one()

        # Lấy base URL từ system parameter
        base_url = self.env['ir.config_parameter'].sudo().get_param(
            'eb_lms.payment_website_base_url', 'https://vantis.edu.vn'
        )

        # Nếu đơn hàng đã được tạo, điều hướng đến trang thanh toán
        if self.sale_order_id:
            return {
                "type": "ir.actions.act_url",
                "url": f"{base_url}/payment/{self.sale_order_id.name}",
                "target": "new",
            }

        # Nếu chưa có đơn hàng, tạo đơn hàng mới
        if not self.course_id.product_id:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Missing Product"),
                    "message": _(
                        "This course has no product linked. Please set up the product first."
                    ),
                    "sticky": False,
                    "type": "warning",
                },
            }

        # Tạo đơn hàng mới
        self._create_sale_order()

        # Điều hướng đến trang thanh toán
        if self.sale_order_id:
            return {
                "type": "ir.actions.act_url",
                "url": f"{base_url}/payment/{self.sale_order_id.name}",
                "target": "new",
            }

        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": _("Error"),
                "message": _("Failed to create sales order. Please try again."),
                "sticky": False,
                "type": "danger",
            },
        }

    def _create_sale_order(self):
        """Tạo đơn hàng Sale Order từ đăng ký."""
        self.ensure_one()

        if not self.course_id.product_id:
            return False

        # Tìm hoặc tạo partner
        partner = self.student_id.partner_id if self.student_id else None
        if not partner and self.visitor_email:
            partner = self.env["res.partner"].search(
                [("email", "=", self.visitor_email)], limit=1
            )
        if not partner:
            # Lấy thông tin country để tạo partner nếu cần
            country = None
            if self.visitor_phone:  # Cần logic để lấy country code từ visitor_phone nếu lưu dạng E164
                # Giả sử visitor_phone lưu dạng +84..., cần tách mã quốc gia
                pass  # Bổ sung logic lấy country code từ phone nếu cần
            if not country:
                country = self.env.ref("base.vn", raise_if_not_found=False)

            partner_vals = {
                "name": self.visitor_name,
                "email": self.visitor_email,
                "phone": self.visitor_phone,  # Nên chuẩn hóa trước khi lưu
                "country_id": country.id if country else None,
                "customer_rank": 1,
            }
            partner = self.env["res.partner"].create(partner_vals)
            # Nếu là đăng ký từ website, có thể liên kết lại student nếu partner mới tạo
            # Hoặc tạo student mới nếu cần

        # Lấy giá chưa bao gồm thuế từ khóa học
        product = self.course_id.product_id
        course = self.course_id
        price_unit = course.discounted_price_untaxed if course.has_discount else course.original_price_untaxed

        # Tạo sale order
        order_vals = {
            "partner_id": partner.id,
            "enrollment_id": self.id,
            "order_line": [
                (
                    0,
                    0,
                    {
                        "product_id": product.id,
                        "product_uom_qty": 1,
                        "price_unit": price_unit,  # Sử dụng giá chưa bao gồm thuế
                        "discount": course.discount_percentage if course.has_discount else 0.0,
                        "name": f"Đăng ký khóa học: {self.course_id.name}",
                    },
                )
            ],
        }

        sale_order = self.env["sale.order"].create(order_vals)
        self.write({"sale_order_id": sale_order.id})
        return sale_order

    def _generate_dh_payment_reference(self):
        """Tạo mã tham chiếu thanh toán dạng DH + 8 số ngẫu nhiên duy nhất, không bắt đầu/kết thúc bằng 0."""
        self.ensure_one()
        PaymentTransaction = self.env["payment.transaction"]
        while True:
            # Tạo 8 chữ số ngẫu nhiên
            random_digits = ''.join(random.choices(string.digits, k=8))

            # Kiểm tra số đầu và số cuối không phải là '0'
            if random_digits[0] == '0' or random_digits[-1] == '0':
                continue  # Tạo lại nếu bắt đầu hoặc kết thúc bằng 0

            reference = f"DH{random_digits}"
            # Kiểm tra xem reference đã tồn tại chưa
            if not PaymentTransaction.search_count([("reference", "=", reference)]):
                return reference
            # Nếu tồn tại, vòng lặp sẽ tiếp tục để tạo mã mới

    def action_assign_class(self):
        """Mở wizard để phân lớp cho học viên."""
        self.ensure_one()

        # Kiểm tra trạng thái đăng ký
        if self.state not in ["paid", "partial"]:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Payment Required"),
                    "message": _(
                        "Student must make at least a partial payment before being assigned to a class."
                    ),
                    "sticky": False,
                    "type": "warning",
                },
            }

        # Tìm các lớp học của khóa học
        classes = self.env["eb.class.class"].search(
            [
                ("course_id", "=", self.course_id.id),
                ("stage_id.is_recruiting", "=", True),  # Chỉ lớp đang tuyển sinh
            ]
        )

        if not classes:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("No Available Classes"),
                    "message": _(
                        "There are no classes in recruiting stage for this course."
                    ),
                    "sticky": False,
                    "type": "warning",
                },
            }

        # Hiển thị wizard để chọn lớp
        return {
            "name": _("Assign to Class"),
            "type": "ir.actions.act_window",
            "res_model": "eb.enrollment.assign.class.wizard",
            "view_mode": "form",
            "target": "new",
            "context": {
                "default_enrollment_id": self.id,
                "default_student_id": self.student_id.id,
            },
        }

    def action_convert_to_student(self):
        """Chuyển đổi đăng ký website thành học viên chính thức."""
        self.ensure_one()

        if self.student_id:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Already Converted"),
                    "message": _("This enrollment is already linked to a student."),
                    "sticky": False,
                    "type": "info",
                },
            }

        if not self.visitor_name or not self.visitor_email:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Missing Information"),
                    "message": _(
                        "Visitor name and email are required to convert to student."
                    ),
                    "sticky": False,
                    "type": "warning",
                },
            }

        # Kiểm tra nếu đã có học viên với email này
        Student = self.env["eb.student.student"]
        existing_student = Student.search(
            [
                ("email", "=", self.visitor_email),
            ],
            limit=1,
        )

        if existing_student:
            # Liên kết với học viên hiện có
            self.student_id = existing_student.id
            self.message_post(
                body=_("Đăng ký đã được liên kết với học viên hiện có: %s")
                % existing_student.name
            )

            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Student Linked"),
                    "message": _("Enrollment linked to existing student: %s")
                    % existing_student.name,
                    "sticky": False,
                    "type": "success",
                },
            }

        # Không tìm thấy học viên hiện có, tạo mới
        # Sử dụng partner_id từ enrollment (đã được tạo khi đăng ký)
        if not self.partner_id:
            raise ValidationError(_("Không tìm thấy thông tin khách hàng để tạo học viên."))

        existing_partner = self.partner_id

        # Tạo học viên mới
        student_vals = {
            "name": self.visitor_name,
            "partner_id": existing_partner.id,
            "email": self.visitor_email,
            "phone": self.visitor_phone,
        }

        new_student = Student.create(student_vals)
        self.student_id = new_student.id

        self.message_post(
            body=_("Đăng ký đã được chuyển đổi thành học viên mới: %s")
            % new_student.name
        )

        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": _("Student Created"),
                "message": _("New student created: %s") % new_student.name,
                "sticky": False,
                "type": "success",
            },
        }

    @api.model
    def create_enrollment_with_payment(self, vals):
        """
        Phương thức chung để tạo đăng ký khóa học và xử lý thanh toán.

        Được sử dụng bởi cả API và wizard đăng ký.

        Args:
            vals (dict): Dictionary chứa các thông tin cần thiết:
                - course_id: ID khóa học
                - student_id: ID học viên (tùy chọn)
                - is_website_enrollment: Boolean đánh dấu đăng ký từ website
                - visitor_name: Tên người đăng ký (nếu là đăng ký từ website)
                - visitor_email: Email người đăng ký (nếu là đăng ký từ website)
                - visitor_phone: Số điện thoại người đăng ký (nếu là đăng ký từ website)
                - notes: Ghi chú đăng ký
                - payment_provider_id: ID cổng thanh toán
                - payment_method_id: ID phương thức thanh toán
                - partner_id: ID đối tác (nếu đã có)
                - country_code: Mã quốc gia (tùy chọn)

        Returns:
            dict: Thông tin về đăng ký và thanh toán đã tạo

        Raises:
            ValidationError: Khi có lỗi trong quá trình xử lý
        """
        # Lấy thông tin từ vals
        course_id = vals.get('course_id')
        student_id = vals.get('student_id')
        is_website_enrollment = vals.get('is_website_enrollment', False)
        visitor_name = vals.get('visitor_name')
        visitor_email = vals.get('visitor_email')
        visitor_phone = vals.get('visitor_phone')
        notes = vals.get('notes')
        partner_id = vals.get('partner_id')
        country_code = vals.get('country_code')

        # 1. Kiểm tra khóa học tồn tại
        course = self.env['eb.course.course'].browse(course_id)
        if not course.exists():
            raise ValidationError(_("Không tìm thấy khóa học với ID %s") % course_id)

        # 2. Kiểm tra khóa học có thể đăng ký không
        availability_check = course.check_enrollment_availability()
        if not availability_check["available"]:
            raise ValidationError(_(availability_check["message"]))

        # 3. Kiểm tra sản phẩm liên kết bắt buộc
        product = course.product_id
        if not product:
            raise ValidationError(_("Khóa học chưa được liên kết với sản phẩm."))

        # 4. Kiểm tra duplicate email trong hệ thống TRƯỚC KHI tạo partner (nếu là đăng ký từ website)
        if is_website_enrollment and visitor_email:
            self._check_duplicate_email_in_system(visitor_email)

        # 5. Xác định partner_id nếu chưa có
        if not partner_id:
            if student_id:
                student = self.env['eb.student.student'].browse(student_id)
                partner_id = student.partner_id.id
            elif is_website_enrollment and visitor_email:
                # Tìm partner theo email
                partner = self.env['res.partner'].search([('email', '=', visitor_email)], limit=1)
                if not partner:
                    # Tạo partner mới
                    country = False
                    if country_code:
                        country = self.env['res.country'].search([('code', '=', country_code)], limit=1)

                    partner_vals = {
                        'name': visitor_name,
                        'email': visitor_email,
                        'phone': visitor_phone,
                        'country_id': country.id if country else False,
                        'customer_rank': 1,
                    }
                    partner = self.env['res.partner'].create(partner_vals)

                partner_id = partner.id

        if not partner_id:
            raise ValidationError(_("Không thể xác định hoặc tạo khách hàng."))

        partner = self.env['res.partner'].browse(partner_id)

        # 6. Kiểm tra email đã đăng ký khóa học này chưa (nếu là đăng ký từ website)
        if is_website_enrollment and visitor_email:
            existing_enrollment = self.search(
                [
                    ('course_id', '=', course.id),
                    ('visitor_email', '=', visitor_email),
                    ('state', 'not in', ['cancelled', 'refunded'])
                ],
                limit=1,
            )
            if existing_enrollment:
                raise ValidationError(_(
                    "Email %s đã được sử dụng để đăng ký khóa học này và đang ở trạng thái '%s'."
                ) % (visitor_email, existing_enrollment.state))

        # 7. Kiểm tra trùng lịch học với các khóa học khác (nếu có force_ignore_schedule_conflicts=True thì bỏ qua)
        force_ignore_conflicts = vals.get('force_ignore_schedule_conflicts', False)
        if is_website_enrollment and not force_ignore_conflicts:
            schedule_utils = self.env["eb.lesson.schedule.utils"]
            conflict_check = schedule_utils.check_student_schedule_conflicts(
                student_email=visitor_email,
                student_phone=visitor_phone,
                course_id=course.id
            )

            if conflict_check.get('has_conflicts'):
                conflicts = conflict_check.get('conflicts', [])
                conflict_details = []
                for conflict in conflicts[:3]:  # Chỉ hiển thị 3 xung đột đầu tiên
                    conflict_details.append(
                        f"• {conflict['existing_course_name']}: "
                        f"{conflict['existing_lesson']['lesson_name']} "
                        f"({conflict['existing_lesson']['start_datetime'].strftime('%d/%m/%Y %H:%M')} - "
                        f"{conflict['existing_lesson']['end_datetime'].strftime('%H:%M')})"
                    )

                conflict_message = _(
                    "Phát hiện trùng lịch học với các khóa học khác:\n\n%s\n\n"
                    "Bạn có chắc chắn muốn tiếp tục đăng ký không? "
                    "Để tiếp tục, vui lòng xác nhận trong bước tiếp theo."
                ) % '\n'.join(conflict_details)

                # Trả về thông tin xung đột để frontend xử lý
                return {
                    'schedule_conflicts': True,
                    'conflict_message': conflict_message,
                    'conflicts': conflicts,
                    'course_id': course.id,
                    'visitor_email': visitor_email,
                    'visitor_phone': visitor_phone,
                }

        # 8. Lấy thông tin giá từ các trường compute đã có
        price_total_incl_tax = course.discounted_price if course.has_discount else course.original_price
        price_total_excl_tax = course.discounted_price_untaxed if course.has_discount else course.original_price_untaxed

        # 9. Tạo đăng ký khóa học
        enrollment_vals = {
            'course_id': course.id,
            'enrollment_date': fields.Datetime.now(),
            'payment_amount': price_total_incl_tax,  # Vẫn lưu giá bao gồm thuế để hiển thị
            'notes': notes,
            'state': 'unpaid',
            'opportunity_id': vals.get('opportunity_id', False),  # Thêm opportunity_id nếu có
            'partner_id': partner_id,  # Lưu partner_id vào enrollment
        }

        # Thêm class_id và selected_location_id nếu có
        if vals.get('class_id'):
            enrollment_vals['class_id'] = vals.get('class_id')
        if vals.get('selected_location_id'):
            enrollment_vals['selected_location_id'] = vals.get('selected_location_id')

        if is_website_enrollment:
            enrollment_vals.update({
                'is_website_enrollment': True,
                'visitor_name': visitor_name,
                'visitor_email': visitor_email,
                'visitor_phone': visitor_phone,
            })
        elif student_id:
            enrollment_vals['student_id'] = student_id

        # Tạo enrollment trước
        enrollment = self.create(enrollment_vals)

        # 10. Tạo đơn hàng
        default_team = self.env['crm.team']._get_default_team_id(user_id=False)
        order_vals = {
            'partner_id': partner_id,
            'enrollment_id': enrollment.id,
            'pricelist_id': course.pricelist_id.id if course.pricelist_id else False,
            'team_id': default_team.id if default_team else False,
            'order_line': [
                (
                    0,
                    0,
                    {
                        'product_id': product.id,
                        'product_uom_qty': 1,
                        'price_unit': course.original_price_untaxed,  # Sử dụng giá chưa bao gồm thuế
                        'discount': course.discount_percentage if course.has_discount else 0.0,
                        'name': f"Đăng ký khóa học: {course.name}",
                    },
                )
            ],
        }

        sale_order = self.env['sale.order'].create(order_vals)

        # 9. Liên kết đơn hàng với đăng ký và cập nhật enrollment_number
        enrollment.write({
            'sale_order_id': sale_order.id,
            'enrollment_number': sale_order.name  # Sử dụng mã đơn hàng làm enrollment_number
        })
        _logger.info(f"Linked sale order {sale_order.name} to enrollment {enrollment.id} and updated enrollment_number")

        # 11. Tạo giao dịch thanh toán
        # Xác định payment provider
        payment_provider = False

        # Nếu có payment_provider_id trong vals, sử dụng nó
        if vals.get('payment_provider_id'):
            payment_provider = self.env['payment.provider'].sudo().browse(vals.get('payment_provider_id'))
            if not payment_provider.exists() or payment_provider.state not in ['enabled', 'test']:
                payment_provider = False

        # Nếu không có payment_provider_id hoặc không tìm thấy, sử dụng Sepay hoặc bất kỳ provider nào
        if not payment_provider:
            # Ưu tiên tìm Sepay
            payment_provider = self.env['payment.provider'].sudo().search(
                [('code', '=', 'sepay'), ('state', 'in', ['enabled', 'test'])],
                limit=1
            )

            if not payment_provider:
                # Nếu không tìm thấy provider cụ thể, tìm bất kỳ provider nào đang enabled
                payment_provider = self.env['payment.provider'].sudo().search(
                    [('state', 'in', ['enabled', 'test'])],
                    limit=1
                )

        transaction = False
        student_payment = False

        if payment_provider:
            # Sử dụng mã đơn hàng làm tham chiếu thanh toán
            payment_reference = sale_order.name

            # Xác định payment method
            payment_method_id = False

            # Nếu có payment_method_id trong vals, sử dụng nó
            if vals.get('payment_method_id'):
                payment_method = self.env['payment.method'].sudo().browse(vals.get('payment_method_id'))
                if payment_method.exists() and payment_provider.id in payment_method.provider_ids.ids:
                    payment_method_id = payment_method.id

            # Nếu không có payment_method_id hoặc không tìm thấy, sử dụng logic cũ
            if not payment_method_id:
                if payment_provider.code == 'sepay':
                    qr_payment_method = self.env["payment.method"].sudo().search(
                        [("code", "=", "qr_code"), ("provider_ids", "in", [payment_provider.id])],
                        limit=1,
                    )
                    if qr_payment_method:
                        payment_method_id = qr_payment_method.id
                else:
                    # Tìm phương thức thanh toán mặc định cho provider này
                    payment_method = self.env["payment.method"].sudo().search(
                        [("provider_ids", "in", [payment_provider.id])],
                        limit=1,
                    )
                    if payment_method:
                        payment_method_id = payment_method.id

            # Tạo giao dịch thanh toán
            transaction_vals = {
                'provider_id': payment_provider.id,
                'reference': payment_reference,
                'amount': price_total_incl_tax,
                'currency_id': course.currency_id.id,
                'partner_id': partner_id,
                'sale_order_ids': [(4, sale_order.id)],
                'state': 'draft',
            }

            # Thêm payment_method_id nếu có
            if payment_method_id:
                transaction_vals['payment_method_id'] = payment_method_id

            transaction = self.env['payment.transaction'].create(transaction_vals)

            # Tạo bản ghi thanh toán
            student_payment_vals = {
                'course_id': course.id,
                'enrollment_id': enrollment.id,
                'payment_date': fields.Date.context_today(self),
                'amount': price_total_incl_tax,
                'reference_number': payment_reference,
                'transaction_id': transaction.id,
                'state': 'unpaid',
                'notes': _("Tạo từ đăng ký khóa học"),
            }

            # Thêm payment_method_id nếu có
            if payment_method_id:
                student_payment_vals['payment_method_id'] = payment_method_id

            # Thêm payment_provider_id
            if payment_provider:
                student_payment_vals['payment_provider_id'] = payment_provider.id

            # Xử lý student_id
            if student_id:
                # Nếu đã có student_id, sử dụng nó
                student_payment_vals['student_id'] = student_id
            elif is_website_enrollment and visitor_email:
                # KHÔNG tìm và liên kết học viên hiện có cho website enrollment
                # Student và user sẽ được tạo khi thanh toán thành công
                _logger.info(f"Website enrollment {enrollment.id} created without student - will create after payment")

            student_payment = self.env['eb.student.payment'].create(student_payment_vals)

            # Không tự động xác nhận thanh toán, để admin xác nhận thủ công sau

        # 11. Xử lý hóa đơn (invoice) cho tất cả các trường hợp
        invoice = None

        # Kiểm tra xem đã có hóa đơn từ đơn hàng chưa
        if sale_order.invoice_ids:
            invoice = sale_order.invoice_ids.sorted("id", reverse=True)[0]
            _logger.info(f"Using existing invoice {invoice.name} for SO {sale_order.name}")
        else:
            # Xác nhận đơn hàng nếu chưa được xác nhận
            if sale_order.state == 'draft':
                sale_order.action_confirm()
                _logger.info(f"Confirmed sale order {sale_order.name}")

            # Tạo hóa đơn từ đơn hàng
            try:
                invoice = sale_order._create_invoices()
                if invoice:
                    _logger.info(f"Created invoice {invoice.name} for sale order {sale_order.name}")

                    # Không tự động xác nhận hóa đơn, để admin xác nhận thủ công sau

                    # Liên kết hóa đơn với giao dịch thanh toán
                    if transaction and not transaction.invoice_ids:
                        transaction.invoice_ids = [(4, invoice.id)]
                        _logger.info(f"Linked invoice {invoice.name} to transaction {transaction.reference}")

                        # Cập nhật invoice_id trong student_payment
                        if student_payment:
                            student_payment.update_invoice_from_transaction()
                            _logger.info(f"Updated student payment {student_payment.name} with invoice from transaction")
            except Exception as e:
                _logger.error(f"Failed to create invoice for sale order {sale_order.name}: {str(e)}")

        # Đặt trạng thái giao dịch thành pending nếu chưa
        if transaction and transaction.state == 'draft':
            transaction._set_pending()
            _logger.info(f"Set Payment Transaction {transaction.reference} (ID: {transaction.id}) to pending state")

        # 12. Gửi email xác nhận đăng ký (chỉ khi đăng ký từ wizard, không gửi khi đăng ký từ API)
        # Kiểm tra xem đăng ký có phải từ wizard không bằng cách kiểm tra context
        if self.env.context.get('from_enrollment_wizard', False):
            try:
                enrollment.send_enrollment_confirmation_email()
                _logger.info(f"Sent enrollment confirmation email for enrollment {enrollment.id}")
            except Exception as e:
                _logger.error(f"Failed to send enrollment confirmation email: {e}")

        # 13. Trả về kết quả
        result = {
            'enrollment': {
                'id': enrollment.id,
                'enrollment_number': enrollment.enrollment_number,
                'state': enrollment.state,
            },
            'sale_order': {
                'id': sale_order.id,
                'name': sale_order.name,
            },
        }

        if transaction:
            result['payment'] = {
                'transaction_id': transaction.id,
                'reference': transaction.reference,
                'state': transaction.state,
                'amount': transaction.amount,
            }

        if student_payment:
            result['student_payment'] = {
                'id': student_payment.id,
                'name': student_payment.name,
                'state': student_payment.state,
            }

        if invoice:
            result['invoice'] = {
                'id': invoice.id,
                'name': invoice.name,
                'state': invoice.state,
            }

        return result

    @api.model
    def _cron_cleanup_expired_enrollments(self):
        """
        Cron job để cleanup các enrollment đã hết hạn.
        Dựa vào sepay_expiration_time của transaction để xác định thời gian hết hạn.
        Chuyển trạng thái sang 'cancelled' thay vì xóa để giữ lại data.
        """
        current_time = fields.Datetime.now()

        # Tìm các enrollment đã hết hạn dựa vào sepay_expiration_time
        expired_enrollments = self.search([
            ('state', '=', 'unpaid'),
            ('student_id', '=', False),  # Chưa tạo student (chưa thanh toán)
            ('sale_order_id', '!=', False),  # Có sale order
            ('sale_order_id.transaction_ids', '!=', False),  # Có transaction
            ('sale_order_id.transaction_ids.provider_code', '=', 'sepay'),  # Sepay transaction
            ('sale_order_id.transaction_ids.sepay_expiration_time', '<', current_time),  # Đã hết hạn
        ])

        if expired_enrollments:
            # Chuyển sang trạng thái cancelled thay vì xóa
            expired_enrollments.write({
                'state': 'cancelled',
                'cancellation_date': fields.Datetime.now(),
                'cancellation_reason': 'Hết thời gian giao dịch',
            })

            # Ghi log
            _logger.info(f"Cancelled {len(expired_enrollments)} expired enrollments based on sepay_expiration_time: {expired_enrollments.ids}")

            # Gửi thông báo cho từng enrollment
            for enrollment in expired_enrollments:
                # Lấy thông tin expiration time từ transaction
                transaction = enrollment.sale_order_id.transaction_ids.filtered(
                    lambda t: t.provider_code == 'sepay' and t.sepay_expiration_time
                )[:1]

                expiration_info = ""
                if transaction and transaction.sepay_expiration_time:
                    expiration_info = f" (hết hạn lúc {transaction.sepay_expiration_time.strftime('%d/%m/%Y %H:%M')})"

                enrollment.message_post(
                    body=f"Đăng ký đã được tự động hủy do hết hạn thanh toán{expiration_info}",
                    message_type='comment',
                    subtype_xmlid='mail.mt_note',
                )

                # Gửi email thông báo hủy đăng ký
                try:
                    enrollment.send_enrollment_cancellation_email()
                except Exception as e:
                    _logger.error(f"Không thể gửi email thông báo hủy đăng ký tự động {enrollment.enrollment_number}: {str(e)}")

        return len(expired_enrollments)

    @api.model
    def _cron_send_enrollment_reminders(self):
        """
        Cron job để gửi nhắc nhở hoàn tất đăng ký cho enrollment chưa thanh toán.
        Dựa vào sepay_expiration_time để xác định thời gian nhắc nhở.
        """
        # Lấy cấu hình thời gian nhắc nhở (mặc định 15 phút trước khi hết hạn)
        reminder_minutes_before_expiry = int(self.env['ir.config_parameter'].sudo().get_param(
            'eb_lms.enrollment_reminder_minutes', '15'
        ))

        # Tính thời gian
        now = fields.Datetime.now()
        reminder_threshold = now + timedelta(minutes=reminder_minutes_before_expiry)

        # Tìm enrollment cần nhắc nhở dựa vào sepay_expiration_time
        enrollments_to_remind = self.search([
            ('state', '=', 'unpaid'),  # Chưa thanh toán
            ('student_id', '=', False),  # Chưa tạo student (chưa thanh toán)
            ('reminder_sent', '=', False),  # Chưa gửi reminder
            ('sale_order_id', '!=', False),  # Có sale order
            ('sale_order_id.transaction_ids', '!=', False),  # Có transaction
            ('sale_order_id.transaction_ids.provider_code', '=', 'sepay'),  # Sepay transaction
            ('sale_order_id.transaction_ids.sepay_expiration_time', '>', now),  # Chưa hết hạn
            ('sale_order_id.transaction_ids.sepay_expiration_time', '<=', reminder_threshold),  # Trong thời gian nhắc nhở
            '|',
            ('student_id.email', '!=', False),  # Có email student
            ('visitor_email', '!=', False),  # Hoặc có email visitor
        ])

        if not enrollments_to_remind:
            _logger.info("Không có enrollment nào cần gửi nhắc nhở dựa trên sepay_expiration_time")
            return 0

        # Gửi nhắc nhở cho từng enrollment
        success_count = 0
        for enrollment in enrollments_to_remind:
            try:
                # Lấy thông tin expiration time từ transaction
                transaction = enrollment.sale_order_id.transaction_ids.filtered(
                    lambda t: t.provider_code == 'sepay' and t.sepay_expiration_time
                )[:1]

                # Gửi nhắc nhở thanh toán sử dụng layout từ eb_mail
                mail_utils = self.env['eb.lms.mail.utils']
                if mail_utils.send_payment_reminder(enrollment.id):
                    enrollment.reminder_sent = True
                    success_count += 1

                    expiration_info = ""
                    if transaction and transaction.sepay_expiration_time:
                        expiration_info = f" (hết hạn lúc {transaction.sepay_expiration_time.strftime('%d/%m/%Y %H:%M')})"

                    _logger.info(f"Đã gửi nhắc nhở cho enrollment {enrollment.enrollment_number}{expiration_info}")
                else:
                    _logger.warning(f"Không thể gửi nhắc nhở cho enrollment {enrollment.enrollment_number}")

            except Exception as e:
                _logger.error(f"Lỗi khi gửi nhắc nhở cho enrollment {enrollment.enrollment_number}: {str(e)}")

        _logger.info(f"Đã gửi nhắc nhở thành công cho {success_count}/{len(enrollments_to_remind)} enrollment dựa trên sepay_expiration_time")
        return success_count

    # Các phương thức _cron_process_pending_enrollments, _cron_process_pending_enrollments_job
    # và _process_pending_enrollment đã được loại bỏ vì không còn cần thiết

    def send_new_student_account_notification(self, password=None):
        """Gửi thông báo tài khoản mới cho học viên sử dụng layout từ eb_mail."""
        self.ensure_one()

        try:
            # Sử dụng tiện ích mail từ eb_lms.mail.utils
            return self.env['eb.lms.mail.utils'].send_new_student_account_notification(self.id, password)
        except Exception as e:
            _logger.error(f"Failed to send new student account notification: {e}")

        return True
    # Đăng ký được tạo với trạng thái mặc định là 'unpaid' (chờ thanh toán)
    # và không cần thêm bước xác nhận đăng ký
