# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class LessonQuiz(models.Model):
    """
    Junction model để quản lý mối quan hệ nhiều-nhiều giữa Lesson và Quiz.
    
    Cho phép:
    - 1 buổi học có nhiều quiz
    - 1 quiz có thể được sử dụng cho nhiều buổi học
    - Quản lý trạng thái publish/unpublish của quiz trong từng buổi học
    """
    
    _name = "eb.lesson.quiz"
    _description = "Lesson Quiz Junction"
    _rec_name = "display_name"
    _order = "lesson_id, sequence, published_at desc"
    
    # Core relationships
    lesson_id = fields.Many2one(
        "eb.lesson.lesson",
        string="Buổi học",
        required=True,
        ondelete="cascade",
        index=True,
        help="Buổi học mà quiz này được gán vào",
    )
    
    quiz_id = fields.Many2one(
        "eb.quiz",
        string="Quiz",
        required=True,
        ondelete="cascade", 
        index=True,
        help="Quiz được gán cho buổi học",
    )
    
    # Publishing control
    is_active = fields.Boolean(
        string="Đang hoạt động",
        default=False,
        help="Quiz có đang được công bố cho học viên không",
    )
    
    published_at = fields.Datetime(
        string="Thời gian công bố",
        help="Thời gian quiz được công bố cho học viên",
    )
    
    published_by = fields.Many2one(
        "res.users",
        string="Người công bố",
        help="Người dùng đã công bố quiz này",
    )
    
    unpublished_at = fields.Datetime(
        string="Thời gian hủy công bố",
        help="Thời gian quiz bị hủy công bố",
    )
    
    unpublished_by = fields.Many2one(
        "res.users", 
        string="Người hủy công bố",
        help="Người dùng đã hủy công bố quiz này",
    )
    
    # Ordering and metadata
    sequence = fields.Integer(
        string="Thứ tự",
        default=10,
        help="Thứ tự hiển thị quiz trong buổi học",
    )
    
    notes = fields.Text(
        string="Ghi chú",
        help="Ghi chú về việc sử dụng quiz trong buổi học này",
    )
    
    # Related fields for easy access
    lesson_name = fields.Char(
        related="lesson_id.name",
        string="Tên buổi học",
        store=True,
        readonly=True,
    )
    
    quiz_name = fields.Char(
        related="quiz_id.name", 
        string="Tên quiz",
        store=True,
        readonly=True,
    )
    
    lesson_stage = fields.Char(
        related="lesson_id.stage_id.code",
        string="Trạng thái buổi học",
        store=True,
        readonly=True,
    )
    
    lesson_start_datetime = fields.Datetime(
        related="lesson_id.start_datetime",
        string="Thời gian bắt đầu buổi học",
        store=True,
        readonly=True,
    )
    
    lesson_teacher_id = fields.Many2one(
        related="lesson_id.teacher_id",
        string="Giảng viên",
        store=True,
        readonly=True,
    )
    
    quiz_instructor_id = fields.Many2one(
        related="quiz_id.instructor_id",
        string="Người tạo quiz",
        store=True,
        readonly=True,
    )
    
    # Computed fields
    display_name = fields.Char(
        string="Tên hiển thị",
        compute="_compute_display_name",
        store=True,
        help="Tên hiển thị của lesson-quiz relationship",
    )
    
    can_publish = fields.Boolean(
        string="Có thể công bố",
        compute="_compute_can_publish",
        help="Quiz có thể được công bố không",
    )
    
    can_unpublish = fields.Boolean(
        string="Có thể hủy công bố",
        compute="_compute_can_unpublish",
        help="Quiz có thể được hủy công bố không",
    )

    status = fields.Selection(
        [
            ('inactive', 'Chưa công bố'),
            ('active', 'Đang hoạt động'),
        ],
        string="Trạng thái",
        compute="_compute_status",
        store=True,
        help="Trạng thái công bố của quiz",
    )
    
    @api.depends("lesson_name", "quiz_name", "is_active", "published_at")
    def _compute_display_name(self):
        for record in self:
            lesson_name = record.lesson_name or "Không có buổi học"
            quiz_name = record.quiz_name or "Không có quiz"

            status = "🟢 Đang hoạt động" if record.is_active else "⚪ Chưa công bố"

            if record.published_at:
                time_str = record.published_at.strftime("%d/%m %H:%M")
                status += f" ({time_str})"

            record.display_name = f"{lesson_name} - {quiz_name} - {status}"
    
    @api.depends("lesson_stage", "is_active")
    def _compute_can_publish(self):
        for record in self:
            # Chỉ có thể publish nếu:
            # 1. Quiz chưa được publish
            # 2. Buổi học chưa hoàn thành (scheduled hoặc in_progress)
            record.can_publish = (
                not record.is_active and
                record.lesson_stage in ['scheduled', 'in_progress']
            )

    @api.depends("is_active", "lesson_stage")
    def _compute_can_unpublish(self):
        for record in self:
            # Có thể unpublish nếu:
            # 1. Quiz đang được publish
            # 2. Buổi học chưa hoàn thành
            record.can_unpublish = (
                record.is_active and
                record.lesson_stage in ['scheduled', 'in_progress']
            )

    @api.depends("is_active")
    def _compute_status(self):
        for record in self:
            record.status = 'active' if record.is_active else 'inactive'

    @api.onchange("lesson_id")
    def _onchange_lesson_id(self):
        """Filter quiz domain khi chọn lesson."""
        if self.lesson_id:
            domain = [("state", "=", "ready")]

            # Filter theo class nếu lesson có class
            if self.lesson_id.class_id:
                domain.append(("class_id", "in", [False, self.lesson_id.class_id.id]))

            # Filter theo subject nếu lesson có subject
            if self.lesson_id.subject_id:
                domain.append(("subject_id", "in", [False, self.lesson_id.subject_id.id]))

            return {"domain": {"quiz_id": domain}}
        else:
            return {"domain": {"quiz_id": [("state", "=", "ready")]}}
    
    @api.constrains("lesson_id", "quiz_id")
    def _check_unique_lesson_quiz(self):
        """Đảm bảo không có duplicate lesson-quiz combination."""
        for record in self:
            existing = self.search([
                ("lesson_id", "=", record.lesson_id.id),
                ("quiz_id", "=", record.quiz_id.id),
                ("id", "!=", record.id),
            ])
            if existing:
                raise ValidationError(
                    f"Quiz '{record.quiz_name}' đã được gán cho buổi học '{record.lesson_name}' rồi."
                )
    
    @api.constrains("lesson_id", "quiz_id")
    def _check_quiz_scope(self):
        """Đảm bảo quiz thuộc đúng scope của lesson."""
        for record in self:
            lesson = record.lesson_id
            quiz = record.quiz_id
            
            # Kiểm tra class_id
            if quiz.class_id and lesson.class_id != quiz.class_id:
                raise ValidationError(
                    f"Quiz '{quiz.name}' thuộc lớp '{quiz.class_id.name}' "
                    f"không khớp với lớp của buổi học '{lesson.class_id.name}'"
                )
            
            # Kiểm tra subject_id
            if quiz.subject_id and lesson.subject_id != quiz.subject_id:
                raise ValidationError(
                    f"Quiz '{quiz.name}' thuộc môn '{quiz.subject_id.name}' "
                    f"không khớp với môn của buổi học '{lesson.subject_id.name}'"
                )
    
    @api.constrains("is_active", "lesson_stage")
    def _check_publish_timing(self):
        """Đảm bảo chỉ publish quiz cho lesson chưa kết thúc."""
        for record in self:
            if record.is_active and record.lesson_stage in ['completed', 'cancelled', 'rescheduled']:
                raise ValidationError(
                    f"Không thể công bố quiz cho buổi học đã kết thúc "
                    f"(trạng thái: {record.lesson_stage})"
                )
    
    def action_publish(self):
        """Công bố quiz cho học viên."""
        for record in self:
            if not record.can_publish:
                quiz_name = record.quiz_id.name if record.quiz_id else "Unknown Quiz"
                lesson_name = record.lesson_id.name if record.lesson_id else "Unknown Lesson"
                raise ValidationError(
                    f"Không thể công bố quiz '{quiz_name}' "
                    f"cho buổi học '{lesson_name}'"
                )
            
            record.write({
                "is_active": True,
                "published_at": fields.Datetime.now(),
                "published_by": self.env.user.id,
                "unpublished_at": False,
                "unpublished_by": False,
            })
        
        return True
    
    def action_unpublish(self):
        """Hủy công bố quiz."""
        for record in self:
            if not record.can_unpublish:
                quiz_name = record.quiz_id.name if record.quiz_id else "Unknown Quiz"
                lesson_name = record.lesson_id.name if record.lesson_id else "Unknown Lesson"
                raise ValidationError(
                    f"Không thể hủy công bố quiz '{quiz_name}' "
                    f"cho buổi học '{lesson_name}'"
                )
            
            record.write({
                "is_active": False,
                "unpublished_at": fields.Datetime.now(),
                "unpublished_by": self.env.user.id,
            })
        
        return True
