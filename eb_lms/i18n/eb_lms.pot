# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* eb_lms
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-12 07:52+0000\n"
"PO-Revision-Date: 2025-04-12 07:52+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid ""
"\n"
"                        <p>Dear %s,</p>\n"
"                        <p>This is a reminder that the course <strong>%s</strong> will start on <strong>%s</strong>.</p>\n"
"                        <p>Please make sure you have completed all prerequisites.</p>\n"
"                        <p>Thank you!</p>\n"
"                        "
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "%s - New Class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid ""
"<i class=\"fa fa-info-circle\"/> Expected schedules allow students to choose their preferred time slots when enrolling in this course. \n"
"                                This helps in planning classes and optimizing resources."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"to\" title=\"to\"/>"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "<span class=\"o_stat_text\">Rating</span>"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_attendance_makeup_unique_makeup
msgid ""
"A makeup request already exists for this attendance record and makeup "
"lesson!"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_enrollment_student_course_unique
msgid "A student cannot enroll in the same course twice in the same state!"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_subject_course_subject_unique
msgid "A subject can only be added once to a course!"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_attendance_attendance_unique_attendance
msgid "A user can only have one attendance record per lesson for each role!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__absence_document
msgid "Absence Document"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__absent
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__default_status__absent
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Absent"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_needaction
msgid "Action Needed"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__status__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__status__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__active
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Active"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_ids
msgid "Activities"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_state
msgid "Activity State"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Add Subject"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Add Subjects"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_add_subject_wizard
msgid "Add Subjects to Course Wizard"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_form
msgid "Add a description for this content..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Add notes here..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Add tags..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Additional Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__description
msgid "Additional description for this subject in the context of the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__notes
msgid "Additional notes"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Additional notes about attendance status..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__status_note
msgid "Additional notes about the attendance status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__notes
msgid "Additional notes about the enrollment"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Additional notes about the makeup request..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__note
msgid "Additional notes about this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__notes
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Additional notes about this student's enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__notes
msgid "Additional notes about this teacher's assignment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__address
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__address
msgid "Address"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__admin
msgid "Admin Dashboard"
msgstr ""

#. module: eb_lms
#: model:res.groups,name:eb_lms.group_eb_lms_administrator
msgid "Administrator"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__age
msgid "Age"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__is_all_day
msgid "All Day"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__all
msgid "All Methods"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_face_recognition
msgid "Allow Face Recognition"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_fingerprint
msgid "Allow Fingerprint"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_makeup
msgid "Allow Makeup"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_manual
msgid "Allow Manual"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_qr_code
msgid "Allow QR Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_qr_code
msgid "Allow QR code check-in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_face_recognition
msgid "Allow face recognition check-in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_fingerprint
msgid "Allow fingerprint check-in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_makeup
msgid "Allow makeup classes"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_manual
msgid "Allow manual check-in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allowed_check_in_methods
msgid "Allowed Check-in Methods"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Allowed Methods"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__amount
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__amount
msgid "Amount"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__paid_amount
msgid "Amount already paid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__balance_amount
msgid "Amount still to be paid"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_class_teacher_instructor_class_role_uniq
msgid "An instructor can have each role only once in a class!"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_analytics
msgid "Analytics"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Approve"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_makeup__status__approved
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Approved"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Archived"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__assessment_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Assessment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__assessment_methods
msgid "Assessment Methods"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__assessment_type
msgid "Assessment Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__assessment_id
msgid "Assessment for this lesson"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
msgid "Assign Class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__class_id
msgid "Assigned Class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__assignment_id
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__assignment
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__assignment
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Assignment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__assignment_id
msgid "Assignment for this content item"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__assistant
msgid "Assistant"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__associate
msgid "Associate"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__attachment_id
msgid "Attachment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__attachment_ids
msgid "Attachments"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__attachment_ids
msgid "Attachments related to this qualification"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_attendance
#: model:ir.ui.menu,name:eb_lms.menu_attendance
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_calendar
msgid "Attendance"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_graph
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_pivot
msgid "Attendance Analysis"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__attendance_count
msgid "Attendance Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__attendance_date
msgid "Attendance Date"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_attendance_makeup
msgid "Attendance Makeup Request"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__attendance_rate
msgid "Attendance Rate"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__attendance_rate
msgid "Attendance Rate (%)"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_attendance_settings
#: model:ir.model,name:eb_lms.model_eb_attendance_settings
#: model:ir.ui.menu,name:eb_lms.menu_attendance_settings
msgid "Attendance Settings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__attendance_rate
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__attendance_rate
msgid "Attendance rate for this student"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance
msgid ""
"Attendance records are created automatically for scheduled lessons.\n"
"                You can manually mark attendance as present, absent, late, or excused."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__status
msgid "Attendance status"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/student.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__attendance_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Attendances"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__auto_create_attendance_minutes
msgid "Auto Create Attendance (minutes before)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__rating_avg
msgid "Average Rating"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__grade_average
msgid "Average grade for this student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__rating_avg
msgid "Average rating of this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__award
msgid "Award"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__bachelor
msgid "Bachelor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__balance_amount
msgid "Balance Amount"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bank_account_holder
msgid "Bank Account Holder"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bank_account_number
msgid "Bank Account Number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bank_name
msgid "Bank Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__bank_transfer
msgid "Bank Transfer"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Banking Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__bar
msgid "Bar Chart"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Basic Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bio
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Biography"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__short_description
msgid "Brief description for course listings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__short_description
msgid "Brief description of the class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Brief description of the class (max 200 chars)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__lesson_id
msgid "Buổi học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__is_mandatory
msgid "Bắt buộc"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__calendar
msgid "Calendar"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Cancel"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Cancel Class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Cancel Lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__cancellation_date
msgid "Cancellation Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__cancellation_reason
msgid "Cancellation Reason"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__cancelled
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Cancelled"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Cannot check out without checking in first."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Capacity"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__cash
msgid "Cash"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__category_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__category_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__category_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__category_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Category"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Category Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__code
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__code
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__code
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__code
msgid "Category code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__color
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__color
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__color
msgid "Category color for UI"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__description
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__description
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__description
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__description
msgid "Category description"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__name
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__name
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__name
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__name
msgid "Category name"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_category_name_parent_uniq
#: model:ir.model.constraint,message:eb_lms.constraint_eb_subject_category_name_parent_uniq
msgid "Category name must be unique within the same parent category!"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_category_name_uniq
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_skill_category_name_uniq
msgid "Category name must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__category_id
msgid "Category of the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__certificate
msgid "Certificate"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__chart
msgid "Chart"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__chart_type
msgid "Chart Type"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Check Out"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Check-in Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__check_in_method
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Check-in Method"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Check-in Methods"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__check_in_time
msgid "Check-in Time"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__check_out_time
msgid "Check-out Time"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Check-out time cannot be earlier than check-in time."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__child_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__child_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__child_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__child_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Child Categories"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__child_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__child_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__child_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__child_ids
msgid "Child categories"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Choose a color"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__subject_ids
msgid "Chọn các môn học để thêm vào khóa học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__class_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__class_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__class_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__class_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__code
msgid "Class Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__class_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__class_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__class_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__class_count
msgid "Class Count"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_class_student
msgid "Class Enrollments"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__name
msgid "Class Name"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_class_student
msgid "Class Student"
msgstr ""



#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__class_id
msgid "Class assigned to the student after enrollment confirmation"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_class_class_code_unique
msgid "Class code must be unique!"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_model.py:0
msgid "Class end date must be after start date."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__image
msgid "Class image"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__class_id
msgid "Class related to this rating"
msgstr ""



#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__class_id
msgid "Class the student is enrolled in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__class_id
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__makeup_class_id
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__original_class_id
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__class_id
msgid "Class this lesson belongs to"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: code:addons/eb_lms/models/people/instructor.py:0
#: code:addons/eb_lms/models/people/student.py:0
#: code:addons/eb_lms/models/subject/subject.py:0
#: model:ir.actions.act_window,name:eb_lms.action_class
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__class_ids
#: model:ir.ui.menu,name:eb_lms.menu_class
#: model:ir.ui.menu,name:eb_lms.menu_lms_class
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_calendar
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Classes"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__class_ids
msgid "Classes for this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__class_ids
msgid "Classes related to this payment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__class_ids
msgid "Classes taught by this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__class_ids
msgid "Classes that teach this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__class_ids
msgid "Classes the student is enrolled in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__code
msgid "Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__college
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__college
msgid "College"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__color
msgid "Color"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__color
msgid "Color Index"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__col_size
msgid "Column Size"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__schedule_time
msgid "Combined start and end time for display in list view"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__comment
msgid "Comment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__company
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__company_id
msgid "Company"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__company_id
msgid "Company that owns this class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__company_id
msgid "Company that owns this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__company_id
msgid "Company that owns this instructor record"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__company_id
msgid "Company that owns this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__company_id
msgid "Company that owns this student record"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__company_id
msgid "Company that owns this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__company_id
msgid "Company this enrollment belongs to"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__company_id
msgid "Company this schedule belongs to"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Complete"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Complete Class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Complete Lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__completed
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Completed"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__completion_date
msgid "Completion Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__completion_rate
msgid "Completion Rate (%)"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_configuration
msgid "Configuration"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_settings
msgid ""
"Configure attendance settings for your LMS system,\n"
"                including check-in methods, time thresholds, and makeup class policies."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Confirm"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__confirm_date
msgid "Confirmation Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__confirmed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__confirmed
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
msgid "Confirmed"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Contact Information"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Content"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__name
msgid "Content Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__content_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Content Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__content_ids
msgid "Content items for this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_lesson_content_name_lesson_unique
msgid "Content name must be unique within a lesson!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__employment_type__contractor
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Contractor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__conversion_date
msgid "Conversion Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__conversion_notes
msgid "Conversion Notes"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__converted
msgid "Converted"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/lead.py:0
msgid "Converted to student on %s"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__course_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Course"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_course_category
#: model:ir.ui.menu,name:eb_lms.menu_course_category
msgid "Course Categories"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_category
msgid "Course Category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__code
msgid "Course Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__course_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__course_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__course_count
msgid "Course Count"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_enrollment
msgid "Course Enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_schedule
msgid "Course Expected Schedule"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__image
msgid "Course Image"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Course Name"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Course Period"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_tag
msgid "Course Tag"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_course_tag
#: model:ir.ui.menu,name:eb_lms.menu_course_tag
msgid "Course Tags"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__course_id
msgid "Course being enrolled in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__category_id
msgid "Course category"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_course_code_unique
msgid "Course code must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__image
msgid "Course cover image"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid ""
"Course end date (%s) cannot be earlier than start date (%s) for course: %s"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Course reminders have been sent to all enrolled students."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__course_id
msgid "Course this class belongs to"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__course_id
msgid "Course this schedule belongs to"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_subject
msgid "Course-Subject Relationship"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_category.py:0
#: code:addons/eb_lms/models/course/course_tag.py:0
#: code:addons/eb_lms/models/subject/subject.py:0
#: model:ir.actions.act_window,name:eb_lms.action_course
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__course_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__course_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__course_ids
#: model:ir.ui.menu,name:eb_lms.menu_course
#: model:ir.ui.menu,name:eb_lms.menu_lms_course_config
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "Courses"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__course_ids
msgid "Courses in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__course_ids
msgid "Courses that include this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__course_interest_ids
msgid "Courses this lead is interested in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__course_ids
msgid "Courses with this tag"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Create New Class"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
msgid "Create Payment"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class
msgid ""
"Create classes for your courses and manage enrollments, schedules, and more."
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor
msgid ""
"Create instructors and assign them to classes and lessons.\n"
"                Track their qualifications, skills, and performance."
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_student
msgid ""
"Create students and track their progress across classes,\n"
"                monitor attendance, and manage their enrollments."
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class
msgid "Create your first class!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_course_category
msgid "Create your first course category!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_course_tag
msgid "Create your first course tag!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_course
msgid "Create your first course!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor_category
msgid "Create your first instructor category!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor_payment
msgid "Create your first instructor payment!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor
msgid "Create your first instructor!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_lesson_content
msgid "Create your first lesson content!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_lesson
msgid "Create your first lesson!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_student_payment
msgid "Create your first student payment!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_student
msgid "Create your first student!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_subject_category
msgid "Create your first subject category!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_subject_tag
msgid "Create your first subject tag!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_subject
msgid "Create your first subject!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__create_uid
msgid "Created by"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__create_date
msgid "Created on"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__credit_card
msgid "Credit Card"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__credits
msgid "Credits"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__currency_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__currency_id
msgid "Currency"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__current_students
msgid "Current Students"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__current_students
msgid "Current number of students in the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__custom
msgid "Custom"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__custom
msgid "Custom Dashboard"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__custom
msgid "Custom Range"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__dashboard_id
msgid "Dashboard"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__dashboard_item_ids
msgid "Dashboard Items"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__name
msgid "Dashboard Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__dashboard_type
msgid "Dashboard Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__model_id
msgid "Data Model"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__enrollment_date
msgid "Date and time of enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__cancellation_date
msgid "Date and time when enrollment was cancelled"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__confirm_date
msgid "Date and time when enrollment was confirmed"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__completion_date
msgid "Date and time when student completed the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__start_date
msgid "Date and time when student started the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__date_of_birth
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__date_of_birth
msgid "Date of Birth"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__attendance_date
msgid "Date of attendance"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__request_date
msgid "Date of request"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__enrollment_end_date
msgid "Date when enrollment ends"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__enrollment_start_date
msgid "Date when enrollment starts"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__end_date
msgid "Date when the class ends"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__start_date
msgid "Date when the class starts"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__end_date
msgid "Date when the course ends"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__start_date
msgid "Date when the course starts"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__join_date
msgid "Date when the instructor joined"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__end_date
msgid "Date when the instructor left"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__expiry_date
msgid "Date when the qualification expires"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__issue_date
msgid "Date when the qualification was issued"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__processed_date
msgid "Date when the request was processed"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__enrollment_date
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__enrollment_date
msgid "Date when the student enrolled"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__start_date
msgid "Date when the teacher starts teaching"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__end_date
msgid "Date when the teacher stops teaching"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__conversion_date
msgid "Date when this lead was converted to a student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__datetime_start
msgid "Datetime field for calendar view"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Day"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__weekdays
msgid "Days of Week"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__weekdays
msgid "Days of the week for this schedule"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__debit_card
msgid "Debit Card"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__default_status
msgid "Default Status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__degree
msgid "Degree"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__description
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Description"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__description
msgid "Description of the qualification"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__description
msgid "Description of this content item"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Detailed category description..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "Detailed description about the category..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "Detailed description about the tag..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__description
msgid "Detailed description of the class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid ""
"Detailed description of the class, including objectives, prerequisites, and "
"expectations..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__description
msgid "Detailed description of the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__interest_description
msgid "Detailed description of the lead's interests"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__description
msgid "Detailed description of the lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__description
msgid "Detailed description of the subject"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Detailed instructor biography..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__display_name
msgid "Display Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__name
msgid "Display name for the makeup request"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__doctorate
msgid "Doctorate"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__document
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Document"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__absence_document
msgid "Document reference for excused absence"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__domain
msgid "Domain Filter"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__doughnut
msgid "Doughnut Chart"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__draft
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Draft"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__dropped
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Dropped"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__dropped
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Dropped Out"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__duration
msgid "Duration (hours)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__duration
msgid "Duration (minutes)"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_schedule.py:0
msgid "Duration cannot exceed 6 hours."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__duration
msgid "Duration in hours"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__e_wallet
msgid "E-Wallet"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Education"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__education_level
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__education_level
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Education Level"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__email
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__email
msgid "Email"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Employment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__employment_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Employment Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__end_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__end_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__end_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__end_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "End Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__end_time
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__end_datetime
msgid "End Time"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__end_datetime
msgid "End date and time"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_teacher.py:0
msgid "End date must be after start date."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__end_time
msgid "End time for the classes (hours)"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
msgid "End time must be after start time."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_schedule.py:0
msgid "End time must be greater than start time."
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class_student
msgid ""
"Enroll students in your classes to track their progress and attendance."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_schedule.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__student_ids
msgid "Enrolled Students"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__enrollment_id
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__enrollment
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__enrollment_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__enrollment_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__enrollment_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Enrollment Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__enrollment_end_date
msgid "Enrollment End"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__enrollment_number
msgid "Enrollment Number"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Enrollment Period"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__enrollment_start_date
msgid "Enrollment Start"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_student.py:0
msgid "Enrollment date is required."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid ""
"Enrollment end date (%s) cannot be earlier than start date (%s) for course: "
"%s"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
msgid "Enter detailed description about this category..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Enter detailed description about this subject..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__error
msgid "Error"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__duration
msgid "Estimated duration in minutes to complete this content"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__exam
msgid "Exam"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Example: Basic Python Programming Course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__excused
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Excused"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Excused Absence"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__schedule_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Expected Schedules"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__schedule_ids
msgid "Expected schedules for this course that students can choose from"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__expiry_date
msgid "Expiry Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__external_link
msgid "External Link"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__external_link
msgid "External link for this content item"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__reference_number
msgid "External reference number (e.g. receipt number, transaction ID)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__face_recognition
msgid "Face Recognition"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__face_only
msgid "Face Recognition Only"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__gender__female
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__gender__female
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__gender__female
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Female"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__field_ids
msgid "Fields to Display"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__attachment_id
msgid "File attachment for this content item"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_finance
msgid "Finance"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__finance
msgid "Finance Dashboard"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__fingerprint
msgid "Fingerprint"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__fingerprint_only
msgid "Fingerprint Only"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_follower_ids
msgid "Followers"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__icon
msgid "Font Awesome icon class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__employment_type__full_time
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Full Time"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__gender
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__gender
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__gender
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Gender"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__role__instructor
msgid "Giảng viên"
msgstr ""

#. module: eb_lms
#: model:res.groups,comment:eb_lms.group_eb_lms_teacher
msgid ""
"Giảng viên có thể quản lý lớp học, môn học và buổi học mà họ phụ trách."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__grade_average
msgid "Grade Average"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__graduate
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__graduate
msgid "Graduate"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__graduated
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Graduated"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__graduation_year
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__graduation_year
msgid "Graduation Year"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__graph
msgid "Graph"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Group By"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__guest
msgid "Guest Lecturer"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__has_assessment
msgid "Has Assessment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__has_message
msgid "Has Message"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__height
msgid "Height (px)"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Hidden"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__high_school
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__high_school
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__high_school
msgid "High School"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__referral_source
msgid "How the lead heard about us"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__learning_type__hybrid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__location_type__hybrid
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Hybrid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__role__student
msgid "Học viên"
msgstr ""

#. module: eb_lms
#: model:res.groups,comment:eb_lms.group_eb_lms_student
msgid ""
"Học viên chỉ có thể xem thông tin khóa học, lớp học, môn học và buổi học mà "
"họ tham gia."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__user_id
msgid "Học viên/Giảng viên"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Hủy"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__id
msgid "ID"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__identification
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__identification
msgid "Identification"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__is_mandatory
msgid "If checked, this content is mandatory for the lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__is_mandatory
msgid "If checked, this subject is mandatory for the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__active
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__active
msgid "If unchecked, it will be hidden but still exists"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__active
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__active
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__active
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__active
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__active
msgid "If unchecked, it will be hidden from views"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__is_visible
msgid "If unchecked, this content will be hidden from students"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__image
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__image
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__image
msgid "Image"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__in_progress
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__in_progress
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__in_progress
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "In Progress"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__status__inactive
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__status__inactive
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__inactive
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Inactive"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__info
msgid "Information"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Inline Editing"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__institution
msgid "Institution"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__institution
msgid "Institution that issued the qualification"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__instructor_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
msgid "Instructor"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_instructor_category
#: model:ir.ui.menu,name:eb_lms.menu_instructor_category
msgid "Instructor Categories"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_category
msgid "Instructor Category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__code
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Instructor Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__instructor_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__instructor_count
msgid "Instructor Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__instructor
msgid "Instructor Dashboard"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Instructor Name"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_payment
msgid "Instructor Payment"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_instructor_payment
#: model:ir.ui.menu,name:eb_lms.menu_instructor_payment
msgid "Instructor Payments"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_qualification
msgid "Instructor Qualification"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_rating
msgid "Instructor Rating"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_skill
msgid "Instructor Skill"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_skill_category
msgid "Instructor Skill Category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__address
msgid "Instructor address"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__instructor_id
msgid "Instructor assigned to the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bank_account_holder
msgid "Instructor bank account holder name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bank_account_number
msgid "Instructor bank account number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bank_name
msgid "Instructor bank name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__instructor_id
msgid "Instructor being rated"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bio
msgid "Instructor biography"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__category_id
msgid "Instructor category"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_instructor_code_unique
msgid "Instructor code must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__date_of_birth
msgid "Instructor date of birth"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__email
msgid "Instructor email"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__employment_type
msgid "Instructor employment type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__instructor_id
msgid "Instructor for this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__gender
msgid "Instructor gender"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__identification
msgid "Instructor identification number (ID card, passport, etc.)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__mobile
msgid "Instructor mobile number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__phone
msgid "Instructor phone number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__image
msgid "Instructor profile image"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__qualification_ids
msgid "Instructor qualifications"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__rating_ids
msgid "Instructor ratings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__skill_ids
msgid "Instructor skills"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__status
msgid "Instructor status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__tax_code
msgid "Instructor tax code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__instructor_id
msgid "Instructor who has this qualification"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor_category.py:0
#: code:addons/eb_lms/models/people/instructor_skill.py:0
#: model:ir.actions.act_window,name:eb_lms.action_instructor
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__instructor_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__instructor_ids
#: model:ir.ui.menu,name:eb_lms.menu_instructor
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Instructors"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__instructor_ids
msgid "Instructors in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__instructor_ids
msgid "Instructors with this skill"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__interest_description
msgid "Interest Description"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__course_interest_ids
msgid "Interested Courses"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__subject_interest_ids
msgid "Interested Subjects"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__issue_date
msgid "Issue Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__name
msgid "Item Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__item_type
msgid "Item Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__join_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Join Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__kpi
msgid "KPI"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__course_id
msgid "Khóa học"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_root
msgid "LMS"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_class_class
msgid "LMS Class"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_course
msgid "LMS Course"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_lms_dashboard
msgid "LMS Dashboard"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_lms_dashboard_item
msgid "LMS Dashboard Item"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_instructor
msgid "LMS Instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_crm_lead
msgid "LMS Lead"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_lesson_lesson
msgid "LMS Lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_notification
msgid "LMS Notification"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_student_student
msgid "LMS Student"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_subject_subject
msgid "LMS Subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__write_uid
msgid "Last Updated by"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__write_date
msgid "Last Updated on"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__late
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Late"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__late_minutes
msgid "Late Minutes"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__late_threshold_minutes
msgid "Late Threshold (minutes)"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Late minutes must be positive for 'Late' status."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__age
msgid "Lead's age"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__company
msgid "Lead's company"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__education_level
msgid "Lead's education level"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__gender
msgid "Lead's gender"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__graduation_year
msgid "Lead's graduation year"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__major
msgid "Lead's major"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__occupation
msgid "Lead's occupation"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__school
msgid "Lead's school or university"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_learning
msgid "Learning"
msgstr ""

#. module: eb_lms
#: model:ir.module.category,name:eb_lms.module_category_eb_lms
msgid "Learning Management System"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__objectives
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__learning_objectives
msgid "Learning Objectives"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__learning_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Learning Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__learning_objectives
msgid "Learning objectives for the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__objectives
msgid "Learning objectives for this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__lesson_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__lesson_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Lesson"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
#: model:ir.actions.act_window,name:eb_lms.action_lesson_content
#: model:ir.model,name:eb_lms.model_eb_lesson_content
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__content_ids
#: model:ir.ui.menu,name:eb_lms.menu_lesson_content
msgid "Lesson Content"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__lesson_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__lesson_count
msgid "Lesson Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__name
msgid "Lesson Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__makeup_lesson_id
msgid "Lesson for makeup"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__lesson_id
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__original_lesson_id
msgid "Lesson for this attendance record"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__lesson_id
msgid "Lesson related to this rating"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__lesson_id
msgid "Lesson this content belongs to"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor.py:0
#: code:addons/eb_lms/models/subject/subject.py:0
#: model:ir.actions.act_window,name:eb_lms.action_lesson
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__lesson_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__lesson_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__lesson_ids
#: model:ir.ui.menu,name:eb_lms.menu_lesson
#: model:ir.ui.menu,name:eb_lms.menu_lms_lesson
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_calendar
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Lessons"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__lesson_ids
msgid "Lessons for this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__lesson_ids
msgid "Lessons related to this payment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__lesson_ids
msgid "Lessons taught by this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__level
msgid "Level"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__level
msgid "Level of qualification"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__license
msgid "License"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__line
msgid "Line Chart"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__link
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Link"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__online_meeting_link
msgid "Link for online lessons"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__list
msgid "List"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Location"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__location_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Location Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__class_id
msgid "Lớp học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__main_location_id
msgid "Main Location"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__main
msgid "Main Teacher"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__main_location_id
msgid "Main location for the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__major
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__major
msgid "Major"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__makeup_class_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Makeup Class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Makeup Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__makeup_lesson_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Makeup Lesson"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup Request Approved"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__makeup_request_deadline_days
msgid "Makeup Request Deadline (days)"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup Request Rejected"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_attendance_makeup
#: model:ir.ui.menu,name:eb_lms.menu_attendance_makeup
msgid "Makeup Requests"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Makeup Settings"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup arranged for %s"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup can only be requested for absent or excused attendance."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup for %s"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup lesson must be different from the original lesson."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup lesson must be scheduled after the original lesson."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__gender__male
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__gender__male
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__gender__male
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Male"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__is_mandatory
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__is_mandatory
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Mandatory"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__manual
msgid "Manual"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__manual_only
msgid "Manual Only"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Absent"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Mark Active"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Mark Completed"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Mark Dropped"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Excused"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Late"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Present"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
msgid "Mark as Paid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__master
msgid "Master"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__max_makeup_requests
msgid "Max Makeup Requests"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__max_students
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__max_students
msgid "Maximum Students"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__max_makeup_requests
msgid "Maximum number of makeup requests per student per class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__max_students
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__max_students
msgid "Maximum number of students allowed"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_model.py:0
msgid "Maximum number of students exceeded."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message
msgid "Message"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_ids
msgid "Messages"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__check_in_method
msgid "Method used for check-in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__min_students
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__min_students
msgid "Minimum Students"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__min_students
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__min_students
msgid "Minimum number of students required"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__mobile
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__mobile
msgid "Mobile"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Month"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "My Lessons"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả ca học (Sáng, Chiều, Tối,...)"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả chi tiết"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Mô tả chi tiết về tag này và ý nghĩa của nó"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả khóa học"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả ngắn"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__subject_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__subject_ids
msgid "Môn học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__name
msgid "Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__name
msgid "Name of the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__name
msgid "Name of the content item"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__name
msgid "Name of the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__name
msgid "Name of the instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__name
msgid "Name of the lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__name
msgid "Name of the schedule (e.g. Morning, Evening, Weekend)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__name
msgid "Name of the settings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__name
msgid "Name of the student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__name
msgid "Name of the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__nationality_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Nationality"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: code:addons/eb_lms/models/finance/instructor_payment.py:0
#: code:addons/eb_lms/models/finance/student_payment.py:0
msgid "New"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "New Attendance"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "New Class"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_subject.py:0
msgid "New Course-Subject Relationship"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "New Makeup Request"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Nhập mô tả chi tiết về khóa học..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Nhập mô tả ngắn về khóa học..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance
msgid "No attendance records found!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_settings
msgid "No attendance settings found!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_makeup
msgid "No makeup requests found!"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class_student
msgid "No student enrollments found!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__note
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__note
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__notes
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Notes"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__conversion_notes
msgid "Notes about the conversion process"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Notification Settings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__notify_absent_instructor
msgid "Notify Absent Instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__notify_absent_student
msgid "Notify Absent Student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__notify_manager_on_instructor_absence
msgid "Notify Manager on Instructor Absence"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__notify_absent_instructor
msgid "Notify instructor when marked absent"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__notify_manager_on_instructor_absence
msgid "Notify manager when instructor is marked absent"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__notify_absent_student
msgid "Notify student when marked absent"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__attendance_count
msgid "Number of attendances for this student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__class_count
msgid "Number of classes in this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__class_count
msgid "Number of classes taught by this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__class_count
msgid "Number of classes that teach this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__class_count
msgid "Number of classes the student is enrolled in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__course_count
msgid "Number of courses in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__course_count
msgid "Number of courses that include this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__course_count
msgid "Number of courses with this tag"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__credits
msgid "Number of credits for this subject in the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__makeup_request_deadline_days
msgid ""
"Number of days after absence within which makeup requests must be submitted"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__schedule_count
msgid "Number of expected schedules for this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__instructor_count
msgid "Number of instructors in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__instructor_count
msgid "Number of instructors with this skill"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__lesson_count
msgid "Number of lessons for this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__lesson_count
msgid "Number of lessons taught by this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__late_threshold_minutes
msgid "Number of minutes after which a student is considered late"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__auto_create_attendance_minutes
msgid ""
"Number of minutes before lesson start time to automatically create "
"attendance records"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__late_minutes
msgid "Number of minutes late"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__skill_count
msgid "Number of skills in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__student_count
msgid "Number of students enrolled in this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__subject_count
msgid "Number of subjects in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__subject_count
msgid "Number of subjects in this class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__subject_count
msgid "Number of subjects in this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__subject_count
msgid "Number of subjects with this tag"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__is_mandatory
msgid "Nếu đánh dấu, các môn học này sẽ là bắt buộc cho khóa học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__occupation
msgid "Occupation"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__learning_type__offline
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Offline"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__status__on_leave
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__status__on_leave
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__on_leave
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "On Leave"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__ongoing
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Ongoing"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__learning_type__online
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__location_type__online
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Online"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__online_meeting_link
msgid "Online Meeting Link"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__online_platform
msgid "Online Platform"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Open Enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__operation
msgid "Operations Dashboard"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Optional"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor_category
msgid ""
"Organize your instructors by category based on their specialization,\n"
"                experience level, or any other classification."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__original_attendance_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__original_attendance_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Original Attendance"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__original_class_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Original Class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__original_lesson_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Original Lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__original_attendance_id
msgid "Original attendance record (for makeup classes)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__original_attendance_id
msgid "Original attendance record that needs makeup"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__other
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__gender__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__gender__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__gender__other
msgid "Other"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__progress
msgid "Overall progress in the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__user_id
msgid "Owner"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__paid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__paid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__paid
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
msgid "Paid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__paid_amount
msgid "Paid Amount"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
msgid "Parent Categories"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__parent_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__parent_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__parent_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__parent_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
msgid "Parent Category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__parent_id
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__parent_id
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__parent_id
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__parent_id
msgid "Parent category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__employment_type__part_time
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Part Time"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__partial
msgid "Partial Payment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__partial
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Partially Paid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__partner_id
msgid "Partner linked to this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__partner_id
msgid "Partner linked to this student"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Pause"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__paused
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Paused"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__payment_amount
msgid "Payment Amount"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__payment_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__payment_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Payment Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__payment_method_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__payment_method
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Payment Method"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__payment_status
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__payment_status
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Payment Status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__payment_status
msgid "Payment status of the enrollment"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__payment_ids
msgid "Payments"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__payment_ids
msgid "Payments for this enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_makeup__status__pending
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__pending
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__pending
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Pending"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__pending
msgid "Pending Payment"
msgstr ""

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_people
msgid "People"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__completion_rate
msgid "Percentage of students who completed the class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Personal Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__phone
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__phone
msgid "Phone"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__location_type__physical
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Physical"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__physical_location_id
msgid "Physical Location"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__physical_location_id
msgid "Physical location for this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__pie
msgid "Pie Chart"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__pivot
msgid "Pivot"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__online_platform
msgid "Platform for online lessons (e.g., Zoom, Teams)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__polar
msgid "Polar Chart"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Post"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__posted
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Posted"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__schedule_id
msgid "Preferred Schedule"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__schedule_id
msgid "Preferred schedule chosen by the student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__prerequisites
msgid "Prerequisites"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__prerequisites
msgid "Prerequisites for the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__present
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__default_status__present
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Present"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__presentation
msgid "Presentation"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__primary
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__primary
msgid "Primary"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__primary_instructor_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Primary Instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__primary_instructor_id
msgid "Primary instructor for this class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__processed_by_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Processed By"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__processed_date
msgid "Processed Date"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Processing Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__product_id
msgid "Product used for course enrollment and payment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__professional
msgid "Professional"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__progress
msgid "Progress (%)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__project
msgid "Project"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__published
msgid "Published"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__qr_code
msgid "QR Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__qr_only
msgid "QR Code Only"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__name
msgid "Qualification name"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__qualification_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Qualifications"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__quiz_id
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__quiz
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__quiz
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Quiz"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__quiz_id
msgid "Quiz for this content item"
msgstr ""

#. module: eb_lms
#: model:ir.module.category,description:eb_lms.module_category_eb_lms
msgid "Quản lý hệ thống đào tạo"
msgstr ""

#. module: eb_lms
#: model:res.groups,comment:eb_lms.group_eb_lms_administrator
msgid "Quản trị viên có toàn quyền trên hệ thống LMS."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__radar
msgid "Radar Chart"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__rating
msgid "Rating"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__comment
msgid "Rating comment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__date
msgid "Rating date"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor_rating.py:0
msgid "Rating must be between 0 and 5."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__state
msgid "Rating status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__name
msgid "Rating title"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__rating
msgid "Rating value (0-5)"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor.py:0
#: code:addons/eb_lms/models/people/student.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__rating_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__rating_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Ratings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__rating_ids
msgid "Ratings given by this student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__read
msgid "Read"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__read_date
msgid "Read Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__cancellation_reason
msgid "Reason for cancellation"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__recommended_duration
msgid "Recommended Duration (hours)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__recommended_order
msgid "Recommended Order"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__assessment_methods
msgid "Recommended assessment methods for this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__recommended_duration
msgid "Recommended duration for teaching this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__recommended_order
msgid "Recommended order to study this subject in the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__teaching_methods
msgid "Recommended teaching methods for this subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__recruiting
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Recruiting"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__name
msgid "Reference"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__reference_number
msgid "Reference Number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__referral_source
msgid "Referral Source"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Refund"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__refunded
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__refunded
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__refunded
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Refunded"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Reject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_makeup__status__rejected
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__rejected
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Rejected"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__related_model
msgid "Related Model"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__partner_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__partner_id
msgid "Related Partner"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__product_id
msgid "Related Product"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__related_id
msgid "Related Record ID"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__subject_id
msgid "Related Subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__user_id
msgid "Related User"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__subject_id
msgid "Related subject for this skill"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Reminder Sent"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Reminder: Course %s is starting soon"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__request_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Request Date"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Reset to Scheduled"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__role
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Role"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__role
msgid "Role of the user in this attendance record"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__schedule_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Schedule"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__schedule_count
msgid "Schedule Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__name
msgid "Schedule Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__schedule_summary
msgid "Schedule Summary"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__schedule_time
msgid "Schedule Time"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_schedule_weekday
msgid "Schedule Weekday"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__schedule_id
msgid "Schedule selected by the student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__schedule_slot_ids
msgid "Schedule slots selected by the student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__scheduled
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Scheduled"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Schedules"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__school
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__school
msgid "School/University"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__secondary
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__secondary
msgid "Secondary"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Select a category"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select a course..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select end date..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select learning type..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select main location..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
msgid "Select parent category (if any)"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "Select parent category..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select primary instructor..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select start date..."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__schedule_slot_ids
msgid "Selected Schedule Slots"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
msgid "Selected slots must belong to the selected schedule."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__sequence
msgid "Sequence"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__sequence
msgid "Sequence for ordering"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__sequence
msgid "Sequence for ordering schedules"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__sequence
msgid "Sequence order for displaying categories"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__sequence
msgid "Sequence order for displaying subjects"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Set to Draft"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Settings Name"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__short_description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__short_description
msgid "Short Description"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__show_in_menu
msgid "Show in Menu"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard_item__col_size
msgid "Size of the widget (1-12)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__skill_count
msgid "Skill Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__category_id
msgid "Skill category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__code
msgid "Skill code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__description
msgid "Skill description"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__name
msgid "Skill name"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_skill_name_uniq
msgid "Skill name must be unique!"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor_skill.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__skill_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__skill_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Skills"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__skill_ids
msgid "Skills in this category"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Start Class"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Start Course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__start_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__start_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__start_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__start_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Start Date"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__datetime_start
msgid "Start Datetime"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Start Lesson"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Start Recruiting"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__start_time
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__start_datetime
msgid "Start Time"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__start_datetime
msgid "Start date and time"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__start_time
msgid "Start time for the classes (hours)"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Statistics"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__status
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__status_note
msgid "Status Note"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Status note is required for 'Excused' or 'Cancelled' status."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__state
msgid "Status of the enrollment"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__status
msgid "Status of the makeup request"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__student_id
#: model:res.groups,name:eb_lms.group_eb_lms_student
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__code
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Student Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__student_count
msgid "Student Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__student
msgid "Student Dashboard"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_class_student
msgid "Student Enrollments"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Student Information"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Student Name"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_student_payment
msgid "Student Payment"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_student_payment
#: model:ir.ui.menu,name:eb_lms.menu_student_payment
msgid "Student Payments"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__address
msgid "Student address"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__attendance_ids
msgid "Student attendances"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_class_student_student_class_uniq
msgid "Student can only be enrolled once in a class!"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_student_student_code_unique
msgid "Student code must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__student_id
msgid "Student created from this lead"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__date_of_birth
msgid "Student date of birth"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__education_level
msgid "Student education level"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__email
msgid "Student email"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__student_id
msgid "Student enrolled in the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__student_id
msgid "Student enrolling in the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__gender
msgid "Student gender"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__graduation_year
msgid "Student graduation year"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__identification
msgid "Student identification number (ID card, passport, etc.)"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__major
msgid "Student major"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__mobile
msgid "Student mobile number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__nationality_id
msgid "Student nationality"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__phone
msgid "Student phone number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__image
msgid "Student profile image"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__school
msgid "Student school or university"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__status
msgid "Student status"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__student_id
msgid "Student who gave the rating"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: model:ir.actions.act_window,name:eb_lms.action_student
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__student_ids
#: model:ir.ui.menu,name:eb_lms.menu_student
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Students"
msgstr ""

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_makeup
msgid ""
"Students can request makeup classes when they miss a lesson.\n"
"                Administrators or instructors can approve or reject these requests."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__student_ids
msgid "Students enrolled in this class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__student_ids
msgid "Students who selected this schedule"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__subject_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__subject_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Subject"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_subject_category
#: model:ir.ui.menu,name:eb_lms.menu_subject_category
msgid "Subject Categories"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_subject_category
msgid "Subject Category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__code
msgid "Subject Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__subject_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__subject_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__subject_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__subject_count
msgid "Subject Count"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Subject Name"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_subject_tag
msgid "Subject Tag"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_subject_tag
#: model:ir.ui.menu,name:eb_lms.menu_subject_tag
msgid "Subject Tags"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_subject_subject_code_unique
msgid "Subject code must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__subject_id
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__subject_id
msgid "Subject of this lesson"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_model.py:0
#: code:addons/eb_lms/models/course/course.py:0
#: code:addons/eb_lms/models/subject/subject_category.py:0
#: code:addons/eb_lms/models/subject/subject_tag.py:0
#: model:ir.actions.act_window,name:eb_lms.action_subject
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__subject_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__subject_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__subject_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__subject_ids
#: model:ir.ui.menu,name:eb_lms.menu_lms_subject
#: model:ir.ui.menu,name:eb_lms.menu_lms_subject_config
#: model:ir.ui.menu,name:eb_lms.menu_subject
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Subjects"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__subject_ids
msgid "Subjects in this category"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__subject_ids
msgid "Subjects in this course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__subject_ids
msgid "Subjects taught in this class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__subject_interest_ids
msgid "Subjects this lead is interested in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__subject_ids
msgid "Subjects with this tag"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__substitute
msgid "Substitute"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__substitute_arranged
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Substitute Arranged"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__substitute_instructor_id
msgid "Substitute Instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__substitute_instructor_id
msgid "Substitute instructor (if applicable)"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Substitute instructor is required for 'Substitute Arranged' status."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__success
msgid "Success"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__schedule_summary
msgid "Summary of the class schedule"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__suspended
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Suspended"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__syllabus
msgid "Syllabus"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__syllabus
msgid "Syllabus for the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__system
msgid "System"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__credits
msgid "Số tín chỉ"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__credits
msgid "Số tín chỉ cho các môn học này trong khóa học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__color
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__color
msgid "Tag color for UI"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__description
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__description
msgid "Tag description"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__name
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__name
msgid "Tag name"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_tag_name_uniq
#: model:ir.model.constraint,message:eb_lms.constraint_eb_subject_tag_name_uniq
msgid "Tag name must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__tag_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__tag_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__tag_ids
msgid "Tags"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__tag_ids
msgid "Tags for categorizing courses"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__tag_ids
msgid "Tags for categorizing leads"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__tag_ids
msgid "Tags for categorizing the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__tax_code
msgid "Tax Code"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__teacher_id
#: model:res.groups,name:eb_lms.group_eb_lms_teacher
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Teacher"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__teacher_id
msgid "Teacher for this lesson"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__teacher_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Teachers"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__teacher_ids
msgid "Teachers assigned to this class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__teaching_methods
msgid "Teaching Methods"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__text
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Text"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__text_content
msgid "Text Content"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__text_content
msgid "Text content for this item"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
msgid "The location is already booked for this time."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "The makeup request has been approved and attendance record created."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "The makeup request has been rejected."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
msgid "The teacher is already scheduled for this time."
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid ""
"The user requesting makeup must match the user in the original attendance "
"record."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__month
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "This Month"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__quarter
msgid "This Quarter"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__week
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "This Week"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__year
msgid "This Year"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Thiết lập chung cho các môn học"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/wizards/course_add_subject_wizard.py:0
msgid "Thành công"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Thêm môn học"
msgstr ""

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_course_add_subject_wizard
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Thêm môn học vào khóa học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__recommended_order
msgid "Thứ tự đề xuất"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__recommended_order
msgid "Thứ tự đề xuất để học các môn học này trong khóa học"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__time_range
msgid "Time Range"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Time Settings"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__check_in_time
msgid "Time when the user checked in"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__check_out_time
msgid "Time when the user checked out"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__name
msgid "Title"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__day
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Today"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_list
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_list
msgid "Total"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__payment_amount
msgid "Total amount to be paid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__type
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__notification_type
msgid "Type"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__learning_type
msgid "Type of learning: offline, online, or hybrid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__type
msgid "Type of qualification"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__name
msgid "Tên điểm danh"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__video_url
msgid "URL for video content"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__code
msgid "Unique code for the class"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__code
msgid "Unique code for the course"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__code
msgid "Unique code for the instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__code
msgid "Unique code for the student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__code
msgid "Unique code for the subject"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__enrollment_number
msgid "Unique enrollment number"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__university
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__university
msgid "University"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__unpaid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__unpaid
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Unpaid"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__sequence
msgid "Used to order courses. Lower is better."
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__user_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "User"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__user_id
msgid "User (student or instructor) for this attendance record"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__user_id
msgid "User account for this instructor"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__user_id
msgid "User account for this student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__user_id
msgid "User requesting makeup"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__processed_by_id
msgid "User who processed the request"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__role
msgid "Vai trò"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__video
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Video"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__video_url
msgid "Video URL"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__is_visible
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__is_visible
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Visible"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__warning
msgid "Warning"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_notification__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Week"
msgstr ""

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_schedule_weekday_code_unique
msgid "Weekday code must be unique!"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__is_all_day
msgid "Whether the attendance is for the whole day"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__active
msgid "Whether these settings are active"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__converted
msgid "Whether this lead has been converted to a student"
msgstr ""

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__has_assessment
msgid "Whether this lesson includes an assessment"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "You can add schedules directly in the list below."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. 1"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. 10"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "e.g. 2"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. 30"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. 5"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "e.g. Advanced, Beginner, Elective..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "e.g. Beginner Friendly"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. CAT001"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. CLS-001"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "e.g. MATH101, CS202, ENG305"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. Monday and Wednesday 9:00-11:00 AM, Friday 2:00-4:00 PM"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. Programming Languages"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. Python Programming - Spring 2025"
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
msgid "e.g. Sciences, Humanities, Engineering..."
msgstr ""

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. fa-book"
msgstr ""

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_attendance_attendance
msgid "Điểm danh cho học viên và giảng viên"
msgstr ""

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/wizards/course_add_subject_wizard.py:0
msgid "Đã thêm %s môn học vào khóa học"
msgstr ""
