# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* eb_lms
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-12 08:06+0000\n"
"PO-Revision-Date: 2025-04-12 08:06+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid ""
"\n"
"                        <p>Dear %s,</p>\n"
"                        <p>This is a reminder that the course <strong>%s</strong> will start on <strong>%s</strong>.</p>\n"
"                        <p>Please make sure you have completed all prerequisites.</p>\n"
"                        <p>Thank you!</p>\n"
"                        "
msgstr ""
"\n                        <p><PERSON><PERSON><PERSON> gửi %s,</p>\n                        <p>Đ<PERSON><PERSON> là lời nhắc nhở rằng khóa học <strong>%s</strong> sẽ bắt đầu vào <strong>%s</strong>.</p>\n                        <p>Vui lòng đảm bảo bạn đã hoàn thành tất cả các điều kiện tiên quyết.</p>\n                        <p>Xin cảm ơn!</p>\n                        "

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "%s - New Class"
msgstr "%s - Lớp học mới"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid ""
"<i class=\"fa fa-info-circle\"/> Expected schedules allow students to choose their preferred time slots when enrolling in this course. \n"
"                                This helps in planning classes and optimizing resources."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Lịch trình dự kiến cho phép học viên chọn khung giờ ưa thích khi đăng ký khóa học này. \n                                Điều này giúp lên kế hoạch lớp học và tối ưu hóa nguồn lực."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"to\" title=\"to\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"đến\" title=\"đến\"/>"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "<span class=\"o_stat_text\">Rating</span>"
msgstr "<span class=\"o_stat_text\">Đánh giá</span>"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_attendance_makeup_unique_makeup
msgid ""
"A makeup request already exists for this attendance record and makeup "
"lesson!"
msgstr "Đã tồn tại yêu cầu học bù cho bản ghi điểm danh và buổi học bù này!"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_enrollment_student_course_unique
msgid "A student cannot enroll in the same course twice in the same state!"
msgstr "Học viên không thể đăng ký cùng một khóa học hai lần trong cùng một trạng thái!"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_subject_course_subject_unique
msgid "A subject can only be added once to a course!"
msgstr "Một môn học chỉ có thể được thêm vào khóa học một lần!"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_attendance_attendance_unique_attendance
msgid "A user can only have one attendance record per lesson for each role!"
msgstr "Một người dùng chỉ có thể có một bản ghi điểm danh cho mỗi buổi học với mỗi vai trò!"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__absence_document
msgid "Absence Document"
msgstr "Tài liệu vắng mặt"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__absent
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__default_status__absent
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Absent"
msgstr "Vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_needaction
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_needaction
msgid "Action Needed"
msgstr "Hành động cần thiết"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__active
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__status__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__status__active
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__active
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Active"
msgstr "Đang hoạt động"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_ids
msgid "Activities"
msgstr "Hoạt động"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_exception_decoration
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Trang trí ngoại lệ hoạt động"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_state
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_type_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Add Subject"
msgstr "Thêm môn học"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Add Subjects"
msgstr "Thêm các môn học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_add_subject_wizard
msgid "Add Subjects to Course Wizard"
msgstr "Trình hướng dẫn thêm môn học vào khóa học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_form
msgid "Add a description for this content..."
msgstr "Thêm mô tả cho nội dung này..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Add notes here..."
msgstr "Thêm ghi chú tại đây..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Add tags..."
msgstr "Thêm thẻ..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Additional Information"
msgstr "Thông tin bổ sung"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__description
msgid "Additional description for this subject in the context of the course"
msgstr "Mô tả bổ sung cho môn học này trong bối cảnh khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__notes
msgid "Additional notes"
msgstr "Ghi chú bổ sung"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Additional notes about attendance status..."
msgstr "Ghi chú bổ sung về trạng thái điểm danh..."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__status_note
msgid "Additional notes about the attendance status"
msgstr "Ghi chú bổ sung về trạng thái điểm danh"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__notes
msgid "Additional notes about the enrollment"
msgstr "Ghi chú bổ sung về việc đăng ký"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Additional notes about the makeup request..."
msgstr "Ghi chú bổ sung về yêu cầu học bù..."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__note
msgid "Additional notes about this lesson"
msgstr "Ghi chú bổ sung về buổi học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__notes
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Additional notes about this student's enrollment"
msgstr "Ghi chú bổ sung về việc đăng ký của học viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__notes
msgid "Additional notes about this teacher's assignment"
msgstr "Ghi chú bổ sung về phân công của giáo viên này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__address
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__address
msgid "Address"
msgstr "Địa chỉ"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__admin
msgid "Admin Dashboard"
msgstr "Bảng điều khiển quản trị"

#. module: eb_lms
#: model:res.groups,name:eb_lms.group_eb_lms_administrator
msgid "Administrator"
msgstr "Quản trị viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__age
msgid "Age"
msgstr "Tuổi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__is_all_day
msgid "All Day"
msgstr "Cả ngày"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__all
msgid "All Methods"
msgstr "Tất cả các phương thức"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_face_recognition
msgid "Allow Face Recognition"
msgstr "Cho phép nhận diện khuôn mặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_fingerprint
msgid "Allow Fingerprint"
msgstr "Cho phép vân tay"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_makeup
msgid "Allow Makeup"
msgstr "Cho phép học bù"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_manual
msgid "Allow Manual"
msgstr "Cho phép thủ công"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allow_qr_code
msgid "Allow QR Code"
msgstr "Cho phép mã QR"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_qr_code
msgid "Allow QR code check-in"
msgstr "Cho phép điểm danh bằng mã QR"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_face_recognition
msgid "Allow face recognition check-in"
msgstr "Cho phép điểm danh bằng nhận diện khuôn mặt"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_fingerprint
msgid "Allow fingerprint check-in"
msgstr "Cho phép điểm danh bằng vân tay"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_makeup
msgid "Allow makeup classes"
msgstr "Cho phép các lớp học bù"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__allow_manual
msgid "Allow manual check-in"
msgstr "Cho phép điểm danh thủ công"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__allowed_check_in_methods
msgid "Allowed Check-in Methods"
msgstr "Phương thức điểm danh được phép"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Allowed Methods"
msgstr "Phương thức được phép"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__amount
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__amount
msgid "Amount"
msgstr "Số tiền"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__paid_amount
msgid "Amount already paid"
msgstr "Số tiền đã thanh toán"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__balance_amount
msgid "Amount still to be paid"
msgstr "Số tiền còn phải thanh toán"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_class_teacher_instructor_class_role_uniq
msgid "An instructor can have each role only once in a class!"
msgstr "Một giảng viên chỉ có thể đảm nhận mỗi vai trò một lần trong một lớp học!"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_analytics
msgid "Analytics"
msgstr "Phân tích"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Approve"
msgstr "Phê duyệt"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_makeup__status__approved
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Approved"
msgstr "Đã phê duyệt"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__assessment_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Assessment"
msgstr "Đánh giá"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__assessment_methods
msgid "Assessment Methods"
msgstr "Phương pháp đánh giá"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__assessment_type
msgid "Assessment Type"
msgstr "Loại đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__assessment_id
msgid "Assessment for this lesson"
msgstr "Đánh giá cho buổi học này"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
msgid "Assign Class"
msgstr "Phân lớp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__class_id
msgid "Assigned Class"
msgstr "Lớp được phân công"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__assignment_id
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__assignment
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__assignment
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Assignment"
msgstr "Bài tập"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__assignment_id
msgid "Assignment for this content item"
msgstr "Bài tập cho mục nội dung này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__assistant
msgid "Assistant"
msgstr "Trợ giảng"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__associate
msgid "Associate"
msgstr "Cộng tác viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__attachment_id
msgid "Attachment"
msgstr "Tệp đính kèm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_attachment_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__attachment_ids
msgid "Attachments"
msgstr "Tệp đính kèm"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__attachment_ids
msgid "Attachments related to this qualification"
msgstr "Tệp đính kèm liên quan đến bằng cấp này"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_attendance
#: model:ir.ui.menu,name:eb_lms.menu_attendance
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_calendar
msgid "Attendance"
msgstr "Điểm danh"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_graph
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_pivot
msgid "Attendance Analysis"
msgstr "Phân tích điểm danh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__attendance_count
msgid "Attendance Count"
msgstr "Số lần điểm danh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__attendance_date
msgid "Attendance Date"
msgstr "Ngày điểm danh"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_attendance_makeup
msgid "Attendance Makeup Request"
msgstr "Yêu cầu học bù"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__attendance_rate
msgid "Attendance Rate"
msgstr "Tỷ lệ tham dự"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__attendance_rate
msgid "Attendance Rate (%)"
msgstr "Tỷ lệ tham dự (%)"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_attendance_settings
#: model:ir.model,name:eb_lms.model_eb_attendance_settings
#: model:ir.ui.menu,name:eb_lms.menu_attendance_settings
msgid "Attendance Settings"
msgstr "Cài đặt điểm danh"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__attendance_rate
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__attendance_rate
msgid "Attendance rate for this student"
msgstr "Tỷ lệ tham dự của học viên này"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance
msgid ""
"Attendance records are created automatically for scheduled lessons.\n"
"                You can manually mark attendance as present, absent, late, or excused."
msgstr ""
"Các bản ghi điểm danh được tạo tự động cho các buổi học đã lên lịch.\n"
"                Bạn có thể đánh dấu điểm danh thủ công là có mặt, vắng mặt, đi muộn hoặc có lý do."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__status
msgid "Attendance status"
msgstr "Trạng thái điểm danh"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/student.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__attendance_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Attendances"
msgstr "Điểm danh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__auto_create_attendance_minutes
msgid "Auto Create Attendance (minutes before)"
msgstr "Tự động tạo điểm danh (phút trước)"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__rating_avg
msgid "Average Rating"
msgstr "Đánh giá trung bình"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__grade_average
msgid "Average grade for this student"
msgstr "Điểm trung bình của học viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__rating_avg
msgid "Average rating of this instructor"
msgstr "Đánh giá trung bình của giảng viên này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__award
msgid "Award"
msgstr "Giải thưởng"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__bachelor
msgid "Bachelor"
msgstr "Cử nhân"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__balance_amount
msgid "Balance Amount"
msgstr "Số tiền còn lại"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bank_account_holder
msgid "Bank Account Holder"
msgstr "Chủ tài khoản ngân hàng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bank_account_number
msgid "Bank Account Number"
msgstr "Số tài khoản ngân hàng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bank_name
msgid "Bank Name"
msgstr "Tên ngân hàng"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__bank_transfer
msgid "Bank Transfer"
msgstr "Chuyển khoản ngân hàng"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Banking Information"
msgstr "Thông tin ngân hàng"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__bar
msgid "Bar Chart"
msgstr "Biểu đồ cột"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Basic Information"
msgstr "Thông tin cơ bản"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__bio
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Biography"
msgstr "Tiểu sử"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__short_description
msgid "Brief description for course listings"
msgstr "Mô tả ngắn gọn cho danh sách khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__short_description
msgid "Brief description of the class"
msgstr "Mô tả ngắn gọn về lớp học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Brief description of the class (max 200 chars)"
msgstr "Mô tả ngắn gọn về lớp học (tối đa 200 ký tự)"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__lesson_id
msgid "Buổi học"
msgstr "Buổi học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__is_mandatory
msgid "Bắt buộc"
msgstr "Bắt buộc"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__calendar
msgid "Calendar"
msgstr "Lịch"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Cancel"
msgstr "Hủy"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Cancel Class"
msgstr "Hủy lớp học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Cancel Lesson"
msgstr "Hủy buổi học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__cancellation_date
msgid "Cancellation Date"
msgstr "Ngày hủy"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__cancellation_reason
msgid "Cancellation Reason"
msgstr "Lý do hủy"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__cancelled
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__cancelled
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Cancelled"
msgstr "Đã hủy"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Cannot check out without checking in first."
msgstr "Không thể điểm danh ra mà không điểm danh vào trước."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Capacity"
msgstr "Sức chứa"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__cash
msgid "Cash"
msgstr "Tiền mặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__category_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__category_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__category_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__category_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Category"
msgstr "Danh mục"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Category Name"
msgstr "Tên danh mục"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__code
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__code
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__code
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__code
msgid "Category code"
msgstr "Mã danh mục"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__color
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__color
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__color
msgid "Category color for UI"
msgstr "Màu danh mục cho giao diện người dùng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__description
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__description
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__description
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__description
msgid "Category description"
msgstr "Mô tả danh mục"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__name
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__name
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__name
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__name
msgid "Category name"
msgstr "Tên danh mục"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_category_name_parent_uniq
#: model:ir.model.constraint,message:eb_lms.constraint_eb_subject_category_name_parent_uniq
msgid "Category name must be unique within the same parent category!"
msgstr "Tên danh mục phải là duy nhất trong cùng một danh mục cha!"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_category_name_uniq
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_skill_category_name_uniq
msgid "Category name must be unique!"
msgstr "Tên danh mục phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__category_id
msgid "Category of the subject"
msgstr "Danh mục của môn học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__certificate
msgid "Certificate"
msgstr "Chứng chỉ"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__chart
msgid "Chart"
msgstr "Biểu đồ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__chart_type
msgid "Chart Type"
msgstr "Loại biểu đồ"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Check Out"
msgstr "Điểm danh ra"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Check-in Information"
msgstr "Thông tin điểm danh vào"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__check_in_method
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Check-in Method"
msgstr "Phương thức điểm danh vào"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Check-in Methods"
msgstr "Các phương thức điểm danh vào"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__check_in_time
msgid "Check-in Time"
msgstr "Thời gian điểm danh vào"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__check_out_time
msgid "Check-out Time"
msgstr "Thời gian điểm danh ra"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Check-out time cannot be earlier than check-in time."
msgstr "Thời gian điểm danh ra không thể sớm hơn thời gian điểm danh vào."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__child_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__child_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__child_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__child_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Child Categories"
msgstr "Danh mục con"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__child_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__child_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__child_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__child_ids
msgid "Child categories"
msgstr "Danh mục con"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Choose a color"
msgstr "Chọn một màu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__subject_ids
msgid "Chọn các môn học để thêm vào khóa học"
msgstr "Chọn các môn học để thêm vào khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__class_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__class_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__class_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__class_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Class"
msgstr "Lớp học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__code
msgid "Class Code"
msgstr "Mã lớp học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__class_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__class_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__class_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__class_count
msgid "Class Count"
msgstr "Số lượng lớp học"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_class_student
msgid "Class Enrollments"
msgstr "Đăng ký lớp học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__name
msgid "Class Name"
msgstr "Tên lớp học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_class_student
msgid "Class Student"
msgstr "Học viên lớp học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_class_teacher
msgid "Class Teacher"
msgstr "Giáo viên lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__class_id
msgid "Class assigned to the student after enrollment confirmation"
msgstr "Lớp học được phân cho học viên sau khi xác nhận đăng ký"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_class_class_code_unique
msgid "Class code must be unique!"
msgstr "Mã lớp học phải là duy nhất!"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_model.py:0
msgid "Class end date must be after start date."
msgstr "Ngày kết thúc lớp học phải sau ngày bắt đầu."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__image
msgid "Class image"
msgstr "Hình ảnh lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__class_id
msgid "Class related to this rating"
msgstr "Lớp học liên quan đến đánh giá này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__class_id
msgid "Class the instructor is assigned to"
msgstr "Lớp học mà giảng viên được phân công"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__class_id
msgid "Class the student is enrolled in"
msgstr "Lớp học mà học viên đã đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__class_id
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__makeup_class_id
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__original_class_id
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__class_id
msgid "Class this lesson belongs to"
msgstr "Lớp học mà buổi học này thuộc về"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: code:addons/eb_lms/models/people/instructor.py:0
#: code:addons/eb_lms/models/people/student.py:0
#: code:addons/eb_lms/models/subject/subject.py:0
#: model:ir.actions.act_window,name:eb_lms.action_class
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__class_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__class_ids
#: model:ir.ui.menu,name:eb_lms.menu_class
#: model:ir.ui.menu,name:eb_lms.menu_lms_class
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_calendar
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Classes"
msgstr "Lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__class_ids
msgid "Classes for this course"
msgstr "Các lớp học cho khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__class_ids
msgid "Classes related to this payment"
msgstr "Các lớp học liên quan đến khoản thanh toán này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__class_ids
msgid "Classes taught by this instructor"
msgstr "Các lớp học do giảng viên này dạy"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__class_ids
msgid "Classes that teach this subject"
msgstr "Các lớp học dạy môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__class_ids
msgid "Classes the student is enrolled in"
msgstr "Các lớp học mà học viên đã đăng ký"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__code
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__code
msgid "Code"
msgstr "Mã"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__college
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__college
msgid "College"
msgstr "Cao đẳng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__color
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__color
msgid "Color"
msgstr "Màu sắc"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__color
msgid "Color Index"
msgstr "Chỉ số màu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__col_size
msgid "Column Size"
msgstr "Kích thước cột"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__schedule_time
msgid "Combined start and end time for display in list view"
msgstr "Kết hợp thời gian bắt đầu và kết thúc để hiển thị trong chế độ xem danh sách"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__comment
msgid "Comment"
msgstr "Bình luận"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__company
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__company_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__company_id
msgid "Company"
msgstr "Công ty"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__company_id
msgid "Company that owns this class"
msgstr "Công ty sở hữu lớp học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__company_id
msgid "Company that owns this course"
msgstr "Công ty sở hữu khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__company_id
msgid "Company that owns this instructor record"
msgstr "Công ty sở hữu hồ sơ giảng viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__company_id
msgid "Company that owns this lesson"
msgstr "Công ty sở hữu buổi học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__company_id
msgid "Company that owns this student record"
msgstr "Công ty sở hữu hồ sơ học viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__company_id
msgid "Company that owns this subject"
msgstr "Công ty sở hữu môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__company_id
msgid "Company this enrollment belongs to"
msgstr "Công ty mà đăng ký này thuộc về"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__company_id
msgid "Company this schedule belongs to"
msgstr "Công ty mà lịch trình này thuộc về"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Complete"
msgstr "Hoàn thành"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Complete Class"
msgstr "Hoàn thành lớp học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Complete Lesson"
msgstr "Hoàn thành buổi học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__completed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__completed
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Completed"
msgstr "Đã hoàn thành"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__completion_date
msgid "Completion Date"
msgstr "Ngày hoàn thành"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__completion_rate
msgid "Completion Rate (%)"
msgstr "Tỷ lệ hoàn thành (%)"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_settings
msgid ""
"Configure attendance settings for your LMS system,\n"
"                including check-in methods, time thresholds, and makeup class policies."
msgstr ""
"Cấu hình cài đặt điểm danh cho hệ thống LMS của bạn,\n"
"                bao gồm các phương thức điểm danh, ngưỡng thời gian và chính sách lớp học bù."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Confirm"
msgstr "Xác nhận"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__confirm_date
msgid "Confirmation Date"
msgstr "Ngày xác nhận"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__confirmed
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__confirmed
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
msgid "Confirmed"
msgstr "Đã xác nhận"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Contact Information"
msgstr "Thông tin liên hệ"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Content"
msgstr "Nội dung"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__name
msgid "Content Name"
msgstr "Tên nội dung"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__content_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Content Type"
msgstr "Loại nội dung"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__content_ids
msgid "Content items for this lesson"
msgstr "Các mục nội dung cho buổi học này"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_lesson_content_name_lesson_unique
msgid "Content name must be unique within a lesson!"
msgstr "Tên nội dung phải là duy nhất trong một buổi học!"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__employment_type__contractor
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Contractor"
msgstr "Nhân viên hợp đồng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__conversion_date
msgid "Conversion Date"
msgstr "Ngày chuyển đổi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__conversion_notes
msgid "Conversion Notes"
msgstr "Ghi chú chuyển đổi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__converted
msgid "Converted"
msgstr "Đã chuyển đổi"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/lead.py:0
msgid "Converted to student on %s"
msgstr "Đã chuyển đổi thành học viên vào %s"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__course_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__course_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Course"
msgstr "Khóa học"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_course_category
#: model:ir.ui.menu,name:eb_lms.menu_course_category
msgid "Course Categories"
msgstr "Danh mục khóa học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_category
msgid "Course Category"
msgstr "Danh mục khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__code
msgid "Course Code"
msgstr "Mã khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__course_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__course_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__course_count
msgid "Course Count"
msgstr "Số lượng khóa học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_enrollment
msgid "Course Enrollment"
msgstr "Đăng ký khóa học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_schedule
msgid "Course Expected Schedule"
msgstr "Lịch trình dự kiến khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__image
msgid "Course Image"
msgstr "Hình ảnh khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Course Name"
msgstr "Tên khóa học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Course Period"
msgstr "Thời gian khóa học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_tag
msgid "Course Tag"
msgstr "Thẻ khóa học"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_course_tag
#: model:ir.ui.menu,name:eb_lms.menu_course_tag
msgid "Course Tags"
msgstr "Thẻ khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__course_id
msgid "Course being enrolled in"
msgstr "Khóa học đang được đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__category_id
msgid "Course category"
msgstr "Danh mục khóa học"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_course_code_unique
msgid "Course code must be unique!"
msgstr "Mã khóa học phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__image
msgid "Course cover image"
msgstr "Hình ảnh bìa khóa học"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid ""
"Course end date (%s) cannot be earlier than start date (%s) for course: %s"
msgstr "Ngày kết thúc khóa học (%s) không thể sớm hơn ngày bắt đầu (%s) cho khóa học: %s"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Course reminders have been sent to all enrolled students."
msgstr "Nhắc nhở khóa học đã được gửi đến tất cả học viên đã đăng ký."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__course_id
msgid "Course this class belongs to"
msgstr "Khóa học mà lớp học này thuộc về"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__course_id
msgid "Course this schedule belongs to"
msgstr "Khóa học mà lịch trình này thuộc về"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_subject
msgid "Course-Subject Relationship"
msgstr "Mối quan hệ Khóa học-Môn học"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_category.py:0
#: code:addons/eb_lms/models/course/course_tag.py:0
#: code:addons/eb_lms/models/subject/subject.py:0
#: model:ir.actions.act_window,name:eb_lms.action_course
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__course_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__course_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__course_ids
#: model:ir.ui.menu,name:eb_lms.menu_course
#: model:ir.ui.menu,name:eb_lms.menu_lms_course_config
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "Courses"
msgstr "Khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__course_ids
msgid "Courses in this category"
msgstr "Các khóa học trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__course_ids
msgid "Courses that include this subject"
msgstr "Các khóa học bao gồm môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__course_interest_ids
msgid "Courses this lead is interested in"
msgstr "Các khóa học mà khách hàng tiềm năng này quan tâm"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__course_ids
msgid "Courses with this tag"
msgstr "Các khóa học có thẻ này"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Create New Class"
msgstr "Tạo lớp học mới"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
msgid "Create Payment"
msgstr "Tạo thanh toán"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class
msgid ""
"Create classes for your courses and manage enrollments, schedules, and more."
msgstr "Tạo các lớp học cho khóa học của bạn và quản lý đăng ký, lịch trình và nhiều hơn nữa."

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor
msgid ""
"Create instructors and assign them to classes and lessons.\n"
"                Track their qualifications, skills, and performance."
msgstr ""
"Tạo giảng viên và phân công họ cho các lớp học và buổi học.\n"
"                Theo dõi bằng cấp, kỹ năng và hiệu suất của họ."

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_student
msgid ""
"Create students and track their progress across classes,\n"
"                monitor attendance, and manage their enrollments."
msgstr ""
"Tạo học viên và theo dõi tiến trình của họ qua các lớp học,\n"
"                giám sát điểm danh và quản lý đăng ký của họ."

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class
msgid "Create your first class!"
msgstr "Tạo lớp học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_course_category
msgid "Create your first course category!"
msgstr "Tạo danh mục khóa học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_course_tag
msgid "Create your first course tag!"
msgstr "Tạo thẻ khóa học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_course
msgid "Create your first course!"
msgstr "Tạo khóa học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor_category
msgid "Create your first instructor category!"
msgstr "Tạo danh mục giảng viên đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor_payment
msgid "Create your first instructor payment!"
msgstr "Tạo thanh toán giảng viên đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor
msgid "Create your first instructor!"
msgstr "Tạo giảng viên đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_lesson_content
msgid "Create your first lesson content!"
msgstr "Tạo nội dung buổi học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_lesson
msgid "Create your first lesson!"
msgstr "Tạo buổi học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_student_payment
msgid "Create your first student payment!"
msgstr "Tạo thanh toán học viên đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_student
msgid "Create your first student!"
msgstr "Tạo học viên đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_subject_category
msgid "Create your first subject category!"
msgstr "Tạo danh mục môn học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_subject_tag
msgid "Create your first subject tag!"
msgstr "Tạo thẻ môn học đầu tiên của bạn!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_subject
msgid "Create your first subject!"
msgstr "Tạo môn học đầu tiên của bạn!"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__create_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__create_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__credit_card
msgid "Credit Card"
msgstr "Thẻ tín dụng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__credits
msgid "Credits"
msgstr "Điểm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__currency_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__current_students
msgid "Current Students"
msgstr "Học viên hiện tại"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__current_students
msgid "Current number of students in the class"
msgstr "Số lượng học viên hiện tại trong lớp học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__custom
msgid "Custom"
msgstr "Tùy chỉnh"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__custom
msgid "Custom Dashboard"
msgstr "Bảng điều khiển tùy chỉnh"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__custom
msgid "Custom Range"
msgstr "Phạm vi tùy chỉnh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__dashboard_id
msgid "Dashboard"
msgstr "Bảng điều khiển"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__dashboard_item_ids
msgid "Dashboard Items"
msgstr "Mục bảng điều khiển"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__name
msgid "Dashboard Name"
msgstr "Tên bảng điều khiển"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__dashboard_type
msgid "Dashboard Type"
msgstr "Loại bảng điều khiển"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__model_id
msgid "Data Model"
msgstr "Mô hình dữ liệu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Date"
msgstr "Ngày"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__enrollment_date
msgid "Date and time of enrollment"
msgstr "Ngày và giờ đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__cancellation_date
msgid "Date and time when enrollment was cancelled"
msgstr "Ngày và giờ hủy đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__confirm_date
msgid "Date and time when enrollment was confirmed"
msgstr "Ngày và giờ xác nhận đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__completion_date
msgid "Date and time when student completed the course"
msgstr "Ngày và giờ học viên hoàn thành khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__start_date
msgid "Date and time when student started the course"
msgstr "Ngày và giờ học viên bắt đầu khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__date_of_birth
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__date_of_birth
msgid "Date of Birth"
msgstr "Ngày sinh"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__attendance_date
msgid "Date of attendance"
msgstr "Ngày điểm danh"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__request_date
msgid "Date of request"
msgstr "Ngày yêu cầu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__enrollment_end_date
msgid "Date when enrollment ends"
msgstr "Ngày kết thúc đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__enrollment_start_date
msgid "Date when enrollment starts"
msgstr "Ngày bắt đầu đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__end_date
msgid "Date when the class ends"
msgstr "Ngày kết thúc lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__start_date
msgid "Date when the class starts"
msgstr "Ngày bắt đầu lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__end_date
msgid "Date when the course ends"
msgstr "Ngày kết thúc khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__start_date
msgid "Date when the course starts"
msgstr "Ngày bắt đầu khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__join_date
msgid "Date when the instructor joined"
msgstr "Ngày giảng viên tham gia"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__end_date
msgid "Date when the instructor left"
msgstr "Ngày giảng viên rời"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__expiry_date
msgid "Date when the qualification expires"
msgstr "Ngày học vị hết hạn"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__issue_date
msgid "Date when the qualification was issued"
msgstr "Ngày học vị được cấp"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__processed_date
msgid "Date when the request was processed"
msgstr "Ngày yêu cầu được xử lý"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__enrollment_date
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__enrollment_date
msgid "Date when the student enrolled"
msgstr "Ngày học viên đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__start_date
msgid "Date when the teacher starts teaching"
msgstr "Ngày giáo viên bắt đầu dạy"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__end_date
msgid "Date when the teacher stops teaching"
msgstr "Ngày giáo viên dừng dạy"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__conversion_date
msgid "Date when this lead was converted to a student"
msgstr "Ngày khi dẫn đầu được chuyển đổi thành học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__datetime_start
msgid "Datetime field for calendar view"
msgstr "Trường ngày giờ cho lịch trình"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Day"
msgstr "Ngày"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__weekdays
msgid "Days of Week"
msgstr "Các ngày trong tuần"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__weekdays
msgid "Days of the week for this schedule"
msgstr "Các ngày trong tuần cho lịch trình này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__debit_card
msgid "Debit Card"
msgstr "Thẻ ghi nợ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__default_status
msgid "Default Status"
msgstr "Trạng thái mặc định"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__degree
msgid "Degree"
msgstr "Bậc"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__description
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__description
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Description"
msgstr "Mô tả"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__description
msgid "Description of the qualification"
msgstr "Mô tả về bằng cấp"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__description
msgid "Description of this content item"
msgstr "Mô tả về mục nội dung này"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Detailed category description..."
msgstr "Mô tả chi tiết về danh mục..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "Detailed description about the category..."
msgstr "Mô tả chi tiết về danh mục..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "Detailed description about the tag..."
msgstr "Mô tả chi tiết về thẻ..."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__description
msgid "Detailed description of the class"
msgstr "Mô tả chi tiết về lớp học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid ""
"Detailed description of the class, including objectives, prerequisites, and "
"expectations..."
msgstr "Mô tả chi tiết về lớp học, bao gồm mục tiêu, điều kiện tiên quyết và kỳ vọng..."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__description
msgid "Detailed description of the course"
msgstr "Mô tả chi tiết về khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__interest_description
msgid "Detailed description of the lead's interests"
msgstr "Mô tả chi tiết về sở thích của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__description
msgid "Detailed description of the lesson"
msgstr "Mô tả chi tiết về buổi học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__description
msgid "Detailed description of the subject"
msgstr "Mô tả chi tiết về môn học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Detailed instructor biography..."
msgstr "Tiểu sử chi tiết của giảng viên..."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__display_name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__name
msgid "Display name for the makeup request"
msgstr "Tên hiển thị cho yêu cầu điểm danh"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__doctorate
msgid "Doctorate"
msgstr "Thạc sĩ"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__document
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Document"
msgstr "Tài liệu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__absence_document
msgid "Document reference for excused absence"
msgstr "Tham chiếu tài liệu cho việc nghỉ phép bị trừ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__domain
msgid "Domain Filter"
msgstr "Lọc miền"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__doughnut
msgid "Doughnut Chart"
msgstr "Biểu đồ tròn"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__draft
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__draft
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Draft"
msgstr "Nháp"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__dropped
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Dropped"
msgstr "Bị bỏ"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__dropped
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Dropped Out"
msgstr "Bị bỏ ra"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__duration
msgid "Duration (hours)"
msgstr "Thời gian (giờ)"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__duration
msgid "Duration (minutes)"
msgstr "Thời gian (phút)"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_schedule.py:0
msgid "Duration cannot exceed 6 hours."
msgstr "Thời gian không được vượt quá 6 giờ."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__duration
msgid "Duration in hours"
msgstr "Thời gian trong giờ"


#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__e_wallet
msgid "E-Wallet"
msgstr "Ví điện tử"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Education"
msgstr "Giáo dục"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__education_level
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__education_level
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Education Level"
msgstr "Trình độ học vấn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__email
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__email
msgid "Email"
msgstr "Email"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Employment"
msgstr "Việc làm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__employment_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Employment Type"
msgstr "Loại hình việc làm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__end_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__end_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__end_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__end_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__end_time
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__end_datetime
msgid "End Time"
msgstr "Thời gian kết thúc"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__end_datetime
msgid "End date and time"
msgstr "Ngày và giờ kết thúc"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_teacher.py:0
msgid "End date must be after start date."
msgstr "Ngày kết thúc phải sau ngày bắt đầu."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__end_time
msgid "End time for the classes (hours)"
msgstr "Thời gian kết thúc cho các lớp học (giờ)"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
msgid "End time must be after start time."
msgstr "Thời gian kết thúc phải sau thời gian bắt đầu."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_schedule.py:0
msgid "End time must be greater than start time."
msgstr "Thời gian kết thúc phải lớn hơn thời gian bắt đầu."

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class_student
msgid ""
"Enroll students in your classes to track their progress and attendance."
msgstr "Đăng ký học viên vào các lớp học của bạn để theo dõi tiến trình và điểm danh của họ."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_schedule.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__student_ids
msgid "Enrolled Students"
msgstr "Học viên đã đăng ký"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__enrollment_id
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__enrollment
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Enrollment"
msgstr "Đăng ký"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__enrollment_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__enrollment_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__enrollment_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Enrollment Date"
msgstr "Ngày đăng ký"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__enrollment_end_date
msgid "Enrollment End"
msgstr "Ngày kết thúc đăng ký"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__enrollment_number
msgid "Enrollment Number"
msgstr "Số đăng ký"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Enrollment Period"
msgstr "Thời gian đăng ký"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__enrollment_start_date
msgid "Enrollment Start"
msgstr "Ngày bắt đầu đăng ký"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_student.py:0
msgid "Enrollment date is required."
msgstr "Ngày đăng ký là bắt buộc."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid ""
"Enrollment end date (%s) cannot be earlier than start date (%s) for course: "
"%s"
msgstr "Ngày kết thúc đăng ký (%s) không thể sớm hơn ngày bắt đầu (%s) cho khóa học: %s"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
msgid "Enter detailed description about this category..."
msgstr "Nhập mô tả chi tiết về thể loại này..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Enter detailed description about this subject..."
msgstr "Nhập mô tả chi tiết về môn học này..."

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__error
msgid "Error"
msgstr "Lỗi"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__duration
msgid "Estimated duration in minutes to complete this content"
msgstr "Thời gian dự kiến để hoàn thành nội dung này (phút)"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__exam
msgid "Exam"
msgstr "Thi"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Example: Basic Python Programming Course"
msgstr "Ví dụ: Khóa học lập trình Python cơ bản"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__excused
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Excused"
msgstr "Có phép"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Excused Absence"
msgstr "Có phép"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__schedule_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Expected Schedules"
msgstr "Lịch trình dự kiến"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__schedule_ids
msgid "Expected schedules for this course that students can choose from"
msgstr "Lịch trình dự kiến cho khóa học này mà học viên có thể chọn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__expiry_date
msgid "Expiry Date"
msgstr "Ngày hết hạn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__external_link
msgid "External Link"
msgstr "Liên kết ngoài"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__external_link
msgid "External link for this content item"
msgstr "Liên kết ngoài cho nội dung này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__reference_number
msgid "External reference number (e.g. receipt number, transaction ID)"
msgstr "Số tham chiếu ngoài (ví dụ: số hóa đơn, mã giao dịch)"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__face_recognition
msgid "Face Recognition"
msgstr "Nhận diện khuôn mặt"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__face_only
msgid "Face Recognition Only"
msgstr "Chỉ nhận diện khuôn mặt"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__gender__female
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__gender__female
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__gender__female
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Female"
msgstr "Nữ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__field_ids
msgid "Fields to Display"
msgstr "Các trường để hiển thị"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__attachment_id
msgid "File attachment for this content item"
msgstr "Tệp đính kèm cho mục nội dung này"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_finance
msgid "Finance"
msgstr "Tài chính"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__finance
msgid "Finance Dashboard"
msgstr "Bảng điều khiển tài chính"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__fingerprint
msgid "Fingerprint"
msgstr "Vân tay"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__fingerprint_only
msgid "Fingerprint Only"
msgstr "Chỉ vân tay"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_follower_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_partner_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__icon
msgid "Font Awesome icon class"
msgstr "Lớp biểu tượng Font Awesome"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_type_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Biểu tượng Font awesome ví dụ fa-tasks"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__employment_type__full_time
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Full Time"
msgstr "Toàn thời gian"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__gender
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__gender
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__gender
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Gender"
msgstr "Giới tính"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__role__instructor
msgid "Giảng viên"
msgstr "Giảng viên"

#. module: eb_lms
#: model:res.groups,comment:eb_lms.group_eb_lms_teacher
msgid ""
"Giảng viên có thể quản lý lớp học, môn học và buổi học mà họ phụ trách."
msgstr "Giảng viên có thể quản lý lớp học, môn học và buổi học mà họ phụ trách."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__grade_average
msgid "Grade Average"
msgstr "Điểm trung bình"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__graduate
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__graduate
msgid "Graduate"
msgstr "Sau đại học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__graduated
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Graduated"
msgstr "Đã tốt nghiệp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__graduation_year
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__graduation_year
msgid "Graduation Year"
msgstr "Năm tốt nghiệp"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__graph
msgid "Graph"
msgstr "Đồ thị"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__guest
msgid "Guest Lecturer"
msgstr "Giảng viên khách mời"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__has_assessment
msgid "Has Assessment"
msgstr "Có đánh giá"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__has_message
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__height
msgid "Height (px)"
msgstr "Chiều cao (px)"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Hidden"
msgstr "Ẩn"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__high_school
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__high_school
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__high_school
msgid "High School"
msgstr "Trung học phổ thông"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__referral_source
msgid "How the lead heard about us"
msgstr "Khách hàng tiềm năng biết đến chúng tôi như thế nào"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__learning_type__hybrid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__location_type__hybrid
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Hybrid"
msgstr "Kết hợp"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__role__student
msgid "Học viên"
msgstr "Học viên"

#. module: eb_lms
#: model:res.groups,comment:eb_lms.group_eb_lms_student
msgid ""
"Học viên chỉ có thể xem thông tin khóa học, lớp học, môn học và buổi học mà "
"họ tham gia."
msgstr "Học viên chỉ có thể xem thông tin khóa học, lớp học, môn học và buổi học mà họ tham gia."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__user_id
msgid "Học viên/Giảng viên"
msgstr "Học viên/Giảng viên"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Hủy"
msgstr "Hủy"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__id
msgid "ID"
msgstr "ID"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_exception_icon
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_exception_icon
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng để chỉ ra một hoạt động ngoại lệ."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__identification
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__identification
msgid "Identification"
msgstr "Giấy tờ tùy thân"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_needaction
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu được chọn, tin nhắn mới cần sự chú ý của bạn."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_has_sms_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_has_error
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu được chọn, một số tin nhắn có lỗi gửi."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__is_mandatory
msgid "If checked, this content is mandatory for the lesson"
msgstr "Nếu được chọn, nội dung này là bắt buộc cho buổi học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__is_mandatory
msgid "If checked, this subject is mandatory for the course"
msgstr "Nếu được chọn, môn học này là bắt buộc cho khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__active
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__active
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__active
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__active
msgid "If unchecked, it will be hidden but still exists"
msgstr "Nếu không được chọn, nó sẽ bị ẩn nhưng vẫn tồn tại"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__active
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__active
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__active
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__active
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__active
msgid "If unchecked, it will be hidden from views"
msgstr "Nếu không được chọn, nó sẽ bị ẩn khỏi các góc nhìn"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__is_visible
msgid "If unchecked, this content will be hidden from students"
msgstr "Nếu không được chọn, nội dung này sẽ bị ẩn khỏi học viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__image
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__image
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__image
msgid "Image"
msgstr "Hình ảnh"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_course__state__in_progress
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__in_progress
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__in_progress
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "In Progress"
msgstr "Đang tiến hành"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__status__inactive
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__status__inactive
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__inactive
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Inactive"
msgstr "Không hoạt động"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__info
msgid "Information"
msgstr "Thông tin"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Inline Editing"
msgstr "Chỉnh sửa trực tiếp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__institution
msgid "Institution"
msgstr "Tổ chức"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__institution
msgid "Institution that issued the qualification"
msgstr "Tổ chức cấp bằng cấp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__instructor_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__instructor_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
msgid "Instructor"
msgstr "Giảng viên"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_instructor_category
#: model:ir.ui.menu,name:eb_lms.menu_instructor_category
msgid "Instructor Categories"
msgstr "Danh mục giảng viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_category
msgid "Instructor Category"
msgstr "Danh mục giảng viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__code
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Instructor Code"
msgstr "Mã giảng viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__instructor_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__instructor_count
msgid "Instructor Count"
msgstr "Số lượng giảng viên"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__instructor
msgid "Instructor Dashboard"
msgstr "Bảng điều khiển giảng viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Instructor Name"
msgstr "Tên giảng viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_payment
msgid "Instructor Payment"
msgstr "Thanh toán cho giảng viên"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_instructor_payment
#: model:ir.ui.menu,name:eb_lms.menu_instructor_payment
msgid "Instructor Payments"
msgstr "Thanh toán cho giảng viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_qualification
msgid "Instructor Qualification"
msgstr "Bằng cấp của giảng viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_rating
msgid "Instructor Rating"
msgstr "Đánh giá của giảng viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_skill
msgid "Instructor Skill"
msgstr "Kỹ năng của giảng viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_skill_category
msgid "Instructor Skill Category"
msgstr "Danh mục kỹ năng của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__address
msgid "Instructor address"
msgstr "Địa chỉ của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__instructor_id
msgid "Instructor assigned to the class"
msgstr "Giảng viên được phân công cho lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bank_account_holder
msgid "Instructor bank account holder name"
msgstr "Tên chủ tài khoản ngân hàng của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bank_account_number
msgid "Instructor bank account number"
msgstr "Số tài khoản ngân hàng của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bank_name
msgid "Instructor bank name"
msgstr "Tên ngân hàng của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__instructor_id
msgid "Instructor being rated"
msgstr "Giảng viên được đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__bio
msgid "Instructor biography"
msgstr "Tiểu sử của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__category_id
msgid "Instructor category"
msgstr "Danh mục giảng viên"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_instructor_code_unique
msgid "Instructor code must be unique!"
msgstr "Mã giảng viên phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__date_of_birth
msgid "Instructor date of birth"
msgstr "Ngày sinh của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__email
msgid "Instructor email"
msgstr "Email của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__employment_type
msgid "Instructor employment type"
msgstr "Loại hình việc làm của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__instructor_id
msgid "Instructor for this lesson"
msgstr "Giảng viên cho buổi học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__gender
msgid "Instructor gender"
msgstr "Giới tính của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__identification
msgid "Instructor identification number (ID card, passport, etc.)"
msgstr "Số giấy tờ tùy thân của giảng viên (CMND, hộ chiếu, v.v.)"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__mobile
msgid "Instructor mobile number"
msgstr "Số điện thoại di động của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__phone
msgid "Instructor phone number"
msgstr "Số điện thoại của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__image
msgid "Instructor profile image"
msgstr "Hình ảnh hồ sơ của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__qualification_ids
msgid "Instructor qualifications"
msgstr "Bằng cấp của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__rating_ids
msgid "Instructor ratings"
msgstr "Đánh giá của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__skill_ids
msgid "Instructor skills"
msgstr "Kỹ năng của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__status
msgid "Instructor status"
msgstr "Trạng thái của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__tax_code
msgid "Instructor tax code"
msgstr "Mã số thuế của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__instructor_id
msgid "Instructor who has this qualification"
msgstr "Giảng viên có bằng cấp này"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor_category.py:0
#: code:addons/eb_lms/models/people/instructor_skill.py:0
#: model:ir.actions.act_window,name:eb_lms.action_instructor
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__instructor_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__instructor_ids
#: model:ir.ui.menu,name:eb_lms.menu_instructor
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_form
msgid "Instructors"
msgstr "Giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__instructor_ids
msgid "Instructors in this category"
msgstr "Giảng viên trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__instructor_ids
msgid "Instructors with this skill"
msgstr "Giảng viên có kỹ năng này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__interest_description
msgid "Interest Description"
msgstr "Mô tả sở thích"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__course_interest_ids
msgid "Interested Courses"
msgstr "Khóa học quan tâm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__subject_interest_ids
msgid "Interested Subjects"
msgstr "Môn học quan tâm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_is_follower
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__issue_date
msgid "Issue Date"
msgstr "Ngày cấp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__name
msgid "Item Name"
msgstr "Tên mục"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__item_type
msgid "Item Type"
msgstr "Loại mục"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__join_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Join Date"
msgstr "Ngày tham gia"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__kpi
msgid "KPI"
msgstr "Chỉ số hiệu suất chính"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__course_id
msgid "Khóa học"
msgstr "Khóa học"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_root
msgid "LMS"
msgstr "LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_class_class
msgid "LMS Class"
msgstr "Lớp học LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_course_course
msgid "LMS Course"
msgstr "Khóa học LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_lms_dashboard
msgid "LMS Dashboard"
msgstr "Bảng điều khiển LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_lms_dashboard_item
msgid "LMS Dashboard Item"
msgstr "Mục bảng điều khiển LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_instructor_instructor
msgid "LMS Instructor"
msgstr "Giảng viên LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_crm_lead
msgid "LMS Lead"
msgstr "Lead/Cơ hội"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_lesson_lesson
msgid "LMS Lesson"
msgstr "Buổi học LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_notification
msgid "LMS Notification"
msgstr "Thông báo LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_student_student
msgid "LMS Student"
msgstr "Học viên LMS"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_subject_subject
msgid "LMS Subject"
msgstr "Môn học LMS"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__write_uid
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__write_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__late
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Late"
msgstr "Muộn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__late_minutes
msgid "Late Minutes"
msgstr "Số phút muộn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__late_threshold_minutes
msgid "Late Threshold (minutes)"
msgstr "Ngưỡng muộn (phút)"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Late minutes must be positive for 'Late' status."
msgstr "Số phút muộn phải là số dương cho trạng thái 'Muộn'."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__age
msgid "Lead's age"
msgstr "Tuổi của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__company
msgid "Lead's company"
msgstr "Công ty của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__education_level
msgid "Lead's education level"
msgstr "Trình độ học vấn của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__gender
msgid "Lead's gender"
msgstr "Giới tính của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__graduation_year
msgid "Lead's graduation year"
msgstr "Năm tốt nghiệp của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__major
msgid "Lead's major"
msgstr "Chuyên ngành của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__occupation
msgid "Lead's occupation"
msgstr "Nghề nghiệp của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__school
msgid "Lead's school or university"
msgstr "Trường học hoặc đại học của khách hàng tiềm năng"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_learning
msgid "Learning"
msgstr "Học tập"

#. module: eb_lms
#: model:ir.module.category,name:eb_lms.module_category_eb_lms
msgid "Learning Management System"
msgstr "Hệ thống quản lý học tập"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__objectives
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__learning_objectives
msgid "Learning Objectives"
msgstr "Mục tiêu học tập"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__learning_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Learning Type"
msgstr "Loại hình học tập"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__learning_objectives
msgid "Learning objectives for the subject"
msgstr "Mục tiêu học tập cho môn học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__objectives
msgid "Learning objectives for this lesson"
msgstr "Mục tiêu học tập cho buổi học này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__lesson_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__lesson_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Lesson"
msgstr "Buổi học"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
#: model:ir.actions.act_window,name:eb_lms.action_lesson_content
#: model:ir.model,name:eb_lms.model_eb_lesson_content
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__content_ids
#: model:ir.ui.menu,name:eb_lms.menu_lesson_content
msgid "Lesson Content"
msgstr "Nội dung buổi học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__lesson_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__lesson_count
msgid "Lesson Count"
msgstr "Số lượng buổi học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__name
msgid "Lesson Name"
msgstr "Tên buổi học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__makeup_lesson_id
msgid "Lesson for makeup"
msgstr "Buổi học bù"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__lesson_id
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__original_lesson_id
msgid "Lesson for this attendance record"
msgstr "Buổi học cho bản ghi điểm danh này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__lesson_id
msgid "Lesson related to this rating"
msgstr "Buổi học liên quan đến đánh giá này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__lesson_id
msgid "Lesson this content belongs to"
msgstr "Buổi học mà nội dung này thuộc về"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor.py:0
#: code:addons/eb_lms/models/subject/subject.py:0
#: model:ir.actions.act_window,name:eb_lms.action_lesson
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__lesson_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__lesson_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__lesson_ids
#: model:ir.ui.menu,name:eb_lms.menu_lesson
#: model:ir.ui.menu,name:eb_lms.menu_lms_lesson
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_calendar
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Lessons"
msgstr "Buổi học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__lesson_ids
msgid "Lessons for this subject"
msgstr "Các buổi học cho môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__lesson_ids
msgid "Lessons related to this payment"
msgstr "Các buổi học liên quan đến khoản thanh toán này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__lesson_ids
msgid "Lessons taught by this instructor"
msgstr "Các buổi học do giảng viên này dạy"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__level
msgid "Level"
msgstr "Cấp độ"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__level
msgid "Level of qualification"
msgstr "Cấp độ của bằng cấp"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__license
msgid "License"
msgstr "Giấy phép"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__line
msgid "Line Chart"
msgstr "Biểu đồ đường"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__link
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Link"
msgstr "Liên kết"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__online_meeting_link
msgid "Link for online lessons"
msgstr "Liên kết cho các buổi học trực tuyến"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__list
msgid "List"
msgstr "Danh sách"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Location"
msgstr "Địa điểm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__location_type
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Location Type"
msgstr "Loại địa điểm"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__class_id
msgid "Lớp học"
msgstr "Lớp học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__main_location_id
msgid "Main Location"
msgstr "Địa điểm chính"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__main
msgid "Main Teacher"
msgstr "Giáo viên chính"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__main_location_id
msgid "Main location for the class"
msgstr "Địa điểm chính cho lớp học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__major
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__major
msgid "Major"
msgstr "Chuyên ngành"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__makeup_class_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Makeup Class"
msgstr "Lớp học bù"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Makeup Information"
msgstr "Thông tin học bù"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__makeup_lesson_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Makeup Lesson"
msgstr "Buổi học bù"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup Request Approved"
msgstr "Yêu cầu học bù đã được phê duyệt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__makeup_request_deadline_days
msgid "Makeup Request Deadline (days)"
msgstr "Thời hạn yêu cầu học bù (ngày)"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup Request Rejected"
msgstr "Yêu cầu học bù đã bị từ chối"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_attendance_makeup
#: model:ir.ui.menu,name:eb_lms.menu_attendance_makeup
msgid "Makeup Requests"
msgstr "Yêu cầu học bù"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Makeup Settings"
msgstr "Cài đặt học bù"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup arranged for %s"
msgstr "Học bù đã được sắp xếp cho %s"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup can only be requested for absent or excused attendance."
msgstr "Học bù chỉ có thể được yêu cầu cho trường hợp vắng mặt hoặc vắng mặt có phép."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup for %s"
msgstr "Học bù cho %s"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup lesson must be different from the original lesson."
msgstr "Buổi học bù phải khác với buổi học ban đầu."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "Makeup lesson must be scheduled after the original lesson."
msgstr "Buổi học bù phải được lên lịch sau buổi học ban đầu."

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__gender__male
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__gender__male
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__gender__male
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Male"
msgstr "Nam"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__is_mandatory
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__is_mandatory
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Mandatory"
msgstr "Bắt buộc"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__manual
msgid "Manual"
msgstr "Thủ công"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__manual_only
msgid "Manual Only"
msgstr "Chỉ thủ công"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Absent"
msgstr "Đánh dấu vắng mặt"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Mark Active"
msgstr "Đánh dấu hoạt động"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Mark Completed"
msgstr "Đánh dấu hoàn thành"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Mark Dropped"
msgstr "Đánh dấu đã rời khỏi"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Excused"
msgstr "Đánh dấu có phép"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Late"
msgstr "Đánh dấu muộn"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
msgid "Mark Present"
msgstr "Đánh dấu có mặt"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
msgid "Mark as Paid"
msgstr "Đánh dấu đã thanh toán"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__master
msgid "Master"
msgstr "Thạc sĩ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__max_makeup_requests
msgid "Max Makeup Requests"
msgstr "Số lượng yêu cầu học bù tối đa"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__max_students
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__max_students
msgid "Maximum Students"
msgstr "Số học viên tối đa"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__max_makeup_requests
msgid "Maximum number of makeup requests per student per class"
msgstr "Số lượng yêu cầu học bù tối đa cho mỗi học viên trong mỗi lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__max_students
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__max_students
msgid "Maximum number of students allowed"
msgstr "Số lượng học viên tối đa được phép"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_model.py:0
msgid "Maximum number of students exceeded."
msgstr "Vượt quá số lượng học viên tối đa."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message
msgid "Message"
msgstr "Tin nhắn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_has_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__check_in_method
msgid "Method used for check-in"
msgstr "Phương thức được sử dụng để điểm danh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__min_students
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__min_students
msgid "Minimum Students"
msgstr "Số học viên tối thiểu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__min_students
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__min_students
msgid "Minimum number of students required"
msgstr "Số lượng học viên tối thiểu yêu cầu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__mobile
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__mobile
msgid "Mobile"
msgstr "Di động"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Month"
msgstr "Tháng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__my_activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Thời hạn hoạt động của tôi"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "My Lessons"
msgstr "Buổi học của tôi"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả ca học (Sáng, Chiều, Tối,...)"
msgstr "Mô tả ca học (Sáng, Chiều, Tối,...)"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả chi tiết"
msgstr "Mô tả chi tiết"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Mô tả chi tiết về tag này và ý nghĩa của nó"
msgstr "Mô tả chi tiết về tag này và ý nghĩa của nó"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả khóa học"
msgstr "Mô tả khóa học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Mô tả ngắn"
msgstr "Mô tả ngắn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__subject_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__subject_ids
msgid "Môn học"
msgstr "Môn học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__name
msgid "Name"
msgstr "Tên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__name
msgid "Name of the class"
msgstr "Tên của lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__name
msgid "Name of the content item"
msgstr "Tên của mục nội dung"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__name
msgid "Name of the course"
msgstr "Tên của khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__name
msgid "Name of the instructor"
msgstr "Tên của giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__name
msgid "Name of the lesson"
msgstr "Tên của buổi học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__name
msgid "Name of the schedule (e.g. Morning, Evening, Weekend)"
msgstr "Tên của lịch học (ví dụ: Sáng, Tối, Cuối tuần)"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__name
msgid "Name of the settings"
msgstr "Tên của cài đặt"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__name
msgid "Name of the student"
msgstr "Tên của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__name
msgid "Name of the subject"
msgstr "Tên của môn học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__nationality_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Nationality"
msgstr "Quốc tịch"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: code:addons/eb_lms/models/finance/instructor_payment.py:0
#: code:addons/eb_lms/models/finance/student_payment.py:0
msgid "New"
msgstr "Mới"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "New Attendance"
msgstr "Điểm danh mới"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "New Class"
msgstr "Lớp học mới"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_subject.py:0
msgid "New Course-Subject Relationship"
msgstr "Mối quan hệ Khóa học-Môn học mới"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "New Makeup Request"
msgstr "Yêu cầu học bù mới"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_calendar_event_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện lịch hoạt động tiếp theo"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_date_deadline
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Thời hạn hoạt động tiếp theo"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_summary
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_type_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Nhập mô tả chi tiết về khóa học..."
msgstr "Nhập mô tả chi tiết về khóa học..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Nhập mô tả ngắn về khóa học..."
msgstr "Nhập mô tả ngắn về khóa học..."

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance
msgid "No attendance records found!"
msgstr "Không tìm thấy bản ghi điểm danh nào!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_settings
msgid "No attendance settings found!"
msgstr "Không tìm thấy cài đặt điểm danh nào!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_makeup
msgid "No makeup requests found!"
msgstr "Không tìm thấy yêu cầu học bù nào!"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_class_student
msgid "No student enrollments found!"
msgstr "Không tìm thấy đăng ký học viên nào!"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__notes
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__note
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__note
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__notes
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Notes"
msgstr "Ghi chú"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__conversion_notes
msgid "Notes about the conversion process"
msgstr "Ghi chú về quá trình chuyển đổi"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Notification Settings"
msgstr "Cài đặt thông báo"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__notify_absent_instructor
msgid "Notify Absent Instructor"
msgstr "Thông báo giảng viên vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__notify_absent_student
msgid "Notify Absent Student"
msgstr "Thông báo học viên vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__notify_manager_on_instructor_absence
msgid "Notify Manager on Instructor Absence"
msgstr "Thông báo quản lý khi giảng viên vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__notify_absent_instructor
msgid "Notify instructor when marked absent"
msgstr "Thông báo cho giảng viên khi được đánh dấu vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__notify_manager_on_instructor_absence
msgid "Notify manager when instructor is marked absent"
msgstr "Thông báo cho quản lý khi giảng viên được đánh dấu vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__notify_absent_student
msgid "Notify student when marked absent"
msgstr "Thông báo cho học viên khi được đánh dấu vắng mặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_needaction_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng hành động"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__attendance_count
msgid "Number of attendances for this student"
msgstr "Số lần điểm danh của học viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__class_count
msgid "Number of classes in this course"
msgstr "Số lớp học trong khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__class_count
msgid "Number of classes taught by this instructor"
msgstr "Số lớp học do giảng viên này dạy"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__class_count
msgid "Number of classes that teach this subject"
msgstr "Số lớp học dạy môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__class_count
msgid "Number of classes the student is enrolled in"
msgstr "Số lớp học mà học viên đã đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__course_count
msgid "Number of courses in this category"
msgstr "Số khóa học trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__course_count
msgid "Number of courses that include this subject"
msgstr "Số khóa học bao gồm môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__course_count
msgid "Number of courses with this tag"
msgstr "Số khóa học có thẻ này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__credits
msgid "Number of credits for this subject in the course"
msgstr "Số tín chỉ cho môn học này trong khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__makeup_request_deadline_days
msgid ""
"Number of days after absence within which makeup requests must be submitted"
msgstr "Số ngày sau khi vắng mặt mà trong đó yêu cầu học bù phải được gửi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_has_error_counter
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Số lỗi"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__schedule_count
msgid "Number of expected schedules for this course"
msgstr "Số lịch học dự kiến cho khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__instructor_count
msgid "Number of instructors in this category"
msgstr "Số giảng viên trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__instructor_count
msgid "Number of instructors with this skill"
msgstr "Số giảng viên có kỹ năng này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__lesson_count
msgid "Number of lessons for this subject"
msgstr "Số buổi học cho môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__lesson_count
msgid "Number of lessons taught by this instructor"
msgstr "Số buổi học do giảng viên này dạy"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_needaction_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn yêu cầu hành động"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_notification__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__message_has_error_counter
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn có lỗi gửi"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__late_threshold_minutes
msgid "Number of minutes after which a student is considered late"
msgstr "Số phút sau đó học viên được coi là đến muộn"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__auto_create_attendance_minutes
msgid ""
"Number of minutes before lesson start time to automatically create "
"attendance records"
msgstr "Số phút trước thời gian bắt đầu buổi học để tự động tạo bản ghi điểm danh"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__late_minutes
msgid "Number of minutes late"
msgstr "Số phút đến muộn"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__skill_count
msgid "Number of skills in this category"
msgstr "Số kỹ năng trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__student_count
msgid "Number of students enrolled in this course"
msgstr "Số học viên đăng ký trong khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__subject_count
msgid "Number of subjects in this category"
msgstr "Số môn học trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__subject_count
msgid "Number of subjects in this class"
msgstr "Số môn học trong lớp học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__subject_count
msgid "Number of subjects in this course"
msgstr "Số môn học trong khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__subject_count
msgid "Number of subjects with this tag"
msgstr "Số môn học có thẻ này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__is_mandatory
msgid "Nếu đánh dấu, các môn học này sẽ là bắt buộc cho khóa học"
msgstr "Nếu đánh dấu, các môn học này sẽ là bắt buộc cho khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__occupation
msgid "Occupation"
msgstr "Nghề nghiệp"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__learning_type__offline
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Offline"
msgstr "Ngoại tuyến"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__status__on_leave
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__status__on_leave
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__on_leave
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "On Leave"
msgstr "Nghỉ phép"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__ongoing
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Ongoing"
msgstr "Đang diễn ra"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__learning_type__online
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__location_type__online
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Online"
msgstr "Trực tuyến"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__online_meeting_link
msgid "Online Meeting Link"
msgstr "Liên kết học trực tuyến"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__online_platform
msgid "Online Platform"
msgstr "Nền tảng trực tuyến"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Open Enrollment"
msgstr "Mở đăng ký"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__operation
msgid "Operations Dashboard"
msgstr "Bảng điều khiển hoạt động"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Optional"
msgstr "Tùy chọn"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_instructor_category
msgid ""
"Organize your instructors by category based on their specialization,\n"
"                experience level, or any other classification."
msgstr "Tổ chức giảng viên của bạn theo danh mục dựa trên chuyên môn, cấp độ kinh nghiệm hoặc bất kỳ phân loại nào khác."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__original_attendance_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__original_attendance_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Original Attendance"
msgstr "Điểm danh gốc"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__original_class_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Original Class"
msgstr "Lớp học gốc"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__original_lesson_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Original Lesson"
msgstr "Buổi học gốc"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__original_attendance_id
msgid "Original attendance record (for makeup classes)"
msgstr "Bản ghi điểm danh gốc (cho các lớp học bù)"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__original_attendance_id
msgid "Original attendance record that needs makeup"
msgstr "Bản ghi điểm danh gốc cần học bù"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__other
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__gender__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__gender__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__type__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__payment_method__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__other
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__gender__other
msgid "Other"
msgstr "Khác"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__progress
msgid "Overall progress in the class"
msgstr "Tiến độ tổng thể trong lớp học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__user_id
msgid "Owner"
msgstr "Chủ sở hữu"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__paid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__paid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_payment__state__paid
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
msgid "Paid"
msgstr "Đã thanh toán"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__paid_amount
msgid "Paid Amount"
msgstr "Số tiền đã thanh toán"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
msgid "Parent Categories"
msgstr "Danh mục cha"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__parent_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__parent_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__parent_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__parent_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_category_search
msgid "Parent Category"
msgstr "Danh mục cha"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__parent_id
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__parent_id
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__parent_id
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__parent_id
msgid "Parent category"
msgstr "Danh mục cha"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_instructor__employment_type__part_time
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
msgid "Part Time"
msgstr "Bán thời gian"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__partial
msgid "Partial Payment"
msgstr "Thanh toán một phần"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__partial
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Partially Paid"
msgstr "Đã thanh toán một phần"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__partner_id
msgid "Partner linked to this instructor"
msgstr "Đối tác liên kết với giảng viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__partner_id
msgid "Partner linked to this student"
msgstr "Đối tác liên kết với học viên này"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_form
msgid "Pause"
msgstr "Tạm dừng"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__state__paused
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Paused"
msgstr "Đã tạm dừng"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__payment_amount
msgid "Payment Amount"
msgstr "Số tiền thanh toán"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__payment_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__payment_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Payment Date"
msgstr "Ngày thanh toán"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__payment_method_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__payment_method
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Payment Method"
msgstr "Phương thức thanh toán"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__payment_status
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__payment_status
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Payment Status"
msgstr "Trạng thái thanh toán"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__payment_status
msgid "Payment status of the enrollment"
msgstr "Trạng thái thanh toán của đăng ký"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__payment_ids
msgid "Payments"
msgstr "Các khoản thanh toán"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__payment_ids
msgid "Payments for this enrollment"
msgstr "Các khoản thanh toán cho đăng ký này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_makeup__status__pending
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__pending
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__pending
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Pending"
msgstr "Đang chờ xử lý"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__state__pending
msgid "Pending Payment"
msgstr "Chờ thanh toán"

#. module: eb_lms
#: model:ir.ui.menu,name:eb_lms.menu_lms_people
msgid "People"
msgstr "Con người"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__completion_rate
msgid "Percentage of students who completed the class"
msgstr "Phần trăm học viên đã hoàn thành lớp học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Personal Information"
msgstr "Thông tin cá nhân"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__phone
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__phone
msgid "Phone"
msgstr "Điện thoại"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__location_type__physical
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Physical"
msgstr "Vật lý"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__physical_location_id
msgid "Physical Location"
msgstr "Địa điểm vật lý"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__physical_location_id
msgid "Physical location for this lesson"
msgstr "Địa điểm vật lý cho buổi học này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__pie
msgid "Pie Chart"
msgstr "Biểu đồ tròn"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__item_type__pivot
msgid "Pivot"
msgstr "Bảng khô dữ liệu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__online_platform
msgid "Platform for online lessons (e.g., Zoom, Teams)"
msgstr "Nền tảng cho các buổi học trực tuyến (ví dụ: Zoom, Teams)"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__polar
msgid "Polar Chart"
msgstr "Biểu đồ cực"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Post"
msgstr "Ghi sổ"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__posted
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Posted"
msgstr "Đã ghi sổ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__schedule_id
msgid "Preferred Schedule"
msgstr "Lịch học ưu tiên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__schedule_id
msgid "Preferred schedule chosen by the student"
msgstr "Lịch học ưu tiên được học viên lựa chọn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__prerequisites
msgid "Prerequisites"
msgstr "Điều kiện tiên quyết"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__prerequisites
msgid "Prerequisites for the subject"
msgstr "Điều kiện tiên quyết cho môn học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__present
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__default_status__present
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Present"
msgstr "Có mặt"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__presentation
msgid "Presentation"
msgstr "Thuyết trình"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__primary
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__primary
msgid "Primary"
msgstr "Tiểu học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__primary_instructor_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Primary Instructor"
msgstr "Giảng viên chính"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__primary_instructor_id
msgid "Primary instructor for this class"
msgstr "Giảng viên chính cho lớp học này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__processed_by_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Processed By"
msgstr "Được xử lý bởi"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__processed_date
msgid "Processed Date"
msgstr "Ngày xử lý"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Processing Information"
msgstr "Thông tin xử lý"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__product_id
msgid "Product used for course enrollment and payment"
msgstr "Sản phẩm được sử dụng cho đăng ký và thanh toán khóa học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_qualification__level__professional
msgid "Professional"
msgstr "Chuyên nghiệp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__progress
msgid "Progress (%)"
msgstr "Tiến độ (%)"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__project
msgid "Project"
msgstr "Dự án"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__published
msgid "Published"
msgstr "Đã xuất bản"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__qr_code
msgid "QR Code"
msgstr "Mã QR"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_settings__allowed_check_in_methods__qr_only
msgid "QR Code Only"
msgstr "Chỉ mã QR"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__name
msgid "Qualification name"
msgstr "Tên bằng cấp"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__qualification_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Qualifications"
msgstr "Bằng cấp"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__quiz_id
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__quiz
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__assessment_type__quiz
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Quiz"
msgstr "Bài kiểm tra"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__quiz_id
msgid "Quiz for this content item"
msgstr "Bài kiểm tra cho mục nội dung này"

#. module: eb_lms
#: model:ir.module.category,description:eb_lms.module_category_eb_lms
msgid "Quản lý hệ thống đào tạo"
msgstr "Quản lý hệ thống đào tạo"

#. module: eb_lms
#: model:res.groups,comment:eb_lms.group_eb_lms_administrator
msgid "Quản trị viên có toàn quyền trên hệ thống LMS."
msgstr "Quản trị viên có toàn quyền trên hệ thống LMS."

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__chart_type__radar
msgid "Radar Chart"
msgstr "Biểu đồ radar"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__rating
msgid "Rating"
msgstr "Đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__comment
msgid "Rating comment"
msgstr "Bình luận đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__date
msgid "Rating date"
msgstr "Ngày đánh giá"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor_rating.py:0
msgid "Rating must be between 0 and 5."
msgstr "Đánh giá phải nằm trong khoảng từ 0 đến 5."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__state
msgid "Rating status"
msgstr "Trạng thái đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__name
msgid "Rating title"
msgstr "Tiêu đề đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__rating
msgid "Rating value (0-5)"
msgstr "Giá trị đánh giá (0-5)"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor.py:0
#: code:addons/eb_lms/models/people/student.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__rating_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__rating_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Ratings"
msgstr "Các đánh giá"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__rating_ids
msgid "Ratings given by this student"
msgstr "Các đánh giá được đưa ra bởi học viên này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__read
msgid "Read"
msgstr "Đã đọc"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__read_date
msgid "Read Date"
msgstr "Ngày đọc"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__cancellation_reason
msgid "Reason for cancellation"
msgstr "Lý do hủy"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__recommended_duration
msgid "Recommended Duration (hours)"
msgstr "Thời lượng đề xuất (giờ)"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__recommended_order
msgid "Recommended Order"
msgstr "Thứ tự đề xuất"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__assessment_methods
msgid "Recommended assessment methods for this subject"
msgstr "Phương pháp đánh giá đề xuất cho môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__recommended_duration
msgid "Recommended duration for teaching this subject"
msgstr "Thời lượng đề xuất để giảng dạy môn học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_subject__recommended_order
msgid "Recommended order to study this subject in the course"
msgstr "Thứ tự đề xuất để học môn học này trong khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__teaching_methods
msgid "Recommended teaching methods for this subject"
msgstr "Phương pháp giảng dạy đề xuất cho môn học này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_class__state__recruiting
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Recruiting"
msgstr "Tuyển sinh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__name
msgid "Reference"
msgstr "Tham chiếu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__reference_number
msgid "Reference Number"
msgstr "Số tham chiếu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__referral_source
msgid "Referral Source"
msgstr "Nguồn giới thiệu"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Refund"
msgstr "Hoàn tiền"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__refunded
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__refunded
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_payment__state__refunded
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Refunded"
msgstr "Đã hoàn tiền"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Reject"
msgstr "Từ chối"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_makeup__status__rejected
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_instructor_rating__state__rejected
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Rejected"
msgstr "Đã từ chối"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__related_model
msgid "Related Model"
msgstr "Mô hình liên quan"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__partner_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__partner_id
msgid "Related Partner"
msgstr "Đối tác liên quan"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__product_id
msgid "Related Product"
msgstr "Sản phẩm liên quan"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__related_id
msgid "Related Record ID"
msgstr "ID bản ghi liên quan"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__subject_id
msgid "Related Subject"
msgstr "Môn học liên quan"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__user_id
msgid "Related User"
msgstr "Người dùng liên quan"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__subject_id
msgid "Related subject for this skill"
msgstr "Môn học liên quan cho kỹ năng này"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Reminder Sent"
msgstr "Đã gửi nhắc nhở"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "Reminder: Course %s is starting soon"
msgstr "Nhắc nhở: Khóa học %s sắp bắt đầu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__request_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
msgid "Request Date"
msgstr "Ngày yêu cầu"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Reset to Scheduled"
msgstr "Đặt lại thành Đã lên lịch"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__activity_user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__activity_user_id
msgid "Responsible User"
msgstr "Người dùng phụ trách"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__role
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Role"
msgstr "Vai trò"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__role
msgid "Role of the user in this attendance record"
msgstr "Vai trò của người dùng trong bản ghi điểm danh này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__message_has_sms_error
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__schedule_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Schedule"
msgstr "Lịch trình"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__schedule_count
msgid "Schedule Count"
msgstr "Số lượng lịch trình"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__name
msgid "Schedule Name"
msgstr "Tên lịch trình"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__schedule_summary
msgid "Schedule Summary"
msgstr "Tóm tắt lịch trình"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__schedule_time
msgid "Schedule Time"
msgstr "Giờ lịch trình"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_schedule_weekday
msgid "Schedule Weekday"
msgstr "Ngày trong tuần lịch trình"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__schedule_id
msgid "Schedule selected by the student"
msgstr "Lịch trình được chọn bởi sinh viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__schedule_slot_ids
msgid "Schedule slots selected by the student"
msgstr "Các lịch trình được chọn bởi sinh viên"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_lesson__state__scheduled
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Scheduled"
msgstr "Đã lên lịch"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Schedules"
msgstr "Lịch trình"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__school
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__school
msgid "School/University"
msgstr "Trường học/Trường đại học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__secondary
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__secondary
msgid "Secondary"
msgstr "Trung học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Select a category"
msgstr "Chọn một thể loại"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select a course..."
msgstr "Chọn một khóa học..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select end date..."
msgstr "Chọn ngày kết thúc..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select learning type..."
msgstr "Chọn loại học tập..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select main location..."
msgstr "Chọn địa điểm chính..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
msgid "Select parent category (if any)"
msgstr "Chọn thể loại cha (nếu có)"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "Select parent category..."
msgstr "Chọn thể loại cha..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select primary instructor..."
msgstr "Chọn giảng viên chính..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Select start date..."
msgstr "Chọn ngày bắt đầu..."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__schedule_slot_ids
msgid "Selected Schedule Slots"
msgstr "Các lịch trình được chọn"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course_enrollment.py:0
msgid "Selected slots must belong to the selected schedule."
msgstr "Các lịch trình được chọn phải thuộc lịch trình được chọn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_schedule_weekday__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__sequence
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__sequence
msgid "Sequence"
msgstr "Thứ tự"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__sequence
msgid "Sequence for ordering"
msgstr "Thứ tự để sắp xếp"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__sequence
msgid "Sequence for ordering schedules"
msgstr "Thứ tự để sắp xếp lịch trình"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__sequence
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__sequence
msgid "Sequence order for displaying categories"
msgstr "Thứ tự để hiển thị các thể loại"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__sequence
msgid "Sequence order for displaying subjects"
msgstr "Thứ tự để hiển thị các môn học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_form
msgid "Set to Draft"
msgstr "Đặt về nháp"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Settings Name"
msgstr "Tên cài đặt"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__short_description
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__short_description
msgid "Short Description"
msgstr "Mô tả ngắn"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__show_in_menu
msgid "Show in Menu"
msgstr "Hiển thị trong menu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard_item__col_size
msgid "Size of the widget (1-12)"
msgstr "Kích thước của widget (1-12)"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__skill_count
msgid "Skill Count"
msgstr "Số lượng kỹ năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__category_id
msgid "Skill category"
msgstr "Thể loại kỹ năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__code
msgid "Skill code"
msgstr "Mã kỹ năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__description
msgid "Skill description"
msgstr "Mô tả kỹ năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__name
msgid "Skill name"
msgstr "Tên kỹ năng"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_instructor_skill_name_uniq
msgid "Skill name must be unique!"
msgstr "Tên kỹ năng phải là duy nhất!"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/people/instructor_skill.py:0
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__skill_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__skill_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_form
msgid "Skills"
msgstr "Kỹ năng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__skill_ids
msgid "Skills in this category"
msgstr "Kỹ năng trong thể loại này"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Start Class"
msgstr "Bắt đầu lớp"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Start Course"
msgstr "Bắt đầu khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__start_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__start_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__start_date
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__start_date
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__datetime_start
msgid "Start Datetime"
msgstr "Ngày giờ bắt đầu"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_form
msgid "Start Lesson"
msgstr "Bắt đầu buổi học"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Start Recruiting"
msgstr "Bắt đầu tuyển sinh"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__start_time
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__start_datetime
msgid "Start Time"
msgstr "Thời gian bắt đầu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__start_datetime
msgid "Start date and time"
msgstr "Ngày giờ bắt đầu"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__start_time
msgid "Start time for the classes (hours)"
msgstr "Thời gian bắt đầu cho các lớp (giờ)"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Statistics"
msgstr "Thống kê"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__status
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__state
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__status
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Status"
msgstr "Trạng thái"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__status_note
msgid "Status Note"
msgstr "Ghi chú trạng thái"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_state
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Ngày hết hạn đã qua\n"
"Hôm nay: Ngày hoạt động là hôm nay\n"
"Kế hoạch: Hoạt động trong tương lai."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Status note is required for 'Excused' or 'Cancelled' status."
msgstr "Ghi chú trạng thái là bắt buộc cho trạng thái 'Vắng mặt có phép' hoặc 'Hủy'."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__state
msgid "Status of the enrollment"
msgstr "Trạng thái của đăng ký"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__status
msgid "Status of the makeup request"
msgstr "Trạng thái của yêu cầu học bù"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__student_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__student_id
#: model:res.groups,name:eb_lms.group_eb_lms_student
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "Student"
msgstr "Học viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__code
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Student Code"
msgstr "Mã học viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__student_count
msgid "Student Count"
msgstr "Số lượng học viên"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard__dashboard_type__student
msgid "Student Dashboard"
msgstr "Bảng điều khiển học viên"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_class_student
msgid "Student Enrollments"
msgstr "Đăng ký học viên"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_form
msgid "Student Information"
msgstr "Thông tin học viên"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_form
msgid "Student Name"
msgstr "Tên học viên"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_student_payment
msgid "Student Payment"
msgstr "Thanh toán học viên"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_student_payment
#: model:ir.ui.menu,name:eb_lms.menu_student_payment
msgid "Student Payments"
msgstr "Các khoản thanh toán học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__address
msgid "Student address"
msgstr "Địa chỉ học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__attendance_ids
msgid "Student attendances"
msgstr "Điểm danh học viên"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_class_student_student_class_uniq
msgid "Student can only be enrolled once in a class!"
msgstr "Học viên chỉ có thể đăng ký một lần trong một lớp học!"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_student_student_code_unique
msgid "Student code must be unique!"
msgstr "Mã học viên phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__student_id
msgid "Student created from this lead"
msgstr "Học viên được tạo từ khách hàng tiềm năng này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__date_of_birth
msgid "Student date of birth"
msgstr "Ngày sinh của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__education_level
msgid "Student education level"
msgstr "Trình độ học vấn của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__email
msgid "Student email"
msgstr "Email của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__student_id
msgid "Student enrolled in the class"
msgstr "Học viên đăng ký trong lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__student_id
msgid "Student enrolling in the course"
msgstr "Học viên đăng ký khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__gender
msgid "Student gender"
msgstr "Giới tính của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__graduation_year
msgid "Student graduation year"
msgstr "Năm tốt nghiệp của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__identification
msgid "Student identification number (ID card, passport, etc.)"
msgstr "Số giấy tờ tùy thân của học viên (CMND, hộ chiếu, v.v.)"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__major
msgid "Student major"
msgstr "Chuyên ngành của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__mobile
msgid "Student mobile number"
msgstr "Số điện thoại di động của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__nationality_id
msgid "Student nationality"
msgstr "Quốc tịch của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__phone
msgid "Student phone number"
msgstr "Số điện thoại của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__image
msgid "Student profile image"
msgstr "Hình ảnh hồ sơ của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__school
msgid "Student school or university"
msgstr "Trường học hoặc đại học của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__status
msgid "Student status"
msgstr "Trạng thái của học viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__student_id
msgid "Student who gave the rating"
msgstr "Học viên đã đưa ra đánh giá"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
#: model:ir.actions.act_window,name:eb_lms.action_student
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__student_ids
#: model:ir.ui.menu,name:eb_lms.menu_student
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
msgid "Students"
msgstr "Học viên"

#. module: eb_lms
#: model_terms:ir.actions.act_window,help:eb_lms.action_attendance_makeup
msgid ""
"Students can request makeup classes when they miss a lesson.\n"
"                Administrators or instructors can approve or reject these requests."
msgstr "Học viên có thể yêu cầu học bù khi họ bỏ lỡ một buổi học.\n                Quản trị viên hoặc giảng viên có thể chấp nhận hoặc từ chối các yêu cầu này."

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__student_ids
msgid "Students enrolled in this class"
msgstr "Học viên đăng ký trong lớp học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__student_ids
msgid "Students who selected this schedule"
msgstr "Học viên đã chọn lịch học này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_subject__subject_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__subject_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Subject"
msgstr "Môn học"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_subject_category
#: model:ir.ui.menu,name:eb_lms.menu_subject_category
msgid "Subject Categories"
msgstr "Danh mục môn học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_subject_category
msgid "Subject Category"
msgstr "Danh mục môn học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__code
msgid "Subject Code"
msgstr "Mã môn học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__subject_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__subject_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__subject_count
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__subject_count
msgid "Subject Count"
msgstr "Số lượng môn học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__name
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "Subject Name"
msgstr "Tên môn học"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_subject_tag
msgid "Subject Tag"
msgstr "Thẻ môn học"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_subject_tag
#: model:ir.ui.menu,name:eb_lms.menu_subject_tag
msgid "Subject Tags"
msgstr "Thẻ môn học"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_subject_subject_code_unique
msgid "Subject code must be unique!"
msgstr "Mã môn học phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__subject_id
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__subject_id
msgid "Subject of this lesson"
msgstr "Môn học của buổi học này"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/classroom/class_model.py:0
#: code:addons/eb_lms/models/course/course.py:0
#: code:addons/eb_lms/models/subject/subject_category.py:0
#: code:addons/eb_lms/models/subject/subject_tag.py:0
#: model:ir.actions.act_window,name:eb_lms.action_subject
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__subject_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__subject_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__subject_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__subject_ids
#: model:ir.ui.menu,name:eb_lms.menu_lms_subject
#: model:ir.ui.menu,name:eb_lms.menu_lms_subject_config
#: model:ir.ui.menu,name:eb_lms.menu_subject
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "Subjects"
msgstr "Môn học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__subject_ids
msgid "Subjects in this category"
msgstr "Các môn học trong danh mục này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__subject_ids
msgid "Subjects in this course"
msgstr "Các môn học trong khóa học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__subject_ids
msgid "Subjects taught in this class"
msgstr "Các môn học được dạy trong lớp học này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__subject_interest_ids
msgid "Subjects this lead is interested in"
msgstr "Các môn học mà khách hàng tiềm năng này quan tâm"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__subject_ids
msgid "Subjects with this tag"
msgstr "Các môn học có thẻ này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_teacher__role__substitute
msgid "Substitute"
msgstr "Thay thế"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__status__substitute_arranged
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "Substitute Arranged"
msgstr "Đã sắp xếp người thay thế"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__substitute_instructor_id
msgid "Substitute Instructor"
msgstr "Giảng viên thay thế"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__substitute_instructor_id
msgid "Substitute instructor (if applicable)"
msgstr "Giảng viên thay thế (nếu có)"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance.py:0
msgid "Substitute instructor is required for 'Substitute Arranged' status."
msgstr "Giảng viên thay thế là bắt buộc cho trạng thái 'Đã sắp xếp người thay thế'."

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__success
msgid "Success"
msgstr "Thành công"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__schedule_summary
msgid "Summary of the class schedule"
msgstr "Tóm tắt lịch học của lớp"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__status__suspended
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_search
msgid "Suspended"
msgstr "Đã đình chỉ"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__syllabus
msgid "Syllabus"
msgstr "Giáo trình"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__syllabus
msgid "Syllabus for the subject"
msgstr "Giáo trình cho môn học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_attendance_attendance__check_in_method__system
msgid "System"
msgstr "Hệ thống"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__credits
msgid "Số tín chỉ"
msgstr "Số tín chỉ"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__credits
msgid "Số tín chỉ cho các môn học này trong khóa học"
msgstr "Số tín chỉ cho các môn học này trong khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__color
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__color
msgid "Tag color for UI"
msgstr "Màu thẻ cho giao diện người dùng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__description
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__description
msgid "Tag description"
msgstr "Mô tả thẻ"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__name
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__name
msgid "Tag name"
msgstr "Tên thẻ"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_course_tag_name_uniq
#: model:ir.model.constraint,message:eb_lms.constraint_eb_subject_tag_name_uniq
msgid "Tag name must be unique!"
msgstr "Tên thẻ phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_crm_lead__tag_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__tag_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__tag_ids
msgid "Tags"
msgstr "Thẻ"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__tag_ids
msgid "Tags for categorizing courses"
msgstr "Thẻ để phân loại khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__tag_ids
msgid "Tags for categorizing leads"
msgstr "Phân loại và phân tích danh mục lead/cơ hội như: Đào tạo, Dịch vụ"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__tag_ids
msgid "Tags for categorizing the subject"
msgstr "Thẻ để phân loại môn học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__tax_code
msgid "Tax Code"
msgstr "Mã số thuế"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__teacher_id
#: model:res.groups,name:eb_lms.group_eb_lms_teacher
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Teacher"
msgstr "Giáo viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__teacher_id
msgid "Teacher for this lesson"
msgstr "Giáo viên cho buổi học này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__teacher_ids
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "Teachers"
msgstr "Giáo viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__teacher_ids
msgid "Teachers assigned to this class"
msgstr "Giáo viên được phân công cho lớp học này"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__teaching_methods
msgid "Teaching Methods"
msgstr "Phương pháp giảng dạy"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__text
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Text"
msgstr "Văn bản"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__text_content
msgid "Text Content"
msgstr "Nội dung văn bản"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__text_content
msgid "Text content for this item"
msgstr "Nội dung văn bản cho mục này"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
msgid "The location is already booked for this time."
msgstr "Địa điểm này đã được đặt trước cho thời gian này."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "The makeup request has been approved and attendance record created."
msgstr "Yêu cầu học bù đã được phê duyệt và bản ghi điểm danh đã được tạo."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid "The makeup request has been rejected."
msgstr "Yêu cầu học bù đã bị từ chối."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/lesson/lesson.py:0
msgid "The teacher is already scheduled for this time."
msgstr "Giáo viên đã được lên lịch cho thời gian này."

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/attendance/attendance_makeup.py:0
msgid ""
"The user requesting makeup must match the user in the original attendance "
"record."
msgstr "Người dùng yêu cầu học bù phải trùng khớp với người dùng trong bản ghi điểm danh gốc."

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__month
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_search
msgid "This Month"
msgstr "Tháng này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__quarter
msgid "This Quarter"
msgstr "Quý này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__week
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "This Week"
msgstr "Tuần này"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__year
msgid "This Year"
msgstr "Năm này"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Thiết lập chung cho các môn học"
msgstr "Thiết lập chung cho các môn học"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/wizards/course_add_subject_wizard.py:0
msgid "Thành công"
msgstr "Thành công"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Thêm môn học"
msgstr "Thêm môn học"

#. module: eb_lms
#: model:ir.actions.act_window,name:eb_lms.action_course_add_subject_wizard
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_add_subject_wizard_form
msgid "Thêm môn học vào khóa học"
msgstr "Thêm môn học vào khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_add_subject_wizard__recommended_order
msgid "Thứ tự đề xuất"
msgstr "Thứ tự đề xuất"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_add_subject_wizard__recommended_order
msgid "Thứ tự đề xuất để học các môn học này trong khóa học"
msgstr "Thứ tự đề xuất để học các môn học này trong khóa học"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__time_range
msgid "Time Range"
msgstr "Khoảng thời gian"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_settings_form
msgid "Time Settings"
msgstr "Cài đặt thời gian"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__check_in_time
msgid "Time when the user checked in"
msgstr "Thời gian khi người dùng điểm danh vào"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__check_out_time
msgid "Time when the user checked out"
msgstr "Thời gian khi người dùng điểm danh ra"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__name
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__name
msgid "Title"
msgstr "Tiêu đề"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lms_dashboard_item__time_range__day
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Today"
msgstr "Hôm nay"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_instructor_payment_list
#: model_terms:ir.ui.view,arch_db:eb_lms.view_student_payment_list
msgid "Total"
msgstr "Tổng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__payment_amount
msgid "Total amount to be paid"
msgstr "Tổng số tiền cần thanh toán"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__type
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__notification_type
msgid "Type"
msgstr "Loại"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__learning_type
msgid "Type of learning: offline, online, or hybrid"
msgstr "Loại hình học tập: trực tiếp, trực tuyến hoặc kết hợp"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__type
msgid "Type of qualification"
msgstr "Loại bằng cấp"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__activity_exception_decoration
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trên bản ghi."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__name
msgid "Tên điểm danh"
msgstr "Tên điểm danh"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__video_url
msgid "URL for video content"
msgstr "URL cho nội dung video"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__code
msgid "Unique code for the class"
msgstr "Mã duy nhất cho lớp học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__code
msgid "Unique code for the course"
msgstr "Mã duy nhất cho khóa học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__code
msgid "Unique code for the instructor"
msgstr "Mã duy nhất cho giảng viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__code
msgid "Unique code for the student"
msgstr "Mã sinh viên"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__code
msgid "Unique code for the subject"
msgstr "Mã môn học"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__enrollment_number
msgid "Unique enrollment number"
msgstr "Số đăng ký"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__crm_lead__education_level__university
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_student_student__education_level__university
msgid "University"
msgstr "Trường đại học"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_class_student__payment_status__unpaid
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_course_enrollment__payment_status__unpaid
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_student_search
msgid "Unpaid"
msgstr "Chưa thanh toán"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__sequence
msgid "Used to order courses. Lower is better."
msgstr "Sử dụng để sắp xếp các khóa học. Thấp hơn là tốt hơn."

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__user_id
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__user_id
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_makeup_search
#: model_terms:ir.ui.view,arch_db:eb_lms.view_attendance_search
msgid "User"
msgstr "Người dùng"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__user_id
msgid "User (student or instructor) for this attendance record"
msgstr "Người dùng (sinh viên hoặc giảng viên) cho bản ghi điểm danh này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__user_id
msgid "User account for this instructor"
msgstr "Tài khoản người dùng cho giảng viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__user_id
msgid "User account for this student"
msgstr "Tài khoản người dùng cho sinh viên này"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__user_id
msgid "User requesting makeup"
msgstr "Người dùng yêu cầu học bù"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__processed_by_id
msgid "User who processed the request"
msgstr "Người dùng đã xử lý yêu cầu"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__role
msgid "Vai trò"
msgstr "Vai trò"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_lesson_content__content_type__video
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Video"
msgstr "Video"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__video_url
msgid "Video URL"
msgstr "URL video"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__is_visible
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard_item__is_visible
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_content_search
msgid "Visible"
msgstr "Hiển thị"

#. module: eb_lms
#: model:ir.model.fields.selection,name:eb_lms.selection__eb_notification__notification_type__warning
msgid "Warning"
msgstr "Cảnh báo"

#. module: eb_lms
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_attendance__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_makeup__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_attendance_settings__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_class__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_student__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_class_teacher__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_course__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_enrollment__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_schedule__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_course_tag__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_instructor__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_payment__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_qualification__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_rating__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_instructor_skill_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_content__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lesson_lesson__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_lms_dashboard__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_notification__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_payment__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_student_student__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_category__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_subject__website_message_ids
#: model:ir.model.fields,field_description:eb_lms.field_eb_subject_tag__website_message_ids
msgid "Website Messages"
msgstr "Tin nhắn Website"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_makeup__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_class_class__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_class_student__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_class_teacher__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_course__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_enrollment__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_schedule__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_course_tag__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_instructor__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_payment__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_qualification__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_rating__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_instructor_skill_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_content__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_lms_dashboard__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_notification__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_student_payment__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_student_student__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_category__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_subject__website_message_ids
#: model:ir.model.fields,help:eb_lms.field_eb_subject_tag__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử truyền thông website"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_lesson_search
msgid "Week"
msgstr "Tuần"

#. module: eb_lms
#: model:ir.model.constraint,message:eb_lms.constraint_eb_schedule_weekday_code_unique
msgid "Weekday code must be unique!"
msgstr "Mã ngày trong tuần phải là duy nhất!"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_attendance__is_all_day
msgid "Whether the attendance is for the whole day"
msgstr "Liệu điểm danh có dành cho cả ngày hay không"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_attendance_settings__active
msgid "Whether these settings are active"
msgstr "Liệu các cài đặt này có hoạt động hay không"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_crm_lead__converted
msgid "Whether this lead has been converted to a student"
msgstr "Có phải người này đã được chuyển đổi thành sinh viên hay không"

#. module: eb_lms
#: model:ir.model.fields,help:eb_lms.field_eb_lesson_lesson__has_assessment
msgid "Whether this lesson includes an assessment"
msgstr "Có phải bài giảng này bao gồm một bài kiểm tra hay không"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/models/course/course.py:0
msgid "You can add schedules directly in the list below."
msgstr "Bạn có thể thêm lịch trình trực tiếp vào danh sách dưới đây."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. 1"
msgstr "Ví dụ: 1"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. 10"
msgstr "Ví dụ: 10"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "e.g. 2"
msgstr "Ví dụ: 2"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. 30"
msgstr "Ví dụ: 30"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. 5"
msgstr "Ví dụ: 5"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_tag_form
msgid "e.g. Advanced, Beginner, Elective..."
msgstr "Ví dụ: Nâng cao, Bắt đầu, Tự chọn..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_tag_form
msgid "e.g. Beginner Friendly"
msgstr "Ví dụ: Dễ thương"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. CAT001"
msgstr "Ví dụ: CAT001"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. CLS-001"
msgstr "Ví dụ: CLS-001"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_form
msgid "e.g. MATH101, CS202, ENG305"
msgstr "Ví dụ: MATH101, CS202, ENG305"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. Monday and Wednesday 9:00-11:00 AM, Friday 2:00-4:00 PM"
msgstr "Ví dụ: Thứ hai và thứ tư 9:00-11:00 SA, thứ năm 2:00-4:00 CH"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. Programming Languages"
msgstr "Ví dụ: Ngôn ngữ lập trình"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_class_form
msgid "e.g. Python Programming - Spring 2025"
msgstr "Ví dụ: Lập trình Python - Xuân 2025"

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_subject_category_form
msgid "e.g. Sciences, Humanities, Engineering..."
msgstr "Ví dụ: Khoa học, Văn hóa, Cơ khí..."

#. module: eb_lms
#: model_terms:ir.ui.view,arch_db:eb_lms.view_course_category_form
msgid "e.g. fa-book"
msgstr "Ví dụ: fa-book"

#. module: eb_lms
#: model:ir.model,name:eb_lms.model_eb_attendance_attendance
msgid "Điểm danh cho học viên và giảng viên"
msgstr "Điểm danh cho học viên và giảng viên"

#. module: eb_lms
#. odoo-python
#: code:addons/eb_lms/wizards/course_add_subject_wizard.py:0
msgid "Đã thêm %s môn học vào khóa học"
msgstr "Đã thêm %s môn học vào khóa học"
