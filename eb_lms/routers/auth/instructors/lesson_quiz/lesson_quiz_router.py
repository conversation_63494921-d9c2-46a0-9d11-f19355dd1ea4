# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Instructor Lesson Quiz API Router

Endpoints for instructor lesson-quiz management:
- GET /lessons/{lesson_id}/quizzes: Get quizzes assigned to lesson
- POST /lessons/{lesson_id}/quizzes: Assign quiz to lesson
- DELETE /lessons/{lesson_id}/quizzes/{quiz_id}: Remove quiz from lesson
- POST /lessons/{lesson_id}/quizzes/{quiz_id}/publish: Publish quiz for lesson
- POST /lessons/{lesson_id}/quizzes/{quiz_id}/unpublish: Unpublish quiz for lesson
- PUT /lessons/{lesson_id}/quizzes/{quiz_id}: Update lesson-quiz settings
"""

import logging
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from odoo import _, fields
from odoo.exceptions import ValidationError, AccessError

from odoo.addons.eb_api_core.dependencies.auth import get_current_user
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_lms.dependencies.role_dependencies import require_instructor
from odoo.addons.eb_lms.utils.helpers import safe_value

from .schemas import (
    LessonQuizListItemSchema,
    LessonQuizDetailSchema,
    AssignQuizToLessonRequest,
    UpdateLessonQuizRequest,
    LessonQuizzesResponse,
    LessonQuizDetailResponse,
    AvailableQuizSchema,
    AvailableQuizzesResponse,
)

_logger = logging.getLogger(__name__)

lesson_quiz_router = APIRouter(prefix="/lessons", tags=["Instructor Lesson Quiz"])


@lesson_quiz_router.get(
    "/{lesson_id}/quizzes",
    response_model=LessonQuizzesResponse,
    summary="Get Lesson Quizzes",
    description="Get all quizzes assigned to a lesson"
)
def get_lesson_quizzes(
    lesson_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy danh sách quiz của buổi học"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy lesson và kiểm tra quyền
        lesson = current_user.env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy buổi học"
            )

        if lesson.teacher_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập buổi học này"
            )

        # Lấy danh sách lesson-quiz
        lesson_quizzes = lesson.lesson_quiz_ids

        quiz_items = []
        for lesson_quiz in lesson_quizzes:
            quiz_item = LessonQuizListItemSchema(
                id=lesson_quiz.id,
                quiz_id=lesson_quiz.quiz_id.id,
                quiz_name=safe_value(lesson_quiz.quiz_name) or lesson_quiz.quiz_id.name,
                quiz_type=lesson_quiz.quiz_id.quiz_type,
                sequence=lesson_quiz.sequence,
                is_active=lesson_quiz.is_active,
                status=safe_value(lesson_quiz.status) or "inactive",
                published_at=safe_value(lesson_quiz.published_at),
                published_by_name=lesson_quiz.published_by.name if lesson_quiz.published_by else None,
                can_publish=lesson_quiz.can_publish,
                can_unpublish=lesson_quiz.can_unpublish,
                quiz_question_count=lesson_quiz.quiz_id.question_count or 0,
                quiz_max_score=lesson_quiz.quiz_id.max_score,
                quiz_time_limit=safe_value(lesson_quiz.quiz_id.time_limit),
            )
            quiz_items.append(quiz_item)

        return LessonQuizzesResponse(
            success=True,
            message="Lấy danh sách quiz thành công",
            data=quiz_items,
            meta={
                "lesson_id": lesson.id,
                "lesson_name": lesson.name,
                "total_quizzes": len(quiz_items),
                "active_quizzes": len([q for q in quiz_items if q.is_active]),
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Get lesson quizzes error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@lesson_quiz_router.get(
    "/{lesson_id}/available-quizzes",
    response_model=AvailableQuizzesResponse,
    summary="Get Available Quizzes for Lesson",
    description="Get quizzes that can be assigned to this lesson"
)
def get_available_quizzes_for_lesson(
    lesson_id: int,
    subject_id: Optional[int] = Query(None, description="Lọc theo môn học"),
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy danh sách quiz có thể gán cho lesson"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy lesson và kiểm tra quyền
        lesson = current_user.env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy buổi học"
            )

        if lesson.teacher_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập buổi học này"
            )

        # Build domain cho quiz search
        domain = [
            ("instructor_id", "=", instructor.id),
            ("state", "=", "ready"),  # Chỉ quiz đã sẵn sàng
        ]

        # Thêm filter theo subject nếu có
        if subject_id:
            domain.append(("subject_id", "=", subject_id))

        # Lấy quizzes
        quizzes = current_user.env["eb.quiz"].search(
            domain,
            order="create_date desc"
        )

        # Convert to schema format
        quiz_items = []
        for quiz in quizzes:
            # Tính số buổi học đang công bố quiz này
            published_lesson_count = current_user.env["eb.lesson.quiz"].search_count([
                ("quiz_id", "=", quiz.id),
                ("is_active", "=", True)
            ])

            quiz_item = AvailableQuizSchema(
                id=quiz.id,
                name=quiz.name,
                code=safe_value(quiz.code) or f"QUIZ-{quiz.id}",
                quiz_type=quiz.quiz_type,
                state=quiz.state,
                question_count=quiz.question_count or 0,
                max_score=quiz.max_score,
                time_limit=safe_value(quiz.time_limit),
                passing_score=quiz.passing_score,
                subject_name=safe_value(quiz.subject_id.name) if quiz.subject_id else None,
                class_name=safe_value(quiz.class_id.name) if quiz.class_id else None,
                is_currently_published=published_lesson_count > 0,
                published_lesson_count=published_lesson_count,
            )
            quiz_items.append(quiz_item)

        return AvailableQuizzesResponse(
            success=True,
            message=f"Tìm thấy {len(quiz_items)} quiz có thể sử dụng",
            data=quiz_items
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Get available quizzes for lesson error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@lesson_quiz_router.post(
    "/{lesson_id}/quizzes",
    response_model=ResponseBase[LessonQuizDetailSchema],
    summary="Assign Quiz to Lesson",
    description="Assign a quiz to a lesson"
)
def assign_quiz_to_lesson(
    lesson_id: int,
    request: AssignQuizToLessonRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Gán quiz cho buổi học"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy lesson và kiểm tra quyền
        lesson = current_user.env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy buổi học"
            )

        if lesson.teacher_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền chỉnh sửa buổi học này"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(request.quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền sử dụng quiz này"
            )

        # Kiểm tra quiz đã được gán chưa
        existing = current_user.env["eb.lesson.quiz"].search([
            ("lesson_id", "=", lesson.id),
            ("quiz_id", "=", quiz.id),
        ])
        
        if existing:
            raise HTTPException(
                status_code=400,
                detail="Quiz này đã được gán cho buổi học rồi"
            )

        # Tạo lesson-quiz relationship
        lesson_quiz = current_user.env["eb.lesson.quiz"].create({
            "lesson_id": lesson.id,
            "quiz_id": quiz.id,
            "sequence": request.sequence or 10,
            "is_active": False,  # Chưa publish, instructor sẽ publish sau
            "notes": request.notes,
        })

        # Prepare response data
        lesson_quiz_data = LessonQuizDetailSchema(
            id=lesson_quiz.id,
            lesson_id=lesson_quiz.lesson_id.id,
            lesson_name=lesson_quiz.lesson_name,
            quiz_id=lesson_quiz.quiz_id.id,
            quiz_name=safe_value(lesson_quiz.quiz_name) or lesson_quiz.quiz_id.name,
            quiz_type=lesson_quiz.quiz_id.quiz_type,
            sequence=lesson_quiz.sequence,
            is_active=lesson_quiz.is_active,
            status=lesson_quiz.status,
            published_at=safe_value(lesson_quiz.published_at),
            published_by_name=lesson_quiz.published_by.name if lesson_quiz.published_by else None,
            unpublished_at=safe_value(lesson_quiz.unpublished_at),
            unpublished_by_name=lesson_quiz.unpublished_by.name if lesson_quiz.unpublished_by else None,
            notes=safe_value(lesson_quiz.notes),
            can_publish=lesson_quiz.can_publish,
            can_unpublish=lesson_quiz.can_unpublish,
            lesson_stage=lesson_quiz.lesson_stage,
            lesson_start_datetime=safe_value(lesson_quiz.lesson_start_datetime),
            quiz_question_count=lesson_quiz.quiz_id.question_count or 0,
            quiz_max_score=lesson_quiz.quiz_id.max_score,
            quiz_time_limit=safe_value(lesson_quiz.quiz_id.time_limit),
            quiz_passing_score=lesson_quiz.quiz_id.passing_score,
        )

        return ResponseBase(
            success=True,
            message="Gán quiz cho buổi học thành công",
            data=lesson_quiz_data
        )

    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        _logger.error(f"Assign quiz to lesson error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@lesson_quiz_router.post(
    "/{lesson_id}/quizzes/{quiz_id}/publish",
    response_model=ResponseBase[dict],
    summary="Publish Quiz for Lesson",
    description="Publish a quiz for students in a lesson"
)
def publish_quiz_for_lesson(
    lesson_id: int,
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Công bố quiz cho học viên trong buổi học"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Tìm lesson-quiz relationship
        lesson_quiz = current_user.env["eb.lesson.quiz"].search([
            ("lesson_id", "=", lesson_id),
            ("quiz_id", "=", quiz_id),
        ])
        
        if not lesson_quiz:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz trong buổi học này"
            )

        # Kiểm tra quyền
        if lesson_quiz.lesson_teacher_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền công bố quiz này"
            )

        # Publish quiz
        lesson_quiz.action_publish()

        return ResponseBase(
            success=True,
            message="Công bố quiz thành công",
            data={
                "lesson_quiz_id": lesson_quiz.id,
                "lesson_id": lesson_id,
                "quiz_id": quiz_id,
                "published_at": lesson_quiz.published_at.isoformat() if lesson_quiz.published_at else None,
                "is_active": lesson_quiz.is_active,
            }
        )

    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        _logger.error(f"Publish quiz for lesson error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@lesson_quiz_router.post(
    "/{lesson_id}/quizzes/{quiz_id}/unpublish",
    response_model=ResponseBase[dict],
    summary="Unpublish Quiz for Lesson",
    description="Unpublish a quiz for students in a lesson"
)
def unpublish_quiz_for_lesson(
    lesson_id: int,
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Hủy công bố quiz cho học viên trong buổi học"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Tìm lesson-quiz relationship
        lesson_quiz = current_user.env["eb.lesson.quiz"].search([
            ("lesson_id", "=", lesson_id),
            ("quiz_id", "=", quiz_id),
        ])

        if not lesson_quiz:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz trong buổi học này"
            )

        # Kiểm tra quyền
        if lesson_quiz.lesson_teacher_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền hủy công bố quiz này"
            )

        # Unpublish quiz
        lesson_quiz.action_unpublish()

        return ResponseBase(
            success=True,
            message="Hủy công bố quiz thành công",
            data={
                "lesson_quiz_id": lesson_quiz.id,
                "lesson_id": lesson_id,
                "quiz_id": quiz_id,
                "unpublished_at": lesson_quiz.unpublished_at.isoformat() if lesson_quiz.unpublished_at else None,
                "is_active": lesson_quiz.is_active,
            }
        )

    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        _logger.error(f"Unpublish quiz for lesson error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@lesson_quiz_router.delete(
    "/{lesson_id}/quizzes/{quiz_id}",
    response_model=ResponseBase[dict],
    summary="Remove Quiz from Lesson",
    description="Remove a quiz from a lesson"
)
def remove_quiz_from_lesson(
    lesson_id: int,
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Gỡ quiz khỏi buổi học"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Tìm lesson-quiz relationship
        lesson_quiz = current_user.env["eb.lesson.quiz"].search([
            ("lesson_id", "=", lesson_id),
            ("quiz_id", "=", quiz_id),
        ])

        if not lesson_quiz:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz trong buổi học này"
            )

        # Kiểm tra quyền
        if lesson_quiz.lesson_teacher_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền gỡ quiz này khỏi buổi học"
            )

        # Kiểm tra có thể gỡ không (không được gỡ nếu đã có attempt)
        attempts = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz_id),
        ])

        if attempts:
            raise HTTPException(
                status_code=400,
                detail="Không thể gỡ quiz đã có học viên làm bài"
            )

        # Lưu thông tin trước khi xóa
        quiz_name = lesson_quiz.quiz_name
        lesson_name = lesson_quiz.lesson_name

        # Xóa lesson-quiz relationship
        lesson_quiz.unlink()

        return ResponseBase(
            success=True,
            message=f"Gỡ quiz '{quiz_name}' khỏi buổi học '{lesson_name}' thành công",
            data={
                "lesson_id": lesson_id,
                "quiz_id": quiz_id,
                "removed_at": fields.Datetime.now().isoformat(),
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Remove quiz from lesson error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )
