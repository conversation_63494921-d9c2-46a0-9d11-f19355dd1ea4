# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Instructor Leave Management API Router

Endpoints for instructor leave request management:
- GET /leave-requests: Get instructor's leave requests
- POST /leave-requests: Create new leave request
- GET /leave-requests/{request_id}: Get leave request details
- PUT /leave-requests/{request_id}: Update leave request
- DELETE /leave-requests/{request_id}: Cancel leave request
- GET /leave-balance: Get leave balance information
"""

from typing import Annotated, Optional
from datetime import date

from fastapi import APIRouter, Depends, Query

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env

from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_lms.utils.helpers import safe_value
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.logging import get_request_logger

# Import dependencies
from odoo.addons.eb_lms.dependencies.role_dependencies import get_instructor_info
from odoo.addons.eb_lms.utils.logging_utils import log_user_action

# Import schemas
from odoo.addons.eb_lms.schemas.shared.pagination_schemas import (
    PaginationRequest,
    calculate_offset
)
from odoo.addons.eb_lms.schemas.instructors.leave_schemas import (
    LeaveRequestCreate,
    LeaveRequestUpdate,
    LeaveRequestInfo,
    LeaveBalance,
    LeaveRequestResponse,
    LeaveRequestListResponse,
    LeaveBalanceResponse,
    LeaveRequestCreateResponse,
    LeaveRequestUpdateResponse,
    LeaveRequestDeleteResponse,
)

import logging

_logger = logging.getLogger(__name__)

# Create routers
leave_requests_router = APIRouter(prefix="/requests", tags=["Instructor Leave Management"])
leave_router = APIRouter(prefix="/leave", tags=["Instructor Leave Management"])


def _build_leave_request_info(leave_request, env: Environment) -> LeaveRequestInfo:
    """Build leave request info from model record."""
    # Get approver info if exists
    approver_name = None
    if leave_request.approver_id:
        approver_name = leave_request.approver_id.name

    return LeaveRequestInfo(
        id=leave_request.id,
        code=leave_request.code,
        name=leave_request.name,
        instructor_id=leave_request.instructor_id.id,
        instructor_name=leave_request.instructor_id.name,
        request_date=safe_value(leave_request.request_date),
        start_date=safe_value(leave_request.start_date),
        end_date=safe_value(leave_request.end_date),
        leave_type=leave_request.leave_type,
        half_day_period=leave_request.half_day_period or None,
        time_from=safe_value(leave_request.time_from),
        time_to=safe_value(leave_request.time_to),
        reason_type=leave_request.reason_type,
        reason=leave_request.reason,
        state=leave_request.state,
        approver_id=leave_request.approver_id.id if leave_request.approver_id else None,
        approver_name=approver_name,
        approval_date=leave_request.approval_date or None,
        rejection_reason=leave_request.rejection_reason or None,
        affected_lesson_count=leave_request.affected_lesson_count,
        makeup_lesson_count=leave_request.makeup_lesson_count,
        created_at=safe_value(leave_request.create_date),
        updated_at=safe_value(leave_request.write_date)
    )


@leave_requests_router.get(
    "",
    response_model=LeaveRequestListResponse,
    summary="Get Leave Requests",
    description="Get instructor's leave requests with pagination and filtering"
)
@log_user_action("read", "leave_requests")
async def get_leave_requests(
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    pagination: Annotated[PaginationRequest, Depends()],
    state: Optional[str] = Query(None, description="Lọc theo trạng thái"),
    year: Optional[int] = Query(None, description="Lọc theo năm"),
    leave_type: Optional[str] = Query(None, description="Lọc theo loại nghỉ")
):
    """Get instructor's leave requests."""
    try:
        instructor = instructor_info["instructor"]

        # Build domain
        domain = [("instructor_id", "=", instructor.id)]

        if state:
            domain.append(("state", "=", state))
        if year:
            domain.extend([
                ("start_date", ">=", f"{year}-01-01"),
                ("start_date", "<=", f"{year}-12-31")
            ])
        if leave_type:
            domain.append(("leave_type", "=", leave_type))

        # Get total count
        total_count = env["eb.instructor.leave.request"].search_count(domain)

        # Calculate offset for pagination
        offset = calculate_offset(pagination.page, pagination.page_size)

        # Get paginated records
        leave_requests = env["eb.instructor.leave.request"].search(
            domain,
            limit=pagination.page_size,
            offset=offset,
            order="request_date desc, id desc"
        )

        # Build response data
        leave_request_infos = []
        for leave_request in leave_requests:
            leave_request_infos.append(_build_leave_request_info(leave_request, env))

        return LeaveRequestListResponse.create_leave_request_list_response(
            leave_requests=leave_request_infos,
            page=pagination.page,
            page_size=pagination.page_size,
            total_count=total_count
        )

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get leave requests error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải danh sách yêu cầu nghỉ: {str(e)}"
        )


@leave_requests_router.post(
    "",
    response_model=LeaveRequestCreateResponse,
    summary="Create Leave Request",
    description="Create a new leave request"
)
@log_user_action("create", "leave_request")
async def create_leave_request(
    request: LeaveRequestCreate,
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Create new leave request."""
    try:
        instructor = instructor_info["instructor"]

        # Check for overlapping leave requests
        overlapping = env["eb.instructor.leave.request"].search([
            ("instructor_id", "=", instructor.id),
            ("state", "in", ["submitted", "approved"]),
            "|",
            "&", ("start_date", "<=", request.start_date), ("end_date", ">=", request.start_date),
            "&", ("start_date", "<=", request.end_date), ("end_date", ">=", request.end_date)
        ])

        if overlapping:
            raise api_exception(
                ErrorCode.CONFLICT,
                f"Đã có yêu cầu nghỉ trong khoảng thời gian này: {overlapping[0].name}"
            )

        # Create leave request
        leave_vals = {
            "instructor_id": instructor.id,
            "start_date": safe_value(request.start_date),
            "end_date": safe_value(request.end_date),
            "leave_type": request.leave_type,
            "half_day_period": request.half_day_period,
            "time_from": safe_value(request.time_from),
            "time_to": safe_value(request.time_to),
            "reason_type": request.reason_type,
            "reason": request.reason,
            "state": "draft",
        }

        leave_request = env["eb.instructor.leave.request"].create(leave_vals)

        # Build response
        leave_request_info = _build_leave_request_info(leave_request, env)

        return LeaveRequestCreateResponse.create_leave_request_created_response(leave_request_info)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Create leave request error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tạo yêu cầu nghỉ: {str(e)}"
        )


@leave_requests_router.get(
    "/{request_id}",
    response_model=LeaveRequestResponse,
    summary="Get Leave Request Details",
    description="Get detailed information about a specific leave request"
)
@log_user_action("read", "leave_request")
async def get_leave_request(
    request_id: int,
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Get leave request details."""
    try:
        instructor = instructor_info["instructor"]

        # Get leave request
        leave_request = env["eb.instructor.leave.request"].search([
            ("id", "=", request_id),
            ("instructor_id", "=", instructor.id)
        ], limit=1)

        if not leave_request:
            raise api_exception(ErrorCode.NOT_FOUND, f"Yêu cầu nghỉ ID {request_id} không tồn tại")

        # Build response
        leave_request_info = _build_leave_request_info(leave_request, env)

        return LeaveRequestResponse.create_leave_request_response(leave_request_info)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get leave request error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải thông tin yêu cầu nghỉ: {str(e)}"
        )


@leave_requests_router.put(
    "/{request_id}",
    response_model=LeaveRequestUpdateResponse,
    summary="Update Leave Request",
    description="Update a leave request (only allowed in draft state)"
)
@log_user_action("update", "leave_request")
async def update_leave_request(
    request_id: int,
    request: LeaveRequestUpdate,
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Update leave request."""
    try:
        instructor = instructor_info["instructor"]

        # Get leave request
        leave_request = env["eb.instructor.leave.request"].search([
            ("id", "=", request_id),
            ("instructor_id", "=", instructor.id)
        ], limit=1)

        if not leave_request:
            raise api_exception(ErrorCode.NOT_FOUND, f"Yêu cầu nghỉ ID {request_id} không tồn tại")

        # Check if editable
        if leave_request.state not in ["draft"]:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                f"Không thể chỉnh sửa yêu cầu nghỉ ở trạng thái '{leave_request.state}'"
            )

        # Prepare update values
        update_vals = {}
        for field, value in request.model_dump(exclude_unset=True).items():
            if value is not None:
                update_vals[field] = value

        # Validate dates if both provided
        if "start_date" in update_vals and "end_date" in update_vals:
            if update_vals["end_date"] < update_vals["start_date"]:
                raise api_exception(ErrorCode.BAD_REQUEST, "Ngày kết thúc phải sau ngày bắt đầu")

        # Check for overlapping requests if dates changed
        if "start_date" in update_vals or "end_date" in update_vals:
            start_date = update_vals.get("start_date", leave_request.start_date)
            end_date = update_vals.get("end_date", leave_request.end_date)

            overlapping = env["eb.instructor.leave.request"].search([
                ("instructor_id", "=", instructor.id),
                ("id", "!=", request_id),
                ("state", "in", ["submitted", "approved"]),
                "|",
                "&", ("start_date", "<=", start_date), ("end_date", ">=", start_date),
                "&", ("start_date", "<=", end_date), ("end_date", ">=", end_date)
            ])

            if overlapping:
                raise api_exception(
                    ErrorCode.CONFLICT,
                    f"Đã có yêu cầu nghỉ trong khoảng thời gian này: {overlapping[0].name}"
                )

        # Update leave request
        if update_vals:
            leave_request.write(update_vals)

        # Build response
        leave_request_info = _build_leave_request_info(leave_request, env)

        return LeaveRequestUpdateResponse.create_leave_request_updated_response(leave_request_info)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Update leave request error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể cập nhật yêu cầu nghỉ: {str(e)}"
        )


@leave_requests_router.delete(
    "/{request_id}",
    response_model=LeaveRequestDeleteResponse,
    summary="Cancel Leave Request",
    description="Cancel a leave request (only allowed in draft state)"
)
@log_user_action("delete", "leave_request")
async def cancel_leave_request(
    request_id: int,
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Cancel leave request."""
    try:
        instructor = instructor_info["instructor"]

        # Get leave request
        leave_request = env["eb.instructor.leave.request"].search([
            ("id", "=", request_id),
            ("instructor_id", "=", instructor.id)
        ], limit=1)

        if not leave_request:
            raise api_exception(ErrorCode.NOT_FOUND, f"Yêu cầu nghỉ ID {request_id} không tồn tại")

        # Check if deletable
        if leave_request.state not in ["draft"]:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                f"Không thể hủy yêu cầu nghỉ ở trạng thái '{leave_request.state}'"
            )

        # Delete leave request
        leave_request.unlink()

        return LeaveRequestDeleteResponse.create_leave_request_deleted_response(request_id)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Cancel leave request error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể hủy yêu cầu nghỉ: {str(e)}"
        )


# Add leave balance endpoint to balance router
balance_router = APIRouter(tags=["Instructor Leave Management"])


@balance_router.get(
    "/balance",
    response_model=LeaveBalanceResponse,
    summary="Get Leave Balance",
    description="Get instructor's leave balance information"
)
@log_user_action("read", "leave_balance")
async def get_leave_balance(
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    year: Optional[int] = Query(None, description="Năm tính toán (mặc định năm hiện tại)")
):
    """Get instructor's leave balance."""
    try:
        instructor = instructor_info["instructor"]
        current_year = year or date.today().year

        # Get all leave requests for the year
        leave_requests = env["eb.instructor.leave.request"].search([
            ("instructor_id", "=", instructor.id),
            ("start_date", ">=", f"{current_year}-01-01"),
            ("start_date", "<=", f"{current_year}-12-31"),
            ("state", "in", ["approved"])
        ])

        # Calculate used days by type
        annual_leave_used = 0
        sick_leave_used = 0
        personal_leave_used = 0

        for leave_request in leave_requests:
            days_count = (leave_request.end_date - leave_request.start_date).days + 1

            # Adjust for half-day or hourly leave
            if leave_request.leave_type == "half_day":
                days_count = 0.5
            elif leave_request.leave_type == "hours":
                if leave_request.time_from and leave_request.time_to:
                    hours = leave_request.time_to - leave_request.time_from
                    days_count = hours / 8.0  # Assuming 8-hour work day

            if leave_request.reason_type == "vacation":
                annual_leave_used += days_count
            elif leave_request.reason_type == "sick":
                sick_leave_used += days_count
            else:
                personal_leave_used += days_count

        # Calculate totals
        annual_leave_total = 12  # Standard annual leave days
        annual_leave_remaining = max(0, annual_leave_total - annual_leave_used)
        total_leave_days = annual_leave_used + sick_leave_used + personal_leave_used

        # Count pending and approved requests
        all_requests = env["eb.instructor.leave.request"].search([
            ("instructor_id", "=", instructor.id),
            ("start_date", ">=", f"{current_year}-01-01"),
            ("start_date", "<=", f"{current_year}-12-31")
        ])

        pending_requests = len(all_requests.filtered(lambda r: r.state == "submitted"))
        approved_requests = len(all_requests.filtered(lambda r: r.state == "approved"))

        # Build balance info
        balance = LeaveBalance(
            instructor_id=instructor.id,
            instructor_name=instructor.name,
            annual_leave_total=annual_leave_total,
            annual_leave_used=int(annual_leave_used),
            annual_leave_remaining=int(annual_leave_remaining),
            sick_leave_used=int(sick_leave_used),
            personal_leave_used=int(personal_leave_used),
            total_leave_days_this_year=int(total_leave_days),
            pending_requests=pending_requests,
            approved_requests=approved_requests
        )

        return LeaveBalanceResponse.create_leave_balance_response(balance)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get leave balance error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải thông tin số ngày nghỉ: {str(e)}"
        )


# Include sub-routers into main leave router
leave_router.include_router(leave_requests_router)
leave_router.include_router(balance_router)
