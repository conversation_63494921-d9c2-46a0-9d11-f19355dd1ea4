# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Instructor Payment API Schemas

Schemas for instructor payment information endpoints.
"""

from typing import List, Optional
from datetime import date, datetime
from decimal import Decimal

from pydantic import BaseModel, Field

from odoo.addons.eb_api_core.schemas.base import ResponseBase


class InstructorPaymentSummary(BaseModel):
    """Instructor payment summary schema."""
    
    payment_id: int = Field(..., description="Payment ID")
    payment_code: str = Field(..., description="Payment code")
    payment_date: date = Field(..., description="Payment date")
    payment_type: str = Field(..., description="Payment type")
    amount: float = Field(..., description="Payment amount")
    currency: str = Field(..., description="Currency code")
    state: str = Field(..., description="Payment state")
    note: Optional[str] = Field(None, description="Payment note")


class InstructorSalaryPaymentSummary(BaseModel):
    """Instructor salary payment summary schema."""

    salary_payment_id: int = Field(..., description="Salary payment ID")
    payment_code: str = Field(..., description="Payment code")
    payment_date: date = Field(..., description="Payment date")
    lesson_date: Optional[date] = Field(None, description="Lesson date")
    lesson_type: Optional[str] = Field(None, description="Lesson type")
    base_salary: float = Field(..., description="Base salary")
    allowance: float = Field(..., description="Allowance")
    total_salary: float = Field(..., description="Total salary")
    currency: str = Field(..., description="Currency code")
    state: str = Field(..., description="Payment state")
    lesson_duration: Optional[float] = Field(None, description="Lesson duration in hours")


class InstructorPaymentDetail(BaseModel):
    """Instructor payment detail schema."""

    payment_id: int = Field(..., description="Payment ID")
    payment_code: str = Field(..., description="Payment code")
    payment_date: date = Field(..., description="Payment date")
    payment_type: str = Field(..., description="Payment type")
    amount: float = Field(..., description="Payment amount")
    currency: str = Field(..., description="Currency code")
    state: str = Field(..., description="Payment state")
    note: Optional[str] = Field(None, description="Payment note")

    # Related information
    course_name: Optional[str] = Field(None, description="Related course name")
    classes: Optional[List[str]] = Field(None, description="Related class names")
    lessons: Optional[List[str]] = Field(None, description="Related lesson names")

    # Payment method
    payment_method: Optional[str] = Field(None, description="Payment method")

    # Period information
    payment_period_start: Optional[date] = Field(None, description="Payment period start")
    payment_period_end: Optional[date] = Field(None, description="Payment period end")

    # Timestamps
    created_date: datetime = Field(..., description="Created date")

    # Accounting information
    invoice_state: Optional[str] = Field(None, description="Invoice state")
    salary_rule_name: Optional[str] = Field(None, description="Applied salary rule")


class InstructorSalaryPaymentDetail(BaseModel):
    """Instructor salary payment detail schema."""

    salary_payment_id: int = Field(..., description="Salary payment ID")
    payment_code: str = Field(..., description="Payment code")
    payment_date: date = Field(..., description="Payment date")
    lesson_date: Optional[date] = Field(None, description="Lesson date")
    lesson_type: Optional[str] = Field(None, description="Lesson type")

    # Salary breakdown
    base_salary: float = Field(..., description="Base salary")
    allowance: float = Field(..., description="Allowance")
    total_salary: float = Field(..., description="Total salary")
    currency: str = Field(..., description="Currency code")
    lesson_duration: Optional[float] = Field(None, description="Lesson duration in hours")

    # State and dates
    state: str = Field(..., description="Payment state")
    created_date: datetime = Field(..., description="Created date")
    note: Optional[str] = Field(None, description="Payment note")

    # Related information
    lesson_name: Optional[str] = Field(None, description="Lesson name")
    class_name: Optional[str] = Field(None, description="Class name")
    subject_name: Optional[str] = Field(None, description="Subject name")
    course_name: Optional[str] = Field(None, description="Course name")

    # Salary rule information
    salary_rule_name: Optional[str] = Field(None, description="Applied salary rule")
    rate_type: Optional[str] = Field(None, description="Rate type")
    rate_amount: Optional[float] = Field(None, description="Rate amount")

    # Consolidated payment info
    consolidated_payment_id: Optional[int] = Field(None, description="Consolidated payment ID")


class InstructorPaymentSummaryData(BaseModel):
    """Instructor payment summary data schema."""
    
    # Payment history
    payments: List[InstructorPaymentSummary] = Field(..., description="Payment history")
    salary_payments: List[InstructorSalaryPaymentSummary] = Field(..., description="Salary payment history")
    
    # Summary statistics
    total_payments: int = Field(..., description="Total number of payments")
    total_amount: float = Field(..., description="Total payment amount")
    total_salary_payments: int = Field(..., description="Total number of salary payments")
    total_salary_amount: float = Field(..., description="Total salary amount")
    
    # Current month
    current_month_payments: int = Field(..., description="Current month payments")
    current_month_amount: float = Field(..., description="Current month amount")
    
    # Pagination
    page: int = Field(..., description="Current page")
    page_size: int = Field(..., description="Page size")
    total_pages: int = Field(..., description="Total pages")


# Response schemas
class InstructorPaymentListResponse(ResponseBase[List[InstructorPaymentSummary]]):
    """Instructor payment list response schema."""

    @classmethod
    def create_payment_list_response(cls, payments: List[InstructorPaymentSummary], message: str = "Lấy danh sách thanh toán thành công"):
        """Create payment list response."""
        return cls.success_response(
            data=payments,
            message=message
        )


class InstructorSalaryPaymentListResponse(ResponseBase[List[InstructorSalaryPaymentSummary]]):
    """Instructor salary payment list response schema."""

    @classmethod
    def create_salary_list_response(cls, salary_payments: List[InstructorSalaryPaymentSummary], message: str = "Lấy danh sách lương thành công"):
        """Create salary payment list response."""
        return cls.success_response(
            data=salary_payments,
            message=message
        )


class InstructorPaymentDetailResponse(ResponseBase[InstructorPaymentDetail]):
    """Instructor payment detail response schema."""

    @classmethod
    def create_payment_detail_response(cls, payment: InstructorPaymentDetail, message: str = "Lấy chi tiết thanh toán thành công"):
        """Create payment detail response."""
        return cls.success_response(
            data=payment,
            message=message
        )


class InstructorSalaryPaymentDetailResponse(ResponseBase[InstructorSalaryPaymentDetail]):
    """Instructor salary payment detail response schema."""

    @classmethod
    def create_salary_detail_response(cls, salary_payment: InstructorSalaryPaymentDetail, message: str = "Lấy chi tiết lương thành công"):
        """Create salary payment detail response."""
        return cls.success_response(
            data=salary_payment,
            message=message
        )


class InstructorPaymentSummaryResponse(ResponseBase[InstructorPaymentSummaryData]):
    """Instructor payment summary response schema."""

    @classmethod
    def create_summary_response(cls, summary_data: InstructorPaymentSummaryData, message: str = "Lấy tổng hợp thanh toán thành công"):
        """Create payment summary response."""
        return cls.success_response(
            data=summary_data,
            message=message
        )
