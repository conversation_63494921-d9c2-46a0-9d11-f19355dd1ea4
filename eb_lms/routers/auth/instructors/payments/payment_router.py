# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Instructor Payment Management API Router

Endpoints for instructor payment information (read-only):
- GET /payments: Get payment history
- GET /payments/{payment_id}: Get payment details
- GET /payments/summary: Get payment summary
- GET /salary: Get salary payment history
- GET /salary/{salary_id}: Get salary payment details
"""

import logging
from typing import Annotated, List, Optional
from datetime import datetime, date, timedelta

from fastapi import APIRouter, Depends, Query, HTTPException

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env

from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_lms.utils.helpers import safe_value
from odoo.addons.eb_api_core.schemas.error import ErrorCode


def safe_value(value, default=None):
    """Helper function to handle False values from Odoo."""
    return value if value is not False else default

from odoo.addons.eb_lms.dependencies.role_dependencies import get_instructor_info

from .schemas import (
    InstructorPaymentSummary,
    InstructorSalaryPaymentSummary,
    InstructorPaymentDetail,
    InstructorSalaryPaymentDetail,
    InstructorPaymentSummaryData,
    InstructorPaymentListResponse,
    InstructorSalaryPaymentListResponse,
    InstructorPaymentDetailResponse,
    InstructorSalaryPaymentDetailResponse,
    InstructorPaymentSummaryResponse,
)

# Create routers
payment_router = APIRouter(prefix="/payments", tags=["Instructor Payments"])
salary_router = APIRouter(prefix="/salary", tags=["Instructor Salary"])

# Logger
_logger = get_request_logger(__name__)


@payment_router.get(
    "",
    response_model=InstructorPaymentListResponse,
    summary="Get Payment History",
    description="Get instructor payment history with filtering options"
)
async def get_payment_history(
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    date_from: Optional[date] = Query(None, description="Start date filter"),
    date_to: Optional[date] = Query(None, description="End date filter"),
    state: Optional[str] = Query(None, description="Payment state filter"),
    payment_type: Optional[str] = Query(None, description="Payment type filter")
):
    """Get instructor payment history."""
    try:
        instructor = instructor_info["instructor"]
        
        # Build domain for filtering
        domain = [("instructor_id", "=", instructor.id)]
        
        if date_from:
            domain.append(("payment_date", ">=", date_from))
        if date_to:
            domain.append(("payment_date", "<=", date_to))
        if state:
            domain.append(("state", "=", state))
        if payment_type:
            domain.append(("payment_type", "=", payment_type))
        
        # Get total count
        total_count = env["eb.instructor.payment"].search_count(domain)
        
        # Calculate pagination
        offset = (page - 1) * page_size
        
        # Get payments
        payments = env["eb.instructor.payment"].search(
            domain,
            limit=page_size,
            offset=offset,
            order="payment_date desc, id desc"
        )
        
        # Build response data
        payment_list = []
        for payment in payments:
            payment_data = InstructorPaymentSummary(
                payment_id=int(payment.id),
                payment_code=payment.name,
                payment_date=safe_value(payment.payment_date),
                payment_type=payment.payment_type or "manual",
                amount=payment.amount,
                currency=payment.currency_id.name if payment.currency_id else "VND",
                state=payment.state,
                note=safe_value(payment.note)
            )
            payment_list.append(payment_data)
        
        _logger.info(f"Retrieved {len(payment_list)} payments for instructor {instructor.name}")
        
        return InstructorPaymentListResponse.create_payment_list_response(
            payment_list,
            f"Lấy danh sách thanh toán thành công. Tìm thấy {total_count} thanh toán."
        )
        
    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get payment history error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải lịch sử thanh toán: {str(e)}"
        )


@payment_router.get(
    "/summary",
    response_model=InstructorPaymentSummaryResponse,
    summary="Get Payment Summary",
    description="Get comprehensive payment summary for instructor"
)
async def get_payment_summary(
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    months: int = Query(12, ge=1, le=24, description="Number of months to include")
):
    """Get payment summary."""
    try:
        instructor = instructor_info["instructor"]

        # Calculate date range
        end_date = date.today()
        start_date = end_date - timedelta(days=months * 30)

        # Get regular payments
        payment_domain = [
            ("instructor_id", "=", instructor.id),
            ("payment_date", ">=", start_date),
            ("payment_date", "<=", end_date)
        ]
        payments = env["eb.instructor.payment"].search(payment_domain, order="payment_date desc")

        # Get salary payments
        salary_domain = [
            ("instructor_id", "=", instructor.id),
            ("payment_date", ">=", start_date),
            ("payment_date", "<=", end_date)
        ]
        salary_payments = env["eb.instructor.salary.payment"].search(salary_domain, order="payment_date desc")

        # Build payment summaries
        payment_list = []
        for payment in payments:
            payment_data = InstructorPaymentSummary(
                payment_id=int(payment.id),
                payment_code=payment.name,
                payment_date=safe_value(payment.payment_date),
                payment_type=payment.payment_type or "manual",
                amount=float(payment.amount) if payment.amount else 0.0,
                currency=payment.currency_id.name if payment.currency_id else "VND",
                state=payment.state,
                note=safe_value(payment.note)
            )
            payment_list.append(payment_data)

        # Build salary payment summaries
        salary_payment_list = []
        for salary_payment in salary_payments:
            salary_data = InstructorSalaryPaymentSummary(
                salary_payment_id=int(salary_payment.id),
                payment_code=salary_payment.name,
                payment_date=safe_value(salary_payment.payment_date),
                lesson_date=safe_value(salary_payment.lesson_date),
                lesson_type=safe_value(salary_payment.lesson_type),
                base_salary=float(salary_payment.base_salary) if salary_payment.base_salary else 0.0,
                allowance=float(salary_payment.allowance) if salary_payment.allowance else 0.0,
                total_salary=float(salary_payment.total_salary) if salary_payment.total_salary else 0.0,
                currency=salary_payment.currency_id.name if salary_payment.currency_id else "VND",
                state=salary_payment.state,
                lesson_duration=safe_value(salary_payment.lesson_duration)
            )
            salary_payment_list.append(salary_data)

        # Calculate statistics
        total_amount = sum(p.amount for p in payments if p.amount)
        total_salary_amount = sum(sp.total_salary for sp in salary_payments if sp.total_salary)

        # Current month statistics
        current_month_start = date.today().replace(day=1)
        current_month_payments = payments.filtered(lambda p: p.payment_date >= current_month_start)
        current_month_salary = salary_payments.filtered(lambda sp: sp.payment_date >= current_month_start)
        current_month_amount = sum(p.amount for p in current_month_payments if p.amount) + sum(sp.total_salary for sp in current_month_salary if sp.total_salary)

        # Build summary data
        summary_data = InstructorPaymentSummaryData(
            payments=payment_list,
            salary_payments=salary_payment_list,
            total_payments=int(len(payments)),
            total_amount=float(total_amount),
            total_salary_payments=int(len(salary_payments)),
            total_salary_amount=float(total_salary_amount),
            current_month_payments=int(len(current_month_payments) + len(current_month_salary)),
            current_month_amount=float(current_month_amount),
            page=1,
            page_size=int(len(payment_list) + len(salary_payment_list)),
            total_pages=1
        )

        return InstructorPaymentSummaryResponse.create_summary_response(summary_data)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get payment summary error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải tổng hợp thanh toán: {str(e)}"
        )


@payment_router.get(
    "/{payment_id}",
    response_model=InstructorPaymentDetailResponse,
    summary="Get Payment Details",
    description="Get detailed information about a specific payment"
)
async def get_payment_details(
    payment_id: int,
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Get payment details."""
    try:
        instructor = instructor_info["instructor"]

        # Get payment record
        payment = env["eb.instructor.payment"].browse(payment_id)
        if not payment.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Thanh toán ID {payment_id} không tồn tại")

        # Check if payment belongs to instructor
        if payment.instructor_id.id != instructor.id:
            raise api_exception(ErrorCode.NOT_FOUND, f"Thanh toán ID {payment_id} không tồn tại")

        # Build detailed payment information
        payment_detail = InstructorPaymentDetail(
            payment_id=int(payment.id),
            payment_code=payment.name,
            payment_date=safe_value(payment.payment_date),
            payment_type=payment.payment_type or "manual",
            amount=payment.amount,
            currency=payment.currency_id.name if payment.currency_id else "VND",
            state=payment.state,
            note=safe_value(payment.note),
            course_name=payment.course_id.name if payment.course_id else None,
            classes=[cls.name for cls in payment.class_ids] if payment.class_ids else None,
            lessons=[lesson.name for lesson in payment.lesson_ids] if payment.lesson_ids else None,
            payment_method=payment.payment_method_id.name if payment.payment_method_id else None,
            payment_period_start=safe_value(payment.payment_period_start),
            payment_period_end=safe_value(payment.payment_period_end),
            created_date=safe_value(payment.create_date),
            invoice_state=safe_value(payment.invoice_state),
            salary_rule_name=payment.salary_rule_id.name if payment.salary_rule_id else None
        )

        return InstructorPaymentDetailResponse.create_payment_detail_response(payment_detail)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get payment details error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải chi tiết thanh toán: {str(e)}"
        )




# Salary endpoints
@salary_router.get(
    "",
    response_model=InstructorSalaryPaymentListResponse,
    summary="Get Salary Payment History",
    description="Get instructor salary payment history"
)
async def get_salary_history(
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    date_from: Optional[date] = Query(None, description="Start date filter"),
    date_to: Optional[date] = Query(None, description="End date filter"),
    state: Optional[str] = Query(None, description="Payment state filter")
):
    """Get instructor salary payment history."""
    try:
        instructor = instructor_info["instructor"]

        # Build domain for filtering
        domain = [("instructor_id", "=", instructor.id)]

        if date_from:
            domain.append(("payment_date", ">=", date_from))
        if date_to:
            domain.append(("payment_date", "<=", date_to))
        if state:
            domain.append(("state", "=", state))

        # Get total count
        total_count = env["eb.instructor.salary.payment"].search_count(domain)

        # Calculate pagination
        offset = (page - 1) * page_size

        # Get salary payments
        salary_payments = env["eb.instructor.salary.payment"].search(
            domain,
            limit=page_size,
            offset=offset,
            order="payment_date desc, id desc"
        )

        # Build response data
        salary_list = []
        for salary_payment in salary_payments:
            salary_data = InstructorSalaryPaymentSummary(
                salary_payment_id=int(salary_payment.id),
                payment_code=salary_payment.name,
                payment_date=safe_value(salary_payment.payment_date),
                lesson_date=safe_value(salary_payment.lesson_date),
                lesson_type=safe_value(salary_payment.lesson_type),
                base_salary=salary_payment.base_salary,
                allowance=salary_payment.allowance,
                total_salary=salary_payment.total_salary,
                currency=salary_payment.currency_id.name if salary_payment.currency_id else "VND",
                state=salary_payment.state,
                lesson_duration=safe_value(salary_payment.lesson_duration)
            )
            salary_list.append(salary_data)

        _logger.info(f"Retrieved {len(salary_list)} salary payments for instructor {instructor.name}")

        return InstructorSalaryPaymentListResponse.create_salary_list_response(
            salary_list,
            f"Lấy danh sách lương thành công. Tìm thấy {total_count} kỳ lương."
        )

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get salary history error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải lịch sử lương: {str(e)}"
        )


@salary_router.get(
    "/{salary_id}",
    response_model=InstructorSalaryPaymentDetailResponse,
    summary="Get Salary Payment Details",
    description="Get detailed information about a specific salary payment"
)
async def get_salary_details(
    salary_id: int,
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Get salary payment details."""
    try:
        instructor = instructor_info["instructor"]

        # Get salary payment record
        salary_payment = env["eb.instructor.salary.payment"].browse(salary_id)
        if not salary_payment.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Kỳ lương ID {salary_id} không tồn tại")

        # Check if salary payment belongs to instructor
        if salary_payment.instructor_id.id != instructor.id:
            raise api_exception(ErrorCode.NOT_FOUND, f"Kỳ lương ID {salary_id} không tồn tại")

        # Build detailed salary payment information
        salary_detail = InstructorSalaryPaymentDetail(
            salary_payment_id=int(salary_payment.id),
            payment_code=salary_payment.name,
            payment_date=safe_value(salary_payment.payment_date),
            lesson_date=safe_value(salary_payment.lesson_date),
            lesson_type=safe_value(salary_payment.lesson_type),
            base_salary=salary_payment.base_salary,
            allowance=salary_payment.allowance,
            total_salary=salary_payment.total_salary,
            currency=salary_payment.currency_id.name if salary_payment.currency_id else "VND",
            lesson_duration=safe_value(salary_payment.lesson_duration),
            state=salary_payment.state,
            created_date=safe_value(salary_payment.create_date),
            note=safe_value(salary_payment.note),
            lesson_name=salary_payment.lesson_id.name if salary_payment.lesson_id else None,
            class_name=salary_payment.class_id.name if salary_payment.class_id else None,
            subject_name=salary_payment.subject_id.name if salary_payment.subject_id else None,
            course_name=salary_payment.course_id.name if salary_payment.course_id else None,
            salary_rule_name=salary_payment.salary_rule_id.name if salary_payment.salary_rule_id else None,
            rate_type=safe_value(salary_payment.salary_rule_id.rate_type) if salary_payment.salary_rule_id else None,
            rate_amount=safe_value(salary_payment.salary_rule_id.rate_amount) if salary_payment.salary_rule_id else None,
            consolidated_payment_id=int(salary_payment.consolidated_payment_id.id) if salary_payment.consolidated_payment_id else None
        )

        return InstructorSalaryPaymentDetailResponse.create_salary_detail_response(salary_detail)

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get salary details error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải chi tiết lương: {str(e)}"
        )


_logger.info("Instructor Payment & Salary API routers registered successfully")
