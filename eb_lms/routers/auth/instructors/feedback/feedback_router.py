"""
Instructor Feedback API Router

This module provides feedback-related endpoints for instructors:
- GET /feedback/: Get feedback received from students
- POST /feedback/: Submit feedback about system/courses/students

Based on the evaluation system pattern used in Student API.
"""

from typing import Annotated, Optional, List, Dict, Any
from fastapi import APIRouter, Depends, Query, Body
from odoo import fields
from odoo.api import Environment

# Core imports
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_lms.exceptions.api_exceptions import api_exception, ErrorCode

# LMS imports
from odoo.addons.eb_lms.dependencies.role_dependencies import get_instructor_info
from odoo.addons.eb_lms.utils.logging_utils import log_user_action

import logging

from odoo.addons.eb_lms.utils.helpers import safe_value

_logger = logging.getLogger(__name__)

# Create feedback router
feedback_router = APIRouter(prefix="/feedback", tags=["Instructor Feedback"])


@feedback_router.get(
    "",
    summary="Get Feedback Received",
    description="Get all feedback received from students about instructor, courses, or lessons"
)
@log_user_action("read", "instructor_feedback_sent")
async def get_instructor_feedback(
    instructor_info: Annotated[dict, Depends(get_instructor_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    feedback_type: Optional[str] = Query(None, description="Filter by feedback type: student, course, lesson, system"),
    course_id: Optional[int] = Query(None, description="Filter by specific course"),
    lesson_id: Optional[int] = Query(None, description="Filter by specific lesson"),
    student_id: Optional[int] = Query(None, description="Filter by specific student"),
    state: Optional[str] = Query(None, description="Filter by state: submitted, approved, rejected"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("create_date", description="Sort by: create_date, overall_rating, evaluation_date"),
    sort_order: str = Query("desc", description="Sort order: asc, desc")
):
    """Get feedback sent by instructor (feedback that instructor has submitted)."""
    try:
        instructor = instructor_info["instructor"]

        # Build domain for evaluations where instructor is the evaluator (sender)
        domain = [
            ("evaluator_id", "=", instructor.user_id.id),
            ("evaluator_type", "=", "teacher")  # Match the evaluator_type used in POST
        ]
        
        # Add filters
        if state:
            domain.append(("state", "=", state))
        else:
            # Default to submitted and approved feedback
            domain.append(("state", "in", ["submitted", "approved"]))

        # Filter by feedback type (what the instructor gave feedback about)
        if feedback_type == "student" and student_id:
            # Feedback about a specific student
            domain.extend([
                ("related_model", "=", "eb.student.student"),
                ("res_id", "=", student_id)
            ])
        elif feedback_type == "course" and course_id:
            # Feedback about a specific course
            domain.extend([
                ("related_model", "=", "eb.course.course"),
                ("res_id", "=", course_id)
            ])
        elif feedback_type == "lesson" and lesson_id:
            # Feedback about a specific lesson
            domain.extend([
                ("related_model", "=", "eb.lesson.lesson"),
                ("res_id", "=", lesson_id)
            ])
        elif feedback_type == "system":
            # System feedback (general feedback about the LMS)
            domain.extend([
                ("related_model", "=", "eb.lms.system"),
                ("res_id", "=", 1)  # System feedback uses a dummy ID
            ])
        
        # Get total count
        total_count = env["eb.evaluation"].search_count(domain)
        
        # Calculate pagination
        offset = (page - 1) * page_size
        total_pages = (total_count + page_size - 1) // page_size
        
        # Build order
        order_field = sort_by if sort_by in ["create_date", "overall_rating", "evaluation_date"] else "create_date"
        order = f"{order_field} {sort_order}"
        
        # Get evaluations
        evaluations = env["eb.evaluation"].search(domain, limit=page_size, offset=offset, order=order)
        
        # Format response data
        feedback_items = []
        for evaluation in evaluations:
            # Get related object info (what the instructor gave feedback about)
            related_object_name = "Không xác định"
            if evaluation.related_model and evaluation.res_id:
                try:
                    related_obj = env[evaluation.related_model].browse(evaluation.res_id)
                    if related_obj.exists():
                        related_object_name = related_obj.name
                except:
                    pass

            # Determine feedback type from related_model (what the instructor gave feedback about)
            feedback_type_display = "Chung"
            if evaluation.related_model == "eb.student.student":
                feedback_type_display = "Học viên"
            elif evaluation.related_model == "eb.course.course":
                feedback_type_display = "Khóa học"
            elif evaluation.related_model == "eb.lesson.lesson":
                feedback_type_display = "Buổi học"
            elif evaluation.related_model == "eb.lms.system":
                feedback_type_display = "Hệ thống"
            
            feedback_item = {
                "id": evaluation.id,
                "name": evaluation.name,
                "feedback_type": feedback_type_display,
                "related_model": evaluation.related_model,
                "related_object_id": evaluation.res_id,
                "related_object_name": related_object_name,
                "evaluator_name": instructor.name,  # Always the instructor who sent the feedback
                "is_anonymous": evaluation.is_anonymous,
                "overall_rating": evaluation.overall_rating or 0,
                "summary": evaluation.summary or "",
                "state": evaluation.state,
                "evaluation_date": evaluation.evaluation_date.isoformat() if evaluation.evaluation_date else None,
                "create_date": evaluation.create_date.isoformat() if evaluation.create_date else None,
                "template_name": evaluation.template_id.name if evaluation.template_id else None
            }
            feedback_items.append(feedback_item)
        
        return {
            "success": True,
            "message": f"Tìm thấy {total_count} phản hồi đã gửi",
            "data": {
                "feedback": feedback_items,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                },
                "summary": {
                    "total_feedback": total_count,
                    "average_rating": sum(item["overall_rating"] for item in feedback_items) / len(feedback_items) if feedback_items else 0,
                    "feedback_types": {
                        "student": len([item for item in feedback_items if item["feedback_type"] == "Học viên"]),
                        "course": len([item for item in feedback_items if item["feedback_type"] == "Khóa học"]),
                        "lesson": len([item for item in feedback_items if item["feedback_type"] == "Buổi học"]),
                        "system": len([item for item in feedback_items if item["feedback_type"] == "Hệ thống"])
                    }
                }
            }
        }
        
    except Exception as e:
        _logger.error(f"Get instructor feedback error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải phản hồi: {str(e)}"
        )


@feedback_router.post(
    "",
    summary="Submit Instructor Feedback",
    description="Submit feedback about system, courses, students, or general issues"
)
@log_user_action("create", "instructor_feedback_submit")
async def submit_instructor_feedback(
    feedback_data: dict = Body(...),
    instructor_info: Annotated[dict, Depends(get_instructor_info)] = None,
    env: Annotated[Environment, Depends(odoo_env)] = None
):
    """Submit instructor feedback using the evaluation system."""
    try:
        instructor = instructor_info["instructor"]
        
        # Validate required fields
        rating = feedback_data.get('rating')
        content = feedback_data.get('content', '')
        feedback_type = feedback_data.get('type', 'general')
        
        if not rating or not (1 <= rating <= 5):
            raise api_exception(ErrorCode.BAD_REQUEST, "Đánh giá phải từ 1 đến 5 sao")
        
        if len(content.strip()) < 10:
            raise api_exception(ErrorCode.BAD_REQUEST, "Nội dung phản hồi phải có ít nhất 10 ký tự")
        
        # Determine target model and ID based on feedback type
        related_model = None
        res_id = None
        template_code = None
        
        if feedback_type == 'student' and feedback_data.get('student_id'):
            related_model = 'eb.student.student'
            res_id = feedback_data['student_id']
            template_code = 'STUDENT_EVAL_INSTRUCTOR'
        elif feedback_type == 'course' and feedback_data.get('course_id'):
            related_model = 'eb.course.course'
            res_id = feedback_data['course_id']
            template_code = 'COURSE_EVAL_INSTRUCTOR'
        elif feedback_type == 'system':
            # For system feedback, use instructor as the related object
            related_model = 'eb.instructor.instructor'
            res_id = instructor.id
            template_code = 'SYSTEM_FEEDBACK_INSTRUCTOR'
        else:
            # For general feedback
            related_model = 'eb.instructor.instructor'
            res_id = instructor.id
            template_code = 'GENERAL_FEEDBACK_INSTRUCTOR'
        
        # Find appropriate evaluation template
        template = env["eb.evaluation.template"].search([
            ('code', '=', template_code),
            ('evaluator_type', '=', 'teacher'),
            ('is_active', '=', True)
        ], limit=1)

        # If no specific template found, try to find a general one
        if not template:
            template = env["eb.evaluation.template"].search([
                ('evaluator_type', '=', 'teacher'),
                ('is_active', '=', True)
            ], limit=1)
        
        if not template:
            raise api_exception(ErrorCode.INTERNAL_SERVER_ERROR, "Không tìm thấy mẫu đánh giá phù hợp")
        
        # Create evaluation record
        evaluation_vals = {
            'name': f"Phản hồi từ {instructor.name} - {feedback_type}",
            'template_id': template.id,
            'evaluator_type': 'teacher',
            'evaluator_id': instructor.user_id.id if instructor.user_id else instructor.id,
            'related_model': related_model,
            'res_id': res_id,
            'evaluation_date': fields.Date.today(),
            'summary': content,
            'overall_rating': rating,
            'is_anonymous': feedback_data.get('is_anonymous', False),
            'state': 'submitted',
        }
        
        evaluation = env["eb.evaluation"].create(evaluation_vals)
        
        return {
            "success": True,
            "message": "Cảm ơn bạn đã gửi phản hồi!",
            "data": {
                "evaluation_id": evaluation.id,
                "feedback_id": evaluation.id,  # For backward compatibility
                "status": "submitted",
                "submitted_at": safe_value(evaluation.create_date).isoformat(),
                "will_be_reviewed": True,
                "estimated_response_time": "2-3 ngày làm việc",
                "overall_rating": evaluation.overall_rating
            }
        }
        
    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Submit instructor feedback error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể gửi phản hồi: {str(e)}"
        )


_logger.info("Instructor Feedback API router registered successfully")
