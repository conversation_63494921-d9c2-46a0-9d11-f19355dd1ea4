# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Student Grades API Router

Endpoints for student grade management:
- GET /grades: Get student grades overview
- GET /grades/details: Get detailed grade breakdown
- GET /grades/history: Get grade history
- GET /grades/course/{course_id}: Get grades for specific course
"""

import logging
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from odoo import _
from odoo.exceptions import ValidationError, AccessError

from odoo.addons.eb_api_core.dependencies.auth import get_current_user
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_lms.dependencies.role_dependencies import require_student

_logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/grades",
    tags=["Student Grades"],
)


@router.get(
    "/",
    summary="Get Student Grades Overview",
    description="Get overview of student grades across all courses"
)
def get_student_grades(
    page: int = Query(1, ge=1, description="Số trang"),
    limit: int = Query(20, ge=1, le=100, description="Số item mỗi trang"),
    course_id: Optional[int] = Query(None, description="Lọc theo khóa học"),
    assessment_type: Optional[str] = Query(None, description="Lọc theo loại đánh giá"),
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy tổng quan điểm số của học viên"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Tìm tất cả enrollment của học viên
        enrollments = current_user.env["eb.course.enrollment"].search([
            ("student_id", "=", student.id),
        ])

        if not enrollments:
            return ResponseBase.success_response(
                message="Học viên chưa đăng ký khóa học nào",
                data={
                    "grades": [],
                    "summary": {
                        "total_courses": 0,
                        "average_grade": 0.0,
                        "total_assessments": 0,
                        "passed_assessments": 0,
                        "pass_rate": 0.0
                    },
                    "pagination": {
                        "page": page,
                        "page_size": limit,
                        "total_count": 0,
                        "total_pages": 0,
                        "has_next": False,
                        "has_previous": False
                    }
                }
            )

        # Build domain for grade results
        domain = [("student_id", "=", student.id)]
        
        if course_id:
            domain.append(("course_id", "=", course_id))
        
        if assessment_type:
            domain.append(("assessment_id.assessment_type", "=", assessment_type))

        # Get grade results
        grade_results = current_user.env["eb.grade.result"].search(domain)

        # Calculate pagination
        total_count = len(grade_results)
        offset = (page - 1) * limit
        total_pages = (total_count + limit - 1) // limit
        
        # Get paginated results
        paginated_results = grade_results[offset:offset + limit]

        # Format grade data
        grades = []
        for result in paginated_results:
            grade_item = {
                "id": result.id,
                "course_name": result.course_id.name if result.course_id else "N/A",
                "course_code": result.course_id.code if result.course_id else "N/A",
                "assessment_name": result.assessment_id.name if result.assessment_id else "N/A",
                "assessment_type": result.assessment_id.assessment_type if result.assessment_id else "N/A",
                "score": result.final_score,
                "max_score": result.max_score,
                "score_percentage": result.score_percentage,
                "is_passed": result.is_passed,
                "passing_score": result.passing_score,
                "graded_date": result.graded_date.isoformat() if result.graded_date else None,
                "graded_by": result.graded_by.name if result.graded_by else "N/A",
                "feedback": result.feedback or "",
                "status": result.status
            }
            grades.append(grade_item)

        # Calculate summary statistics
        total_courses = len(enrollments)
        total_assessments = len(grade_results)
        passed_assessments = len(grade_results.filtered(lambda r: r.is_passed))
        pass_rate = (passed_assessments / total_assessments * 100) if total_assessments > 0 else 0.0
        
        # Calculate average grade from enrollments
        total_grade = sum(enrollment.current_grade for enrollment in enrollments if enrollment.current_grade)
        average_grade = total_grade / len(enrollments) if enrollments else 0.0

        summary = {
            "total_courses": total_courses,
            "average_grade": round(average_grade, 2),
            "total_assessments": total_assessments,
            "passed_assessments": passed_assessments,
            "pass_rate": round(pass_rate, 2)
        }

        return ResponseBase.success_response(
            message=f"Tìm thấy {total_count} kết quả điểm số",
            data={
                "grades": grades,
                "summary": summary,
                "pagination": {
                    "page": page,
                    "page_size": limit,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                }
            }
        )

    except Exception as e:
        _logger.error(f"Get student grades error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Không thể tải điểm số: {str(e)}"
        )


@router.get(
    "/course/{course_id}",
    summary="Get Grades for Specific Course",
    description="Get detailed grades for a specific course"
)
def get_course_grades(
    course_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy điểm số chi tiết cho một khóa học cụ thể"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Check if student is enrolled in the course
        enrollment = current_user.env["eb.course.enrollment"].search([
            ("student_id", "=", student.id),
            ("course_id", "=", course_id)
        ], limit=1)

        if not enrollment:
            raise HTTPException(
                status_code=404,
                detail="Học viên chưa đăng ký khóa học này"
            )

        # Get course info
        course = current_user.env["eb.course.course"].browse(course_id)
        if not course.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy khóa học"
            )

        # Get grade results for this course
        grade_results = current_user.env["eb.grade.result"].search([
            ("student_id", "=", student.id),
            ("course_id", "=", course_id)
        ])

        # Format detailed grade data
        assessments = []
        for result in grade_results:
            assessment_item = {
                "id": result.id,
                "assessment_name": result.assessment_id.name if result.assessment_id else "N/A",
                "assessment_type": result.assessment_id.assessment_type if result.assessment_id else "N/A",
                "weight": result.weight,
                "score": result.final_score,
                "max_score": result.max_score,
                "score_percentage": result.score_percentage,
                "is_passed": result.is_passed,
                "passing_score": result.passing_score,
                "graded_date": result.graded_date.isoformat() if result.graded_date else None,
                "graded_by": result.graded_by.name if result.graded_by else "N/A",
                "feedback": result.feedback or "",
                "status": result.status
            }
            assessments.append(assessment_item)

        # Calculate course summary
        total_assessments = len(grade_results)
        passed_assessments = len(grade_results.filtered(lambda r: r.is_passed))
        pass_rate = (passed_assessments / total_assessments * 100) if total_assessments > 0 else 0.0
        
        course_summary = {
            "course_name": course.name,
            "course_code": course.code,
            "current_grade": enrollment.current_grade,
            "total_assessments": total_assessments,
            "passed_assessments": passed_assessments,
            "pass_rate": round(pass_rate, 2),
            "enrollment_status": enrollment.state,
            "progress_percentage": enrollment.progress_percentage or 0.0
        }

        return ResponseBase.success_response(
            message=f"Chi tiết điểm số khóa học {course.name}",
            data={
                "course_summary": course_summary,
                "assessments": assessments
            }
        )

    except Exception as e:
        _logger.error(f"Get course grades error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Không thể tải điểm số khóa học: {str(e)}"
        )
