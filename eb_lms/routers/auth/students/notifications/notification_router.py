# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Student Notifications API Router

Endpoints for student notification management:
- GET /notifications: Get student notifications
- PUT /notifications/{notification_id}/read: Mark notification as read
- PUT /notifications/mark-all-read: Mark all notifications as read
- GET /notifications/unread-count: Get unread notifications count
"""

import logging
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from odoo import _
from odoo.exceptions import ValidationError, AccessError

from odoo.addons.eb_api_core.dependencies.auth import get_current_user
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_lms.dependencies.role_dependencies import require_student

_logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/notifications",
    tags=["Student Notifications"],
)


@router.get(
    "/",
    summary="Get Student Notifications",
    description="Get list of notifications for student"
)
def get_student_notifications(
    page: int = Query(1, ge=1, description="Số trang"),
    limit: int = Query(20, ge=1, le=100, description="Số item mỗi trang"),
    is_read: Optional[bool] = Query(None, description="Lọc theo trạng thái đã đọc"),
    notification_type: Optional[str] = Query(None, description="Lọc theo loại thông báo"),
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy danh sách thông báo của học viên"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Build domain for notifications
        # Sử dụng mail.message hoặc mail.notification để lấy thông báo
        domain = [
            ("partner_ids", "in", [student.user_id.partner_id.id]),
            ("message_type", "in", ["notification", "comment"])
        ]

        if is_read is not None:
            # Tìm mail.notification để check read status
            if is_read:
                domain.append(("needaction", "=", False))
            else:
                domain.append(("needaction", "=", True))

        # Get total count
        total_count = current_user.env["mail.message"].search_count(domain)

        # Calculate pagination
        offset = (page - 1) * limit
        total_pages = (total_count + limit - 1) // limit

        # Get messages
        messages = current_user.env["mail.message"].search(
            domain, 
            limit=limit, 
            offset=offset, 
            order="date desc"
        )

        # Format notifications
        notifications = []
        for message in messages:
            # Determine notification type from subject or model
            notif_type = "general"
            if message.model:
                if "course" in message.model:
                    notif_type = "course"
                elif "lesson" in message.model:
                    notif_type = "lesson"
                elif "payment" in message.model:
                    notif_type = "payment"
                elif "grade" in message.model:
                    notif_type = "grade"

            # Check if read by looking at mail.notification
            is_message_read = True
            notification_record = current_user.env["mail.notification"].search([
                ("mail_message_id", "=", message.id),
                ("res_partner_id", "=", student.user_id.partner_id.id)
            ], limit=1)
            
            if notification_record:
                is_message_read = not notification_record.is_read

            notification_item = {
                "id": message.id,
                "title": message.subject or "Thông báo",
                "content": message.body or "",
                "type": notif_type,
                "is_read": is_message_read,
                "created_at": message.date.isoformat() if message.date else None,
                "sender_name": message.author_id.name if message.author_id else "Hệ thống",
                "related_model": message.model or None,
                "related_id": message.res_id or None
            }
            notifications.append(notification_item)

        # Calculate unread count
        unread_count = current_user.env["mail.message"].search_count([
            ("partner_ids", "in", [student.user_id.partner_id.id]),
            ("message_type", "in", ["notification", "comment"]),
            ("needaction", "=", True)
        ])

        return ResponseBase.success_response(
            message=f"Tìm thấy {total_count} thông báo",
            data={
                "notifications": notifications,
                "unread_count": unread_count,
                "pagination": {
                    "page": page,
                    "page_size": limit,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                }
            }
        )

    except Exception as e:
        _logger.error(f"Get student notifications error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Không thể tải thông báo: {str(e)}"
        )


@router.get(
    "/unread-count",
    summary="Get Unread Notifications Count",
    description="Get count of unread notifications for student"
)
def get_unread_count(
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Lấy số lượng thông báo chưa đọc"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Count unread notifications
        unread_count = current_user.env["mail.message"].search_count([
            ("partner_ids", "in", [student.user_id.partner_id.id]),
            ("message_type", "in", ["notification", "comment"]),
            ("needaction", "=", True)
        ])

        return ResponseBase.success_response(
            message="Số lượng thông báo chưa đọc",
            data={
                "unread_count": unread_count,
                "student_id": student.id,
                "student_name": student.name
            }
        )

    except Exception as e:
        _logger.error(f"Get unread count error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Không thể tải số lượng thông báo: {str(e)}"
        )


@router.put(
    "/{notification_id}/read",
    summary="Mark Notification as Read",
    description="Mark a specific notification as read"
)
def mark_notification_read(
    notification_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Đánh dấu thông báo đã đọc"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Find the notification
        notification = current_user.env["mail.notification"].search([
            ("mail_message_id", "=", notification_id),
            ("res_partner_id", "=", student.user_id.partner_id.id)
        ], limit=1)

        if not notification:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông báo"
            )

        # Mark as read
        notification.write({"is_read": True})

        return ResponseBase.success_response(
            message="Đã đánh dấu thông báo đã đọc",
            data={
                "notification_id": notification_id,
                "is_read": True
            }
        )

    except Exception as e:
        _logger.error(f"Mark notification read error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Không thể đánh dấu thông báo: {str(e)}"
        )


@router.put(
    "/mark-all-read",
    summary="Mark All Notifications as Read",
    description="Mark all notifications as read for student"
)
def mark_all_notifications_read(
    current_user=Depends(get_current_user),
    _=Depends(require_student),
):
    """Đánh dấu tất cả thông báo đã đọc"""
    try:
        # Tìm student record
        student = current_user.env["eb.student.student"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not student:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin học viên"
            )

        # Find all unread notifications
        notifications = current_user.env["mail.notification"].search([
            ("res_partner_id", "=", student.user_id.partner_id.id),
            ("is_read", "=", False)
        ])

        # Mark all as read
        notifications.write({"is_read": True})

        return ResponseBase.success_response(
            message=f"Đã đánh dấu {len(notifications)} thông báo đã đọc",
            data={
                "marked_count": len(notifications),
                "student_id": student.id
            }
        )

    except Exception as e:
        _logger.error(f"Mark all notifications read error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Không thể đánh dấu tất cả thông báo: {str(e)}"
        )
