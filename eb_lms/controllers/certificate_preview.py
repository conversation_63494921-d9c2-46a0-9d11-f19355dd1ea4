# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import http
from odoo.http import request
import logging
import json
import base64
from datetime import datetime
from markupsafe import Markup

_logger = logging.getLogger(__name__)


class CertificatePreviewController(http.Controller):
    """
    Controller cho việc xem trước chứng chỉ
    """

    @http.route('/eb_lms/certificate/mobile/preview', methods=['GET'], type='http', auth='user', website=True)
    def certificate_mobile_preview_content(self):
        """
        Return the mobile preview content template for certificate
        Similar to mass_mailing mobile preview
        """
        return request.render("eb_lms.certificate_preview_content_mobile", {})

    @http.route('/eb_lms/certificate/preview', type='http', auth='user', website=True)
    def preview_certificate(self, **kwargs):
        """
        <PERSON><PERSON><PERSON> thị xem trước chứng chỉ

        Args:
            template_id: ID của mẫu chứng chỉ
            data: Dữ liệu JSON để render template

        Returns:
            HTML được render
        """
        try:
            template_id = int(kwargs.get('template_id', 0))
            data_json = kwargs.get('data', '{}')
            data = json.loads(data_json)

            if not template_id:
                return request.render('web.404')

            template = request.env['eb.certificate.template'].browse(template_id)
            if not template.exists():
                return request.render('web.404')

            # Chuẩn bị dữ liệu cho template
            certificate_data = {
                'certificate_number': data.get('certificate_number', 'CERT-PREVIEW-001'),
                'issue_date': datetime.strptime(data.get('issue_date', datetime.now().strftime('%Y-%m-%d')), '%Y-%m-%d').date(),
                'expiry_date': datetime.strptime(data.get('expiry_date', ''), '%Y-%m-%d').date() if data.get('expiry_date') else False,
                'grade': data.get('grade', 85),
                'grade_text': data.get('grade_text', 'Xuất sắc'),
                'student_id': {
                    'name': data.get('student_name', 'Nguyễn Văn A'),
                    'code': data.get('student_code', 'SV001'),
                },
                'course_id': {
                    'name': data.get('course_name', 'Khóa học mẫu'),
                    'code': data.get('course_code', 'KH001'),
                },
            }

            # Render HTML
            if template.body_html:
                # Xử lý theo phương pháp mới (QWeb)
                template_content = template.body_html

                # Thay thế các biến trong template
                for field in ['name', 'code']:
                    template_content = template_content.replace(
                        f'<t t-out="object.student_id.{field}"/>',
                        certificate_data['student_id'][field]
                    )
                    template_content = template_content.replace(
                        f'<t t-out="object.course_id.{field}"/>',
                        certificate_data['course_id'][field]
                    )

                # Thay thế các biến chứng chỉ
                for field in ['certificate_number', 'grade', 'grade_text']:
                    template_content = template_content.replace(
                        f'<t t-out="object.{field}"/>',
                        str(certificate_data[field])
                    )

                # Xử lý đặc biệt cho ngày tháng
                if certificate_data['issue_date']:
                    formatted_date = certificate_data['issue_date'].strftime("%d/%m/%Y")
                    template_content = template_content.replace(
                        '<t t-out="object.issue_date" t-options="{\'widget\': \'date\', \'format\': \'dd/MM/yyyy\'}"/>',
                        formatted_date
                    )
                    template_content = template_content.replace(
                        '<t t-out="object.issue_date"/>',
                        formatted_date
                    )

                if certificate_data['expiry_date']:
                    formatted_date = certificate_data['expiry_date'].strftime("%d/%m/%Y")
                    template_content = template_content.replace(
                        '<t t-out="object.expiry_date" t-options="{\'widget\': \'date\', \'format\': \'dd/MM/yyyy\'}"/>',
                        formatted_date
                    )
                    template_content = template_content.replace(
                        '<t t-out="object.expiry_date"/>',
                        formatted_date
                    )

                html_content = template_content
            else:
                # Xử lý theo phương pháp cũ (thay thế chuỗi)
                html_content = template.template_html

                # Tạo một dictionary chứa các biến thay thế
                variables = {
                    "${student.name}": certificate_data['student_id']['name'],
                    "${student.code}": certificate_data['student_id']['code'],
                    "${course.name}": certificate_data['course_id']['name'],
                    "${course.code}": certificate_data['course_id']['code'],
                    "${certificate.number}": certificate_data['certificate_number'],
                    "${certificate.issue_date}": certificate_data['issue_date'].strftime("%d/%m/%Y") if certificate_data['issue_date'] else "",
                    "${certificate.expiry_date}": certificate_data['expiry_date'].strftime("%d/%m/%Y") if certificate_data['expiry_date'] else "",
                    "${certificate.grade}": str(certificate_data['grade']),
                    "${certificate.grade_text}": certificate_data['grade_text'],
                }

                # Thay thế các biến trong mẫu HTML
                for var, value in variables.items():
                    html_content = html_content.replace(var, value)

            # Debug: Log the HTML content being returned
            _logger.info(f"Returning HTML content for template {template_id}: {html_content[:200]}...")

            # Return rendered content using template (similar to mass_mailing)
            # Use Markup to ensure HTML is not escaped
            return request.render(
                'eb_lms.certificate_preview_view',
                {
                    'body': Markup(html_content),
                    'template': template,
                },
            )
        except Exception as e:
            _logger.error(f"Error rendering certificate preview: {str(e)}")
            return request.render('web.500')

    @http.route('/eb_lms/certificate/mobile/download_pdf', type='http', auth='user', methods=['POST'], csrf=False)
    def download_mobile_preview_pdf(self, **kwargs):
        """
        Download PDF from mobile preview
        """
        try:
            html_content = kwargs.get('html_content', '')
            template_id = kwargs.get('template_id', '')

            if not html_content:
                return request.make_response(
                    'Không có nội dung HTML',
                    status=400,
                    headers={'Content-Type': 'text/plain'}
                )

            # Process HTML for PDF
            processed_html = self._process_html_for_pdf_controller(html_content)

            # Build complete HTML
            final_html = self._build_complete_html_controller(processed_html)

            # Generate PDF
            pdf_content = self._generate_pdf_controller(final_html)

            # Create filename
            filename = f"certificate_preview_{template_id or 'mobile'}_{int(datetime.now().timestamp())}.pdf"

            # Return PDF response
            return request.make_response(
                pdf_content,
                headers=[
                    ('Content-Type', 'application/pdf'),
                    ('Content-Disposition', f'attachment; filename="{filename}"'),
                    ('Content-Length', len(pdf_content)),
                ]
            )

        except Exception as e:
            _logger.error(f"Error generating mobile preview PDF: {str(e)}")
            return request.make_response(
                f'Lỗi khi tạo PDF: {str(e)}',
                status=500,
                headers={'Content-Type': 'text/plain'}
            )

    def _process_html_for_pdf_controller(self, html_content):
        """
        Process HTML for PDF in controller context
        """
        try:
            # Use certificate model's processing
            certificate_model = request.env['eb.certificate.certificate']

            # Create temporary certificate for context
            temp_certificate = certificate_model.new({})

            return certificate_model._prepare_html_for_pdf(html_content, temp_certificate)

        except Exception as e:
            _logger.warning(f"CSS processing failed in controller: {str(e)}")
            return self._add_basic_css_controller(html_content)

    def _add_basic_css_controller(self, html_content):
        """
        Add basic CSS for controller fallback
        """
        basic_css = """
        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: 'DejaVu Sans', 'Arial Unicode MS', Arial, sans-serif;
                background: white;
                color: #212529;
                line-height: 1.5;
                font-size: 14px;
            }
            .certificate-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: white;
            }
            .text-center {
                text-align: center;
            }
        </style>
        """
        return basic_css + html_content

    def _build_complete_html_controller(self, processed_html):
        """
        Build complete HTML document for controller
        """
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Certificate Preview</title>
        </head>
        <body>
            <div class="certificate-container">
                {processed_html}
            </div>
        </body>
        </html>
        """

    def _generate_pdf_controller(self, final_html):
        """
        Generate PDF in controller context
        """
        try:
            # Use certificate model's PDF generation
            certificate_model = request.env['eb.certificate.certificate']
            return certificate_model._generate_pdf_with_processing(final_html)

        except Exception as e:
            _logger.warning(f"Advanced PDF generation failed in controller: {str(e)}")

            # Fallback to basic generation
            return request.env['ir.actions.report']._run_wkhtmltopdf(
                [final_html],
                specific_paperformat_args={
                    'data-report-margin-top': 0,
                    'data-report-margin-bottom': 0,
                    'data-report-margin-left': 0,
                    'data-report-margin-right': 0,
                }
            )
