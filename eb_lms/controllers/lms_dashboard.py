# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
from datetime import datetime, timedelta
import logging


_logger = logging.getLogger(__name__)


class LmsDashboardController(http.Controller):

    @http.route('/eb_lms/dashboard/summary', type='json', auth='user')
    def get_dashboard_summary(self):
        """
        Lấy dữ liệu tổng quan cho dashboard LMS.

        Returns:
            dict: Dữ liệu tổng quan cho dashboard
        """
        try:
            # Khởi tạo giá trị mặc định
            result = {
                'total_courses': 0,
                'total_classes': 0,
                'total_students': 0,
                'total_instructors': 0,
                'total_lessons': 0,
                'total_subjects': 0,
                'total_evaluations': 0,
                'total_revenue': 0,
                'active_courses': 0,
                'active_classes': 0,
                'today_lessons': 0,
                'attendance_rate': 0,
                'student_change_pct': 0,
                'instructor_change_pct': 0,
                'revenue_change_pct': 0
            }

            # T<PERSON>h tổng số khóa học
            try:
                result['total_courses'] = request.env['eb.course.course'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số khóa học: {str(e)}")

            # Tính tổng số lớp học
            try:
                result['total_classes'] = request.env['eb.class.class'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số lớp học: {str(e)}")

            # Tính tổng số học viên
            try:
                result['total_students'] = request.env['eb.student.student'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số học viên: {str(e)}")

            # Tính tổng số giảng viên
            try:
                result['total_instructors'] = request.env['eb.instructor.instructor'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số giảng viên: {str(e)}")

            # Tính tổng số buổi học
            try:
                result['total_lessons'] = request.env['eb.lesson.lesson'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số buổi học: {str(e)}")

            # Tính tổng số môn học
            try:
                result['total_subjects'] = request.env['eb.subject.subject'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số môn học: {str(e)}")

            # Tính tổng số đánh giá
            try:
                result['total_evaluations'] = request.env['eb.evaluation'].search_count([])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm tổng số đánh giá: {str(e)}")

            # Tính tổng doanh thu
            try:
                total_revenue = 0
                invoices = request.env['account.move'].search([
                    ('move_type', '=', 'out_invoice'),
                    ('state', '=', 'posted'),
                    ('payment_state', 'in', ['paid', 'partial'])
                ])
                for invoice in invoices:
                    total_revenue += invoice.amount_total
                result['total_revenue'] = total_revenue
            except Exception as e:
                _logger.error(f"Lỗi khi tính tổng doanh thu: {str(e)}")

            # Tính số khóa học đang diễn ra
            try:
                result['active_courses'] = request.env['eb.course.course'].search_count([
                    ('stage_id.is_ongoing', '=', True)
                ])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm số khóa học đang diễn ra: {str(e)}")

            # Tính số lớp học đang diễn ra
            try:
                result['active_classes'] = request.env['eb.class.class'].search_count([
                    ('stage_id.is_ongoing', '=', True)
                ])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm số lớp học đang diễn ra: {str(e)}")

            # Tính số buổi học hôm nay
            try:
                today = datetime.now().strftime('%Y-%m-%d')
                result['today_lessons'] = request.env['eb.lesson.lesson'].search_count([
                    ('start_datetime', '>=', f'{today} 00:00:00'),
                    ('start_datetime', '<=', f'{today} 23:59:59')
                ])
            except Exception as e:
                _logger.error(f"Lỗi khi đếm số buổi học hôm nay: {str(e)}")

            # Tính tỷ lệ tham dự
            try:
                attendance_rate = 0
                attendances = request.env['eb.attendance.attendance'].search([])
                if attendances:
                    present_count = len(attendances.filtered(lambda a: a.status in ['present', 'late']))
                    attendance_rate = round((present_count / len(attendances)) * 100, 2)
                result['attendance_rate'] = int(attendance_rate)
            except Exception as e:
                _logger.error(f"Lỗi khi tính tỷ lệ tham dự: {str(e)}")

            # Tính % thay đổi so với tháng trước
            try:
                last_month_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                last_month_students = request.env['eb.student.student'].search_count([
                    ('create_date', '<', last_month_date)
                ])
                student_change_pct = 0
                if last_month_students > 0:
                    student_change_pct = round(((result['total_students'] - last_month_students) / last_month_students) * 100, 1)
                result['student_change_pct'] = int(student_change_pct)
            except Exception as e:
                _logger.error(f"Lỗi khi tính % thay đổi học viên: {str(e)}")

            try:
                last_month_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                last_month_instructors = request.env['eb.instructor.instructor'].search_count([
                    ('create_date', '<', last_month_date)
                ])
                instructor_change_pct = 0
                if last_month_instructors > 0:
                    instructor_change_pct = round(((result['total_instructors'] - last_month_instructors) / last_month_instructors) * 100, 1)
                result['instructor_change_pct'] = int(instructor_change_pct)
            except Exception as e:
                _logger.error(f"Lỗi khi tính % thay đổi giảng viên: {str(e)}")

            # Tính doanh thu tháng trước
            try:
                last_month_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                last_month_revenue = 0
                last_month_invoices = request.env['account.move'].search([
                    ('move_type', '=', 'out_invoice'),
                    ('state', '=', 'posted'),
                    ('payment_state', 'in', ['paid', 'partial']),
                    ('invoice_date', '<', datetime.now().strftime('%Y-%m-%d')),
                    ('invoice_date', '>=', last_month_date)
                ])
                for invoice in last_month_invoices:
                    last_month_revenue += invoice.amount_total

                revenue_change_pct = 0
                if last_month_revenue > 0:
                    current_month_revenue = result['total_revenue'] - last_month_revenue
                    revenue_change_pct = round(((current_month_revenue - last_month_revenue) / last_month_revenue) * 100, 1)
                result['revenue_change_pct'] = int(revenue_change_pct)
            except Exception as e:
                _logger.error(f"Lỗi khi tính % thay đổi doanh thu: {str(e)}")

            return result

        except Exception as e:
            _logger.error(f"Lỗi khi lấy dữ liệu tổng quan: {str(e)}")
            return {
                'total_courses': 0,
                'total_classes': 0,
                'total_students': 0,
                'total_instructors': 0,
                'total_lessons': 0,
                'total_subjects': 0,
                'total_evaluations': 0,
                'total_revenue': 0,
                'active_courses': 0,
                'active_classes': 0,
                'today_lessons': 0,
                'attendance_rate': 0,
                'student_change_pct': 0,
                'instructor_change_pct': 0,
                'revenue_change_pct': 0
            }

    @http.route('/eb_lms/dashboard/charts', type='json', auth='user')
    def get_dashboard_charts(self):
        """
        Lấy dữ liệu biểu đồ cho dashboard LMS.

        Returns:
            dict: Dữ liệu biểu đồ cho dashboard
        """
        try:
            # Biểu đồ khóa học theo trạng thái
            course_by_status_data = []
            try:
                stages = request.env['eb.course.stage'].search([])
                for stage in stages:
                    try:
                        count = request.env['eb.course.course'].search_count([('stage_id', '=', stage.id)])
                        if count > 0:
                            course_by_status_data.append({
                                'label': stage.name,
                                'value': count
                            })
                    except Exception as e:
                        _logger.error(f"Lỗi khi xử lý trạng thái khóa học {stage.id}: {str(e)}")
                        continue
            except Exception as e:
                _logger.error(f"Lỗi khi lấy danh sách trạng thái khóa học: {str(e)}")

            # Biểu đồ doanh thu theo tháng
            revenue_by_month_data = []

            try:
                # Lấy dữ liệu doanh thu 12 tháng gần nhất
                # Tạo truy vấn SQL để lấy doanh thu theo tháng
                query = """
                    SELECT
                        TO_CHAR(invoice_date, 'YYYY-MM') AS month,
                        SUM(amount_total) AS revenue
                    FROM
                        account_move
                    WHERE
                        move_type = 'out_invoice'
                        AND state = 'posted'
                        AND payment_state IN ('paid', 'partial')
                        AND invoice_date >= (CURRENT_DATE - INTERVAL '12 months')
                    GROUP BY
                        TO_CHAR(invoice_date, 'YYYY-MM')
                    ORDER BY
                        month
                """

                request.env.cr.execute(query)
                results = request.env.cr.dictfetchall()

                for result in results:
                    revenue_by_month_data.append({
                        'label': result['month'],
                        'value': round(result['revenue'], 2)
                    })
            except Exception as e:
                _logger.error(f"Lỗi khi lấy dữ liệu doanh thu theo tháng: {str(e)}")

            # Biểu đồ học viên theo khóa học
            students_by_course_data = []
            try:
                courses = request.env['eb.course.course'].search([], limit=10, order='id desc')
                for course in courses:
                    try:
                        enrollment_count = request.env['eb.course.enrollment'].search_count([
                            ('course_id', '=', course.id),
                            ('state', '=', 'paid')
                        ])
                        if enrollment_count > 0:
                            students_by_course_data.append({
                                'label': course.name,
                                'value': enrollment_count
                            })
                    except Exception as e:
                        _logger.error(f"Lỗi khi xử lý khóa học {course.id} cho biểu đồ học viên: {str(e)}")
                        continue
            except Exception as e:
                _logger.error(f"Lỗi khi lấy danh sách khóa học cho biểu đồ học viên: {str(e)}")

            # Biểu đồ đánh giá theo đối tượng
            evaluation_by_model_data = []
            try:
                model_mapping = {
                    'eb.course.course': 'Khóa học',
                    'eb.class.class': 'Lớp học',
                    'eb.instructor.instructor': 'Giảng viên',
                    'eb.lesson.lesson': 'Buổi học',
                    'eb.student.student': 'Học viên'
                }

                for model, label in model_mapping.items():
                    try:
                        count = request.env['eb.evaluation'].search_count([('related_model', '=', model)])
                        if count > 0:
                            evaluation_by_model_data.append({
                                'label': label,
                                'value': count
                            })
                    except Exception as e:
                        _logger.error(f"Lỗi khi xử lý đánh giá cho model {model}: {str(e)}")
                        continue
            except Exception as e:
                _logger.error(f"Lỗi khi lấy dữ liệu đánh giá theo đối tượng: {str(e)}")

            return {
                'course_by_status_data': course_by_status_data,
                'revenue_by_month_data': revenue_by_month_data,
                'students_by_course_data': students_by_course_data,
                'evaluation_by_model_data': evaluation_by_model_data
            }
        except Exception as e:
            _logger.error(f"Lỗi khi lấy dữ liệu biểu đồ: {str(e)}")
            return {
                'course_by_status_data': [],
                'revenue_by_month_data': [],
                'students_by_course_data': [],
                'evaluation_by_model_data': []
            }

    @http.route('/eb_lms/dashboard/upcoming_events', type='json', auth='user')
    def get_upcoming_events(self, limit=6):
        """
        Lấy danh sách sự kiện sắp diễn ra.

        Args:
            limit: Số lượng sự kiện tối đa để hiển thị

        Returns:
            list: Danh sách sự kiện sắp diễn ra
        """
        try:
            events = []

            # Lấy ngày hiện tại
            now = datetime.now()

            # Lấy các buổi học sắp diễn ra
            try:
                upcoming_lessons = request.env['eb.lesson.lesson'].search([
                    ('start_datetime', '>=', now.strftime('%Y-%m-%d %H:%M:%S')),
                    ('start_datetime', '<=', (now + timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S'))
                ], limit=limit // 2, order='start_datetime asc')

                for lesson in upcoming_lessons:
                    try:
                        location = ""
                        if lesson.location_id and lesson.location_id.exists():
                            location = lesson.location_id.name
                            if lesson.room_id and lesson.room_id.exists():
                                location += f" - {lesson.room_id.name}"

                        events.append({
                            'id': lesson.id,
                            'name': lesson.name,
                            'type': 'lesson',
                            'type_name': 'Buổi học',
                            'type_class': 'lesson-event',
                            'date': lesson.start_datetime,
                            'location': location,
                            'description': lesson.description or ""
                        })
                    except Exception as e:
                        _logger.error(f"Lỗi khi xử lý buổi học {lesson.id}: {str(e)}")
                        continue
            except Exception as e:
                _logger.error(f"Lỗi khi lấy danh sách buổi học sắp diễn ra: {str(e)}")

            # Lấy các đánh giá sắp diễn ra
            try:
                upcoming_evaluations = request.env['eb.evaluation.schedule'].search([
                    ('start_date', '>=', now.strftime('%Y-%m-%d')),
                    ('start_date', '<=', (now + timedelta(days=7)).strftime('%Y-%m-%d'))
                ], limit=limit // 2, order='start_date asc')

                for evaluation in upcoming_evaluations:
                    try:
                        events.append({
                            'id': evaluation.id,
                            'name': evaluation.name,
                            'type': 'evaluation',
                            'type_name': 'Đánh giá',
                            'type_class': 'evaluation-event',
                            'date': evaluation.start_date,
                            'location': "",
                            'description': evaluation.notes or ""
                        })
                    except Exception as e:
                        _logger.error(f"Lỗi khi xử lý đánh giá {evaluation.id}: {str(e)}")
                        continue
            except Exception as e:
                _logger.error(f"Lỗi khi lấy danh sách đánh giá sắp diễn ra: {str(e)}")

            # Sắp xếp sự kiện theo thời gian
            events.sort(key=lambda x: x['date'])

            # Giới hạn số lượng sự kiện
            return events[:limit]

        except Exception as e:
            _logger.error(f"Lỗi khi lấy sự kiện sắp diễn ra: {str(e)}")
            return []

    @http.route('/eb_lms/dashboard/popular_courses', type='json', auth='user')
    def get_popular_courses(self, limit=5):
        """
        Lấy danh sách khóa học phổ biến nhất dựa trên số lượng học viên đăng ký.

        Args:
            limit: Số lượng khóa học tối đa để hiển thị

        Returns:
            list: Danh sách khóa học phổ biến
        """
        try:
            # Lấy tất cả khóa học
            courses = request.env['eb.course.course'].search([], limit=100)

            # Tính số lượng học viên đã đăng ký cho mỗi khóa học
            course_data = []
            for course in courses:
                try:
                    enrollment_count = request.env['eb.course.enrollment'].search_count([
                        ('course_id', '=', course.id),
                        ('state', '=', 'paid')
                    ])

                    if enrollment_count > 0:
                        course_data.append({
                            'id': course.id,
                            'name': course.name,
                            'count': enrollment_count,
                            'icon_class': 'red' if len(course_data) % 3 == 0 else ('blue' if len(course_data) % 3 == 1 else 'purple')
                        })
                except Exception as e:
                    _logger.error(f"Lỗi khi xử lý khóa học {course.id}: {str(e)}")
                    continue

            # Sắp xếp theo số lượng học viên giảm dần
            course_data.sort(key=lambda x: x['count'], reverse=True)

            # Tính tổng số học viên để tính phần trăm
            total_enrollments = sum(course['count'] for course in course_data)

            # Thêm phần trăm vào dữ liệu
            for course in course_data:
                course['percentage'] = round((course['count'] / total_enrollments) * 100) if total_enrollments > 0 else 0

            # Giới hạn số lượng khóa học
            return course_data[:limit]

        except Exception as e:
            _logger.error(f"Lỗi khi lấy khóa học phổ biến: {str(e)}")
            return []

    @http.route('/eb_lms/dashboard/reports', type='json', auth='user')
    def get_reports(self):
        """
        Lấy dữ liệu báo cáo cho dashboard LMS.

        Returns:
            dict: Dữ liệu báo cáo cho dashboard
        """
        try:
            # Đếm số lượng đánh giá theo loại người đánh giá
            student_evaluations = request.env['eb.evaluation'].search_count([
                ('evaluator_type', '=', 'student')
            ])

            teacher_evaluations = request.env['eb.evaluation'].search_count([
                ('evaluator_type', '=', 'teacher')
            ])

            manager_evaluations = request.env['eb.evaluation'].search_count([
                ('evaluator_type', '=', 'manager')
            ])

            # Tính tổng số đánh giá
            total_evaluations = student_evaluations + teacher_evaluations + manager_evaluations

            # Tạo dữ liệu cho biểu đồ
            chart_data = {
                'current': total_evaluations,
                'total': total_evaluations,
                'percentage': 100 if total_evaluations > 0 else 0
            }

            # Tạo danh sách báo cáo
            reports = [
                {
                    'id': 1,
                    'name': 'Đánh giá từ học viên',
                    'count': student_evaluations
                },
                {
                    'id': 2,
                    'name': 'Đánh giá từ giảng viên',
                    'count': teacher_evaluations
                },
                {
                    'id': 3,
                    'name': 'Đánh giá từ quản lý',
                    'count': manager_evaluations
                }
            ]

            return {
                'reports': reports,
                'chart_data': chart_data
            }
        except Exception as e:
            _logger.error(f"Lỗi khi lấy dữ liệu báo cáo: {str(e)}")
            return {
                'reports': [],
                'chart_data': {
                    'current': 0,
                    'total': 0,
                    'percentage': 0
                }
            }

    @http.route('/eb_lms/dashboard/activities', type='json', auth='user')
    def get_activities(self, limit=5):
        """
        Lấy danh sách các activity (nhắc nhở/todo) của người dùng hiện tại.

        Args:
            limit: Số lượng activity tối đa để hiển thị

        Returns:
            list: Danh sách activity
        """
        try:
            # Lấy ngày hiện tại
            today_date = datetime.now().date()

            # Lấy danh sách activity của người dùng hiện tại
            activities = request.env['mail.activity'].search([
                ('user_id', '=', request.env.user.id),
            ], limit=limit, order='date_deadline asc')

            result = []
            for activity in activities:
                # Xác định trạng thái của activity
                state = 'planned'
                if activity.date_deadline == today_date:
                    state = 'today'
                elif activity.date_deadline < today_date:
                    state = 'overdue'

                # Lấy tên của bản ghi liên quan
                res_name = ''
                try:
                    record = request.env[activity.res_model].browse(activity.res_id)
                    res_name = record.display_name or record.name
                except Exception:
                    res_name = f"{activity.res_model} #{activity.res_id}"

                result.append({
                    'id': activity.id,
                    'summary': activity.summary,
                    'note': activity.note,
                    'activity_type_id': activity.activity_type_id.id,
                    'activity_type_name': activity.activity_type_id.name,
                    'date_deadline': activity.date_deadline,
                    'state': state,
                    'res_model': activity.res_model,
                    'res_id': activity.res_id,
                    'res_name': res_name,
                    'user_id': activity.user_id.id,
                    'user_name': activity.user_id.name,
                })

            return result
        except Exception as e:
            _logger.error(f"Lỗi khi lấy danh sách activity: {str(e)}")
            return []

    @http.route('/eb_lms/dashboard/student_activities', type='json', auth='user')
    def get_student_activities(self, limit=5):
        """
        Lấy danh sách hoạt động gần đây của học viên.

        Args:
            limit: Số lượng hoạt động tối đa để hiển thị

        Returns:
            list: Danh sách hoạt động học viên gần đây
        """
        activities = []

        try:
            # Lấy các điểm danh gần đây
            recent_attendances = request.env['eb.attendance.attendance'].search([
                ('role', '=', 'student')
            ], limit=limit, order='create_date desc')

            for attendance in recent_attendances:
                try:
                    # Kiểm tra xem user_id có tồn tại không
                    if not attendance.user_id:
                        continue

                    # Kiểm tra xem học viên có tồn tại không
                    student = request.env['eb.student.student'].browse(attendance.user_id.id)
                    if not student.exists():
                        continue

                    lesson = attendance.lesson_id
                    if not lesson or not lesson.subject_id:
                        continue

                    activities.append({
                        'student_id': student.id,
                        'student_name': student.name,
                        'student_image': student.image_128 or False,
                        'subject': lesson.subject_id.name,
                        'date': attendance.create_date,
                        'activity_type': 'attendance'
                    })
                except Exception as e:
                    _logger.error(f"Lỗi khi xử lý điểm danh {attendance.id}: {str(e)}")
                    continue

            # Lấy các bài làm kiểm tra gần đây
            recent_attempts = request.env['eb.quiz.attempt'].search([
            ], limit=limit, order='create_date desc')

            for attempt in recent_attempts:
                try:
                    student = attempt.student_id
                    quiz = attempt.quiz_id

                    if not student.exists() or not quiz.exists():
                        continue

                    activities.append({
                        'student_id': student.id,
                        'student_name': student.name,
                        'student_image': student.image_128 or False,
                        'subject': quiz.name,
                        'date': attempt.create_date,
                        'activity_type': 'quiz'
                    })
                except Exception as e:
                    _logger.error(f"Lỗi khi xử lý bài làm kiểm tra {attempt.id}: {str(e)}")
                    continue

            # Sắp xếp theo thời gian giảm dần
            activities.sort(key=lambda x: x['date'], reverse=True)

            # Giới hạn số lượng hoạt động
            return activities[:limit]

        except Exception as e:
            _logger.error(f"Lỗi khi lấy hoạt động học viên: {str(e)}")
            return []
