<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cron Job để gửi email chứng chỉ tự động -->
        <record id="ir_cron_send_certificate_emails" model="ir.cron">
            <field name="name">LMS: <PERSON><PERSON><PERSON> email chứng chỉ tự động</field>
            <field name="model_id" ref="model_eb_certificate_certificate"/>
            <field name="state">code</field>
            <field name="code">model._cron_send_certificate_emails()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
        </record>
    </data>
</odoo>
