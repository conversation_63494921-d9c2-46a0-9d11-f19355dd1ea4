/* Instructor Application Kanban View Improvements */

/* Override Odoo default kanban record styles */
.o_instructor_application_kanban .o_kanban_renderer {
    .o_kanban_record {
        border: none !important;
        background-color: transparent !important;
        padding: 0 !important;
        box-shadow: none !important;
        margin: 0 !important;

        &:not(.o_legacy_kanban_record):not(.o_kanban_ghost) {
            border: none !important;
            background-color: transparent !important;
            padding: 0 !important;
        }
    }

    /* Add spacing between cards */
    .o_kanban_record {
        margin: 5px 0 !important;
    }
}

.o_instructor_application_kanban .o_kanban_renderer {
    .o_instructor_application_custom_card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
        background-color: white;

        &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
            border-color: #007bff;
        }

        .oe_kanban_content {
            padding: 16px;
        }

        .o_kanban_record_title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #212529;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .text-muted {
            font-size: 0.85rem;
            color: #6c757d !important;
        }

        .o_kanban_record_body {
            margin: 12px 0;

            .mb8 {
                margin-bottom: 8px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;

                .fa {
                    width: 16px;
                    text-align: center;
                    margin-right: 8px;
                    font-size: 0.85rem;
                }
            }
        }

        .o_kanban_record_bottom {
            border-top: 1px solid #e9ecef;
            padding-top: 12px;
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .oe_kanban_bottom_left {
                .badge {
                    font-size: 0.75rem;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-weight: 500;
                }
            }

            .oe_kanban_bottom_right {
                .btn {
                    font-size: 0.8rem;
                    padding: 6px 12px;
                    border-radius: 6px;
                    font-weight: 500;
                    margin-left: 4px;
                }

                .badge {
                    font-size: 0.75rem;
                    padding: 4px 8px;
                    border-radius: 12px;
                }
            }
        }

        /* Dropdown kanban removed - no longer needed */
    }
}

/* Color improvements for icons */
.o_instructor_application_kanban .o_kanban_renderer {
    .fa-envelope {
        color: #007bff !important;
    }

    .fa-phone {
        color: #28a745 !important;
    }

    .fa-calendar {
        color: #17a2b8 !important;
    }

    .fa-briefcase {
        color: #ffc107 !important;
    }

    .fa-graduation-cap {
        color: #6c757d !important;
    }

    .fa-money {
        color: #28a745 !important;
    }
}

/* Better spacing */
.o_instructor_application_kanban .o_kanban_renderer {
    .o_kanban_record_top {
        margin-bottom: 12px;

        .o_kanban_record_headings {
            flex: 1;
            min-width: 0;
        }
    }
}

/* Priority colors */
.o_instructor_application_kanban .o_kanban_renderer {
    .o_instructor_application_custom_card {
        &[data-color="kanban-color-1"] {
            border-left: 4px solid #dc3545;
        }
        
        &[data-color="kanban-color-2"] {
            border-left: 4px solid #fd7e14;
        }
        
        &[data-color="kanban-color-3"] {
            border-left: 4px solid #ffc107;
        }
        
        &[data-color="kanban-color-4"] {
            border-left: 4px solid #20c997;
        }
        
        &[data-color="kanban-color-5"] {
            border-left: 4px solid #0dcaf0;
        }
        
        &[data-color="kanban-color-6"] {
            border-left: 4px solid #6f42c1;
        }
        
        &[data-color="kanban-color-7"] {
            border-left: 4px solid #d63384;
        }
        
        &[data-color="kanban-color-8"] {
            border-left: 4px solid #6c757d;
        }
        
        &[data-color="kanban-color-9"] {
            border-left: 4px solid #198754;
        }
        
        &[data-color="kanban-color-10"] {
            border-left: 4px solid #0d6efd;
        }
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .o_instructor_application_kanban .o_kanban_renderer {
        .o_instructor_application_custom_card {
            .oe_kanban_content {
                padding: 12px;
            }

            .o_kanban_record_title {
                font-size: 1rem;
            }

            .o_kanban_record_body .mb8 {
                font-size: 0.85rem;

                .fa {
                    width: 14px;
                    font-size: 0.8rem;
                }
            }

            .o_kanban_record_bottom {
                flex-direction: column;
                gap: 8px;
                align-items: stretch;

                .oe_kanban_bottom_right {
                    display: flex;
                    justify-content: flex-end;
                    gap: 4px;
                }
            }
        }
    }
}

/* Tags styling */
.o_instructor_application_kanban .o_kanban_renderer {
    .o_field_many2manytags {
        .o_tag {
            font-size: 0.75rem;
            padding: 2px 6px;
            margin: 1px 2px;
            border-radius: 10px;
        }
    }
}
