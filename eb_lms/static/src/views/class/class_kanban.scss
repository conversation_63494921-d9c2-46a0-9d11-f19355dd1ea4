/* Class Kanban View Improvements */

/* Override Odoo default kanban record styles for class view */
.o_eb_lms_view .o_kanban_renderer {
    .o_kanban_record {
        border: none !important;
        background-color: transparent !important;
        padding: 0 !important;
        box-shadow: none !important;
        margin: 0 !important;

        &:not(.o_legacy_kanban_record):not(.o_kanban_ghost) {
            border: none !important;
            background-color: transparent !important;
            padding: 0 !important;
        }
    }

    /* Add spacing between cards */
    .o_kanban_record {
        margin: 5px 0 !important;
    }
}

/* Enhanced styling for class kanban cards */
.o_eb_lms_view .o_kanban_renderer {
    .card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
        background-color: white;

        &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
            border-color: #007bff;
        }
    }

    .card-header {
        border-bottom: 1px solid #e9ecef !important;
        background-color: #f8f9fa !important;
        padding: 12px 16px !important;
        
        .badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
    }

    .card-body {
        padding: 16px !important;
    }

    .card-footer {
        border-top: 1px solid #e9ecef !important;
        background-color: #f8f9fa !important;
        padding: 12px 16px !important;
    }

    /* Image styling */
    .o_kanban_image {
        border-radius: 6px;
        overflow: hidden;
        
        img {
            transition: transform 0.2s ease;
        }
        
        &:hover img {
            transform: scale(1.05);
        }
    }

    /* Progress bar styling */
    .progress {
        height: 6px;
        border-radius: 3px;
        background-color: #e9ecef;
        
        .progress-bar {
            border-radius: 3px;
        }
    }

    /* Text styling */
    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #212529;
        line-height: 1.3;
        margin-bottom: 8px;
    }

    .card-subtitle {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 12px;
    }

    .text-muted {
        color: #6c757d !important;
        font-size: 0.85rem;
    }

    /* Icon colors */
    .fa-user {
        color: #007bff !important;
    }

    .fa-calendar {
        color: #28a745 !important;
    }

    .fa-map-marker {
        color: #dc3545 !important;
    }

    .fa-users {
        color: #17a2b8 !important;
    }

    .fa-graduation-cap {
        color: #6f42c1 !important;
    }

    /* Button styling */
    .btn {
        font-size: 0.8rem;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
        
        &:hover {
            transform: translateY(-1px);
        }
    }

    /* Badge improvements */
    .badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 500;
    }

    /* Learning type badges */
    .bg-primary {
        background-color: #0d6efd !important;
    }

    .bg-success {
        background-color: #198754 !important;
    }

    .bg-info {
        background-color: #0dcaf0 !important;
    }

    .bg-warning {
        background-color: #ffc107 !important;
        color: #000 !important;
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .o_eb_lms_view .o_kanban_renderer {
        .card {
            margin: 8px 4px;
        }

        .card-header,
        .card-body,
        .card-footer {
            padding: 8px 12px !important;
        }

        .card-title {
            font-size: 1rem;
        }

        .card-subtitle {
            font-size: 0.85rem;
        }

        .btn {
            font-size: 0.75rem;
            padding: 4px 8px;
        }

        .badge {
            font-size: 0.7rem;
            padding: 2px 6px;
        }
    }
}

/* Better spacing for grouped kanban */
.o_kanban_grouped.o_eb_lms_view {
    .o_kanban_group {
        padding: 0 8px;
        
        .o_kanban_record {
            margin: 8px 0;
        }
    }
}

/* Hover effects for interactive elements */
.o_eb_lms_view .o_kanban_renderer {
    .oe_kanban_global_click {
        cursor: pointer;
        
        &:hover {
            .card {
                border-color: #007bff;
            }
        }
    }

    /* Smooth transitions for all interactive elements */
    .card,
    .btn,
    .badge,
    .progress-bar {
        transition: all 0.2s ease;
    }
}

/* Fix for kanban scroll issues */
.o_kanban_grouped.o_eb_lms_view {
    overflow-x: auto !important;
    overflow-y: auto !important;

    .o_kanban_group {
        height: auto !important;
        min-height: 100% !important;
        max-height: none !important;
        overflow-y: auto !important;
    }
}

.o_kanban_ungrouped.o_eb_lms_view {
    overflow-y: auto !important;
    height: auto !important;
    min-height: 100% !important;
}

/* Ensure records are properly displayed */
.o_eb_lms_view {
    .o_kanban_renderer {
        overflow-y: auto !important;
    }

    .o_kanban_record {
        overflow: visible !important;
    }
}
