# EarnBase API Files

## Overview
This module provides REST API endpoints for file attachment operations, leveraging Odoo's `ir.attachment` model. It's designed to be used as part of the EarnBase API ecosystem.

## Features
- **Get File**: Retrieve file metadata or download/preview files by access token
- **Upload File**: Upload files to the system and get attachment details

## API Endpoints

### GET /files/{access_token}
Get file by access token.

**Query Parameters**:
- `is_download` (boolean): Set to true to download the file
- `is_preview` (boolean): Set to true to preview the file (applies to images and PDFs)

**Responses**:
- `200`: File information or the file itself
- `404`: File not found
- `500`: Server error

### POST /files/
Upload a file.

**Request Body**:
- File as multipart/form-data

**Responses**:
- `200`: File uploaded successfully with attachment details
- `400`: Bad request
- `500`: Server error

## Dependencies
- `base`
- `fastapi`
- `eb_api_core`

## Author
EarnBase Technology <https://earnbase.io>

## License
LGPL-3.0 or later 