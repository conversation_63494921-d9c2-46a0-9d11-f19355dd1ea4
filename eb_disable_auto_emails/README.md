# EB Disable Auto Emails

Mo<PERSON>le này vô hiệu hóa các email tự động được gửi bởi Odoo trong các trường hợp sau:

1. <PERSON><PERSON> hóa đơn (invoice) đư<PERSON><PERSON> xác nhận
2. <PERSON><PERSON> đơn hàng (sale order) được xác nhận
3. <PERSON>hi người dùng mới được tạo

## Cách hoạt động

Module này sử dụng hai cách tiếp cận để vô hiệu hóa email tự động:

### 1. Ghi đè các phương thức gửi email

- <PERSON><PERSON> đè phương thức `_get_mail_template` của model `account.move` để trả về `False` thay vì template mặc định
- Ghi đè phương thức `_action_invoice_ready_to_be_sent` của model `account.move` để không làm gì cả
- <PERSON>hi đè phương thức `_get_default_sending_settings` của model `account.move.send` để loại bỏ email khỏi sending_methods
- <PERSON>hi đè phương thức `_find_mail_template` của model `sale.order` để trả về `False` thay vì template mặc định
- Ghi đè phương thức `_send_order_notification_mail` của model `sale.order` để không gửi email

### 2. Cấu hình tham số hệ thống

- Đặt `sale.automatic_email` thành `False` để tắt email tự động khi xác nhận đơn hàng
- Đặt `account.automatic_email` thành `False` để tắt email tự động khi tạo hóa đơn
- Đặt `auth_signup.send_welcome_email` thành `False` để tắt email tự động khi tạo user

## Cài đặt

Module này được cài đặt tự động (auto_install=True) khi các module phụ thuộc (account, sale, auth_signup) được cài đặt.

## Ghi chú

Nếu bạn muốn gửi email thủ công, bạn vẫn có thể sử dụng các chức năng gửi email thủ công trong Odoo.
