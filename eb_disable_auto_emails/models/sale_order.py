import logging
from odoo import models, api

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _find_mail_template(self):
        """
        <PERSON>hi đè phương thức này để trả về False thay vì template mặc định
        """
        _logger.info("Disabled automatic email template selection for order %s", self.ids)
        return False

    def _send_order_notification_mail(self, mail_template):
        """
        Ghi đè phương thức này để không gửi email
        """
        _logger.info("Disabled _send_order_notification_mail for order %s", self.ids)
        return
