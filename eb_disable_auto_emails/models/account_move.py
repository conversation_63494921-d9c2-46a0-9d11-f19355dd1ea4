import logging
from odoo import models, api

_logger = logging.getLogger(__name__)

class AccountMove(models.Model):
    _inherit = 'account.move'

    def _get_mail_template(self):
        """
        <PERSON>hi đè phương thức này để trả về False thay vì template mặc định
        """
        _logger.info("Disabled automatic email template selection for invoice %s", self.ids)
        return False

    def _action_invoice_ready_to_be_sent(self):
        """
        <PERSON>hi đè phương thức hook này để không làm gì cả
        """
        _logger.info("Disabled _action_invoice_ready_to_be_sent for invoice %s", self.ids)
        return
