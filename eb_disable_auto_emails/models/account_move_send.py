import logging
from odoo import models, api

_logger = logging.getLogger(__name__)


class AccountMoveSend(models.AbstractModel):
    _inherit = 'account.move.send'

    @api.model
    def _get_default_sending_settings(self, move, from_cron=False, **custom_settings):
        """
        Ghi đè phương thức này để loại bỏ email khỏi sending_methods
        """
        result = super()._get_default_sending_settings(move, from_cron=from_cron, **custom_settings)

        # Loại bỏ email khỏi sending_methods
        if 'email' in result.get('sending_methods', []):
            result['sending_methods'].remove('email')
            _logger.info("Removed email from sending methods for invoice %s", move.id)

        return result

    @api.model
    def _get_default_mail_lang(self, move, mail_template):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, tr<PERSON> về ngôn ngữ của đối tác hoặc ngôn ngữ mặc định
        thay vì gọi _render_lang trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False for invoice %s, returning partner language or default", move.id)
            return move.partner_id.lang or self.env.user.lang or 'en_US'

        return super()._get_default_mail_lang(move, mail_template)

    @api.model
    def _get_placeholder_mail_template_dynamic_attachments_data(self, move, mail_template, pdf_report=None):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, trả về danh sách rỗng thay vì
        cố gắng truy cập thuộc tính report_template_ids trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False for invoice %s, returning empty list for dynamic attachments", move.id)
            return []

        return super()._get_placeholder_mail_template_dynamic_attachments_data(move, mail_template, pdf_report=pdf_report)

    @api.model
    def _get_default_mail_partner_ids(self, move, mail_template, mail_lang):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, trả về recordset rỗng thay vì
        cố gắng truy cập thuộc tính email_to trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False for invoice %s, returning empty partner recordset", move.id)
            return self.env['res.partner']

        return super()._get_default_mail_partner_ids(move, mail_template, mail_lang)

    @api.model
    def _get_default_mail_body(self, move, mail_template, mail_lang):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, trả về chuỗi rỗng thay vì
        cố gắng render trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False for invoice %s, returning empty body", move.id)
            return ""

        return super()._get_default_mail_body(move, mail_template, mail_lang)

    @api.model
    def _get_default_mail_subject(self, move, mail_template, mail_lang):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, trả về chuỗi mặc định thay vì
        cố gắng render trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False for invoice %s, returning default subject", move.id)
            return f"Invoice {move.name}"

        return super()._get_default_mail_subject(move, mail_template, mail_lang)

    @api.model
    def _get_mail_template_attachments_data(self, mail_template):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, trả về danh sách rỗng thay vì
        cố gắng truy cập thuộc tính attachment_ids trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False, returning empty attachments data")
            return []

        return super()._get_mail_template_attachments_data(mail_template)

    @api.model
    def _get_mail_default_field_value_from_template(self, mail_template, lang, move, field, **kwargs):
        """
        Ghi đè phương thức này để xử lý trường hợp mail_template là False

        Trong trường hợp mail_template là False, trả về None thay vì
        cố gắng render trên đối tượng False.
        """
        if not mail_template:
            _logger.info("Mail template is False, returning None for field %s", field)
            return None

        return super()._get_mail_default_field_value_from_template(mail_template, lang, move, field, **kwargs)
