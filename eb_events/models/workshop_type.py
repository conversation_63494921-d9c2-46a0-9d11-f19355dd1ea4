# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models
from random import randint


class WorkshopType(models.Model):
    """
    Model để lưu trữ các loại workshop.
    """
    _name = "eb.workshop.type"
    _description = "Workshop Type"
    _order = "sequence, name"

    def _default_color(self):
        return randint(1, 11)

    name = fields.Char(
        string="Name",
        required=True,
        translate=True,
        help="Name of the workshop type"
    )
    code = fields.Char(
        string="Code",
        required=True,
        help="Technical code of the workshop type"
    )
    sequence = fields.Integer(
        string="Sequence",
        default=10,
        help="Sequence order for displaying"
    )
    active = fields.Bo<PERSON>an(
        string="Active",
        default=True,
        help="If unchecked, it will not be visible in selection"
    )
    description = fields.Text(
        string="Description",
        translate=True,
        help="Description of the workshop type"
    )
    color = fields.Integer(
        string="Color Index",
        default=_default_color,
        help="Color for the workshop type"
    )
    
    _sql_constraints = [
        ('code_uniq', 'unique (code)', "Workshop type code must be unique!"),
    ]
