# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models


class EventEvent(models.Model):
    """
    Mở rộng model event.event để bổ sung các trường và phương thức cần thiết cho API.
    """
    _inherit = "event.event"

    # Các trường bổ sung cho API
    is_workshop = fields.Boolean(
        string="Is Workshop",
        default=False,
        help="Check if this event is a workshop",
    )

    workshop_type_id = fields.Many2one(
        "eb.workshop.type",
        string="Workshop Type",
        help="Type of workshop",
    )

    duration_hours = fields.Float(
        string="Duration (hours)",
        compute="_compute_duration_hours",
        store=True,
        help="Duration of the event in hours",
    )

    presenter_ids = fields.Many2many(
        "res.partner",
        "event_presenter_rel",
        "event_id",
        "partner_id",
        string="Presenters",
        help="Presenters of the workshop",
    )

    @api.depends("date_begin", "date_end")
    def _compute_duration_hours(self):
        """Tính toán thời lượng sự kiện theo giờ."""
        for event in self:
            if event.date_begin and event.date_end:
                duration = event.date_end - event.date_begin
                event.duration_hours = duration.total_seconds() / 3600
            else:
                event.duration_hours = 0.0

    def get_event_data(self):
        """
        Lấy dữ liệu sự kiện cho API.

        Returns:
            dict: Dữ liệu sự kiện
        """
        self.ensure_one()
        return {
            "id": self.id,
            "name": self.name,
            "is_workshop": self.is_workshop,
            "workshop_type": self.workshop_type_id.name if self.workshop_type_id else None,
            "date_begin": self.date_begin,
            "date_end": self.date_end,
            "duration_hours": self.duration_hours,
            "organizer": self.organizer_id.name if self.organizer_id else None,
            "address": self.address_id.display_name if self.address_id else None,
            "seats_available": self.seats_available,
            "seats_max": self.seats_max,
            "description": self.description,
            "website_published": self.website_published if hasattr(self, "website_published") else False,
            "presenters": [
                {"id": presenter.id, "name": presenter.name}
                for presenter in self.presenter_ids
            ] if self.presenter_ids else [],
        }
