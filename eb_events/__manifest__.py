# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

{
    "name": "Events API",
    "summary": "API for events and workshops",
    "description": """
        API for events and workshops management.
        Features include:
        - API to get list of events/workshops
        - API to get event details
        - Public API for events listing
        - Integration with Odoo events module
    """,
    "author": "eBill and Vantis Vietnam",
    "website": "https://earnbase.io",
    "category": "Events",
    "version": "********.0",
    "depends": [
        "base",
        "event",
        "eb_api_core",
        "eb_api_auth",
    ],
    "data": [
        "security/ir.model.access.csv",
        "data/workshop_type_data.xml",
        "views/workshop_type_views.xml",
        "views/event_views.xml",
    ],
    "demo": [],
    "installable": True,
    "application": False,
    "auto_install": False,
    "license": "LGPL-3",
}
