# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

from fastapi import APIRouter
from odoo.addons.eb_api_core.utils.decorators import register_api_module

_logger = logging.getLogger(__name__)


@register_api_module(
    module_name="events",
    description="Events API for workshops and events management",
    version="1.0",
    app_name="events_api",
)
class EventsAPI:
    """API module for Events endpoints"""

    # Router instance - không gắn tag ở đây để có thể sử dụng nhiều tag khác nhau
    router = APIRouter()

    # Import routers để đăng ký endpoints
    # Router cho các sự kiện (cần xác thực)
    # from odoo.addons.eb_events.routers.event_router import event_router
    # from odoo.addons.eb_events.routers.workshop_type_router import workshop_type_router

    # Router cho API công khai
    from odoo.addons.eb_events.public_api.routers.public_router import public_router

    # Đăng ký sub-routers với các tag khác nhau
    # API cho sự kiện (private)
    # router.include_router(event_router, tags=["events"])
    # router.include_router(workshop_type_router, tags=["events"])

    # API công khai cho sự kiện (public)
    router.include_router(public_router, tags=["public"])
