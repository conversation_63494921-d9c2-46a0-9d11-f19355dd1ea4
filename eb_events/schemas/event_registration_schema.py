# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import List, Optional
from pydantic import BaseModel, Field, EmailStr

from odoo.addons.eb_api_core.schemas.base import ResponseBase


class PublicEventRegistrationCreate(BaseModel):
    """Schema cho việc tạo đăng ký sự kiện công khai"""

    name: str = Field(..., description="Tên người tham dự")
    email: EmailStr = Field(..., description="Email người tham dự")
    phone: Optional[str] = Field(None, description="Số điện thoại người tham dự")
    company_name: Optional[str] = Field(None, description="Tên công ty")
    ticket_id: Optional[int] = Field(None, description="ID của vé sự kiện (nếu có)")
    notes: Optional[str] = Field(None, description="<PERSON><PERSON> chú bổ sung")


class PublicEventRegistrationData(BaseModel):
    """Schema cho dữ liệu đăng ký sự kiện trả về"""

    id: int = Field(..., description="ID của đăng ký sự kiện")
    name: str = Field(..., description="Tên người tham dự")
    email: str = Field(..., description="Email người tham dự")
    phone: Optional[str] = Field(None, description="Số điện thoại người tham dự")
    company_name: Optional[str] = Field(None, description="Tên công ty")
    event_name: str = Field(..., description="Tên sự kiện")
    event_date_begin: str = Field(..., description="Thời gian bắt đầu sự kiện")
    event_date_end: str = Field(..., description="Thời gian kết thúc sự kiện")
    ticket_name: Optional[str] = Field(None, description="Tên vé")
    state: str = Field(..., description="Trạng thái đăng ký")
    barcode: str = Field(..., description="Mã vạch đăng ký")


class PublicEventRegistrationResponse(ResponseBase[PublicEventRegistrationData]):
    """Response cho API đăng ký sự kiện"""
    pass


class PublicEventRegistrationListItem(BaseModel):
    """Schema cho item trong danh sách đăng ký sự kiện"""

    id: int = Field(..., description="ID của đăng ký sự kiện")
    name: str = Field(..., description="Tên người tham dự")
    email: str = Field(..., description="Email người tham dự")
    state: str = Field(..., description="Trạng thái đăng ký")
    barcode: str = Field(..., description="Mã vạch đăng ký")


class PublicEventRegistrationListResponse(ResponseBase[List[PublicEventRegistrationListItem]]):
    """Response cho API lấy danh sách đăng ký sự kiện của người dùng"""
    pass
