# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoints cho quản lý sự kiện

Cung cấp các API endpoints cho quản lý sự kiện:
- GET /api/events: <PERSON><PERSON><PERSON><PERSON> s<PERSON>ch sự kiện
- GET /api/events/{id}: <PERSON><PERSON><PERSON> chi tiết sự kiện
"""

from typing import Annotated

from fastapi import APIRouter, Depends, Path

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.dependencies.auth import get_current_user
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.decorators import auto_error_response

# Import từ schemas
from odoo.addons.eb_events.schemas.event_schema import (
    EventListResponse,
    EventDetailResponse,
)

_logger = get_request_logger(__name__)

# Router cho API events
event_router = APIRouter(prefix="/events")


@event_router.get("", response_model=EventListResponse)
@auto_error_response([401, 500])
@trace()
async def get_events(
    env: Annotated[Environment, Depends(odoo_env)],
    current_user: Annotated[object, Depends(get_current_user)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy toàn bộ danh sách sự kiện"""
    _logger.info(
        f"Getting all events, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(
            user_id=current_user.id
        )

        # Xây dựng domain - không có điều kiện lọc
        domain = []

        # Đếm tổng số bản ghi
        total = env["event.event"].search_count(domain)

        # Lấy toàn bộ danh sách sự kiện
        events = env["event.event"].search(
            domain, order="date_begin, name"
        )

        # Chuyển đổi sang định dạng response
        items = []
        for event in events:
            event_data = event.get_event_data()
            # Chỉ lấy các trường cần thiết cho danh sách
            items.append({
                "id": event_data["id"],
                "name": event_data["name"],
                "is_workshop": event_data["is_workshop"],
                "workshop_type": event_data["workshop_type"],
                "date_begin": event_data["date_begin"],
                "date_end": event_data["date_end"],
                "duration_hours": event_data["duration_hours"],
                "organizer": event_data["organizer"],
                "address": event_data["address"],
                "seats_available": event_data["seats_available"],
                "description": event_data["description"],
            })

        # Trả về response
        return EventListResponse(
            success=True,
            message=f"Tìm thấy {total} sự kiện",
            data=items,
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error getting events: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy danh sách sự kiện: {str(e)}",
        )


@event_router.get("/{event_id}", response_model=EventDetailResponse)
@auto_error_response([401, 404, 500])
@trace()
async def get_event_detail(
    event_id: Annotated[int, Path(..., description="ID sự kiện")],
    env: Annotated[Environment, Depends(odoo_env)],
    current_user: Annotated[object, Depends(get_current_user)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy chi tiết sự kiện theo ID"""
    _logger.info(f"Getting event id={event_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(user_id=current_user.id, event_id=event_id)

        # Tìm sự kiện
        event = env["event.event"].browse(event_id)
        if not event.exists():
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Không tìm thấy sự kiện với ID {event_id}",
            )

        # Lấy dữ liệu sự kiện
        event_data = event.get_event_data()

        # Trả về response
        return EventDetailResponse(
            success=True,
            message="Lấy thông tin sự kiện thành công",
            data=event_data,
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error getting event detail: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy chi tiết sự kiện: {str(e)}",
        )
