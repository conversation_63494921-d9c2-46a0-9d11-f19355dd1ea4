# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoints cho quản lý loại workshop

Cung cấp các API endpoints cho quản lý loại workshop:
- GET /api/workshop-types: Lấy <PERSON>nh s<PERSON>ch loại workshop
"""

from typing import Annotated, List

from fastapi import APIRouter, Depends

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_auth.dependencies import get_current_user
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils.request import get_meta_data

# Import từ schemas
from odoo.addons.eb_events.schemas.workshop_type_schema import (
    WorkshopTypeListResponse,
    WorkshopType,
)

_logger = get_request_logger(__name__)

# Router cho API workshop types
workshop_type_router = APIRouter(prefix="/workshop-types")


@workshop_type_router.get("", response_model=WorkshopTypeListResponse)
@auto_error_response([401, 500])
@trace()
async def get_workshop_types(
    env: Annotated[Environment, Depends(odoo_env)],
    current_user: Annotated[object, Depends(get_current_user)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy danh sách loại workshop"""
    _logger.info(
        f"Getting all workshop types, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(
            user_id=current_user.id
        )

        # Xây dựng domain - chỉ lấy các loại workshop đang active
        domain = [('active', '=', True)]

        try:
            # Đếm tổng số bản ghi
            total = env["eb.workshop.type"].sudo().search_count(domain)

            # Lấy toàn bộ danh sách loại workshop
            workshop_types = env["eb.workshop.type"].sudo().search(
                domain, order="sequence, name"
            )
        except Exception as e:
            _logger.error(f"Error accessing workshop types: {str(e)}, requestId={request_id}")
            raise api_exception(
                ErrorCode.PERMISSION_DENIED,
                f"Không có quyền truy cập vào danh sách loại workshop: {str(e)}",
            )

        # Chuyển đổi sang định dạng response
        items = []
        for wtype in workshop_types:
            items.append({
                "id": wtype.id,
                "name": wtype.name,
                "code": wtype.code,
                "description": wtype.description,
                "color": wtype.color,
            })

        # Trả về response
        return WorkshopTypeListResponse(
            success=True,
            message=f"Tìm thấy {total} loại workshop",
            data=items,
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error getting workshop types: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy danh sách loại workshop: {str(e)}",
        )
