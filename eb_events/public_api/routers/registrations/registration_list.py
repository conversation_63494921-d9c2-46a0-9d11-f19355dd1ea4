# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoint công khai để lấy danh sách đăng ký sự kiện

Cung cấp API endpoint công khai để lấy danh sách đăng ký sự kiện:
- GET /public/registrations: L<PERSON>y danh sách đăng ký sự kiện theo email
"""

from typing import Annotated
import traceback

from fastapi import APIRouter, Depends, Query
from pydantic import EmailStr

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.logging_setup import get_request_logger

from odoo.addons.eb_events.schemas.event_registration_schema import (
    PublicEventRegistrationListResponse,
)

_logger = get_request_logger(__name__)

# Router cho API lấy danh sách đăng ký sự kiện
registration_list_router = APIRouter()


@registration_list_router.get(
    "/registrations",
    response_model=PublicEventRegistrationListResponse,
    summary="Lấy danh sách đăng ký sự kiện theo email",
    description="API lấy danh sách đăng ký sự kiện của người dùng dựa trên email.",
)
@auto_error_response([400, 500])
@trace()
async def get_registrations_by_email(
    email: Annotated[EmailStr, Query(..., description="Email người dùng")],
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy danh sách đăng ký sự kiện của người dùng dựa trên email

    Args:
        email: Email người dùng
        env: Odoo environment từ request
        request_id: ID request hiện tại

    Returns:
        Danh sách đăng ký sự kiện của người dùng

    Raises:
        HTTPException: Khi gặp lỗi trong quá trình xử lý
    """
    _logger.info(
        f"Getting registrations for email {email}, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(email=email)

        # Tìm các đăng ký sự kiện theo email
        registrations = env["event.registration"].sudo().search([
            ("email", "=", email),
            ("state", "!=", "cancel"),  # Không lấy các đăng ký đã hủy
        ])

        # Chuẩn bị dữ liệu trả về
        registration_list = []
        for registration in registrations:
            registration_list.append({
                "id": registration.id,
                "name": registration.name,
                "email": registration.email,
                "state": registration.state,
                "barcode": registration.barcode,
            })

        # Trả về response
        return PublicEventRegistrationListResponse(
            success=True,
            message="Lấy danh sách đăng ký sự kiện thành công",
            data=registration_list,
            meta=get_meta_data(),
        )

    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(
            f"Error getting registrations: {str(e)}, traceback: {traceback.format_exc()}, requestId={request_id}"
        )
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy danh sách đăng ký sự kiện: {str(e)}",
        )
