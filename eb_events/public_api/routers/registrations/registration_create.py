# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoint công khai cho đăng ký sự kiện

Cung cấp API endpoint công khai để đăng ký sự kiện:
- POST /public/events/{event_id}/registrations: Tạo đăng ký sự kiện mới
"""

from typing import Annotated
import traceback

from fastapi import APIRouter, Depends, Path

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.logging_setup import get_request_logger

from odoo.addons.eb_events.schemas.event_registration_schema import (
    PublicEventRegistrationCreate,
    PublicEventRegistrationResponse,
)

_logger = get_request_logger(__name__)

# Router cho API đăng ký sự kiện
registration_create_router = APIRouter()


@registration_create_router.post(
    "/events/{event_id}/registrations",
    response_model=PublicEventRegistrationResponse,
    summary="Tạo đăng ký sự kiện mới",
    description="API tạo đăng ký sự kiện mới từ thông tin người dùng website cho một sự kiện cụ thể.",
)
@auto_error_response([400, 404, 500])
@trace()
async def create_registration(
    event_id: Annotated[int, Path(..., description="ID sự kiện")],
    registration_data: PublicEventRegistrationCreate,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Tạo đăng ký sự kiện mới từ thông tin người dùng website

    Args:
        event_id: ID của sự kiện từ URL path
        registration_data: Thông tin đăng ký sự kiện (bao gồm: name, email, phone, company_name, ticket_id, notes)
        env: Odoo environment từ request
        request_id: ID request hiện tại

    Returns:
        Thông tin về đăng ký sự kiện đã tạo

    Raises:
        HTTPException: Khi gặp lỗi trong quá trình xử lý
    """
    _logger.info(
        f"Creating new registration for event ID {event_id}, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(event_id=event_id)

        # Tìm sự kiện
        event = env["event.event"].browse(event_id)
        if not event.exists():
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Không tìm thấy sự kiện với ID {event_id}",
            )

        # Kiểm tra sự kiện đã xuất bản chưa (nếu có trường website_published)
        if hasattr(event, "website_published") and not event.website_published:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Sự kiện với ID {event_id} chưa được xuất bản",
            )

        # Kiểm tra sự kiện có đang mở đăng ký không
        if not event.event_registrations_open:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                f"Sự kiện với ID {event_id} hiện không mở đăng ký",
            )

        # Kiểm tra số lượng chỗ còn trống
        if event.seats_limited and event.seats_available <= 0:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                f"Sự kiện với ID {event_id} đã hết chỗ",
            )

        # Xử lý ticket_id nếu có
        ticket = None
        if registration_data.ticket_id:
            ticket = env["event.event.ticket"].browse(registration_data.ticket_id)
            if not ticket.exists() or ticket.event_id.id != event.id:
                raise api_exception(
                    ErrorCode.BAD_REQUEST,
                    f"Không tìm thấy vé với ID {registration_data.ticket_id} cho sự kiện này",
                )

            # Kiểm tra vé còn hiệu lực không
            if ticket.is_expired:
                raise api_exception(
                    ErrorCode.BAD_REQUEST,
                    f"Vé với ID {registration_data.ticket_id} đã hết hạn",
                )

            # Kiểm tra số lượng vé còn trống
            if ticket.seats_limited and ticket.seats_available <= 0:
                raise api_exception(
                    ErrorCode.BAD_REQUEST,
                    f"Vé với ID {registration_data.ticket_id} đã hết chỗ",
                )

        # Tạo đăng ký sự kiện mới
        registration_vals = {
            "event_id": event.id,
            "name": registration_data.name,
            "email": registration_data.email,
            "phone": registration_data.phone,
            "company_name": registration_data.company_name,
            "event_ticket_id": registration_data.ticket_id,
            "state": "open",  # Trạng thái mặc định là đã đăng ký
        }

        # Tạo bản ghi đăng ký
        registration = env["event.registration"].sudo().create(registration_vals)
        _logger.info(f"Created registration ID {registration.id}, requestId={request_id}")

        # Lấy thông tin đăng ký để trả về
        registration_data = {
            "id": registration.id,
            "name": registration.name,
            "email": registration.email,
            "phone": registration.phone,
            "company_name": registration.company_name,
            "event_name": event.name,
            "event_date_begin": event.date_begin,
            "event_date_end": event.date_end,
            "ticket_name": ticket.name if ticket else None,
            "state": registration.state,
            "barcode": registration.barcode,
        }

        # Trả về response
        return PublicEventRegistrationResponse(
            success=True,
            message="Đăng ký sự kiện thành công",
            data=registration_data,
            meta=get_meta_data(),
        )

    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(
            f"Error creating registration: {str(e)}, traceback: {traceback.format_exc()}, requestId={request_id}"
        )
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tạo đăng ký sự kiện: {str(e)}",
        )
