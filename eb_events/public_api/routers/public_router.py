# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoints công khai cho sự kiện và loại workshop

Cung cấp các API endpoints công khai để hiển thị thông tin:
- GET /public/events: <PERSON><PERSON><PERSON> <PERSON><PERSON> s<PERSON>ch sự kiện công khai
- GET /public/events/{id}: L<PERSON><PERSON> chi tiết sự kiện công khai
- GET /public/workshop-types: Lấy danh sách loại workshop
- POST /public/events/{id}/registrations: Tạo đăng ký sự kiện mới
- GET /public/registrations: L<PERSON><PERSON> da<PERSON> sách đăng ký sự kiện theo email
"""

from fastapi import APIRouter

from odoo.addons.eb_api_core.utils.logging import get_request_logger

# Import các router con
from odoo.addons.eb_events.public_api.routers.events.event_list import event_list_router
from odoo.addons.eb_events.public_api.routers.events.event_detail import event_detail_router
from odoo.addons.eb_events.public_api.routers.workshop_types.workshop_type_list import workshop_type_list_router
from odoo.addons.eb_events.public_api.routers.registrations.registration_create import registration_create_router
from odoo.addons.eb_events.public_api.routers.registrations.registration_list import registration_list_router

_logger = get_request_logger(__name__)

# Router chính cho API công khai
public_router = APIRouter(prefix="/public")

# Đăng ký các router con
public_router.include_router(event_list_router)
public_router.include_router(event_detail_router)
public_router.include_router(workshop_type_list_router)
public_router.include_router(registration_create_router)
public_router.include_router(registration_list_router)

_logger.info("Public Events API routers registered successfully")
