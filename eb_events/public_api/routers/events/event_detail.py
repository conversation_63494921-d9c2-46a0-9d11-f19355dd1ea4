# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoint công khai cho chi tiết sự kiện

Cung cấp API endpoint công khai để lấy chi tiết sự kiện:
- GET /public/events/{event_id}: L<PERSON><PERSON> chi tiết sự kiện công khai
"""

from typing import Annotated, Optional

from fastapi import APIRouter, Depends, Path, Query

from odoo.api import Environment
from odoo import fields
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.decorators import auto_error_response

# Import từ schemas
from odoo.addons.eb_events.public_api.schemas.public_schema import (
    PublicEventDetailResponse,
)

_logger = get_request_logger(__name__)

# Router cho API công khai
event_detail_router = APIRouter()


@event_detail_router.get(
    "/events/{event_id}", response_model=PublicEventDetailResponse
)
@auto_error_response([404, 500])
@trace()
async def get_public_event_detail(
    event_id: Annotated[int, Path(..., description="ID sự kiện")],
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy chi tiết sự kiện công khai theo ID"""
    _logger.info(f"Getting public event id={event_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(event_id=event_id)

        # Tìm sự kiện
        event = env["event.event"].browse(event_id)
        if not event.exists():
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Không tìm thấy sự kiện với ID {event_id}",
            )

        # Kiểm tra sự kiện đã xuất bản chưa (nếu có trường website_published)
        if hasattr(event, "website_published") and not event.website_published:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Sự kiện với ID {event_id} chưa được xuất bản",
            )

        # Kiểm tra sự kiện đã kết thúc chưa
        if event.date_end and event.date_end <= fields.Datetime.now():
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Sự kiện với ID {event_id} đã kết thúc",
            )

        # Lấy dữ liệu sự kiện
        event_data = event.get_event_data()

        # Trả về response
        return PublicEventDetailResponse(
            success=True,
            message="Lấy thông tin sự kiện thành công",
            data=event_data,
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error getting public event detail: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy chi tiết sự kiện: {str(e)}",
        )
