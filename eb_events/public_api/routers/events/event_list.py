# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoint công khai cho danh sách sự kiện

Cung cấp API endpoint công khai để lấy danh sách sự kiện:
- GET /public/events: <PERSON><PERSON><PERSON> da<PERSON> sách sự kiện công khai
"""

from typing import Annotated

from fastapi import APIRouter, Depends

from odoo.api import Environment
from odoo import fields
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils.request import get_meta_data

# Import từ schemas
from odoo.addons.eb_events.public_api.schemas.public_schema import (
    PublicEventListResponse,
)

_logger = get_request_logger(__name__)

# Router cho API công khai
event_list_router = APIRouter()


@event_list_router.get("/events", response_model=PublicEventListResponse)
@auto_error_response([500])
@trace()
async def get_public_events(
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy toàn bộ danh sách sự kiện công khai"""
    _logger.info(
        f"Getting all public events, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes()

        # Xây dựng domain
        domain = []

        # Chỉ lấy các sự kiện đã xuất bản (nếu có trường website_published)
        if hasattr(env["event.event"], "website_published"):
            domain.append(("website_published", "=", True))

        # Chỉ lấy các sự kiện chưa kết thúc (date_end > now)
        domain.append(("date_end", ">", fields.Datetime.now()))

        # Đếm tổng số bản ghi
        total = env["event.event"].search_count(domain)

        # Lấy toàn bộ danh sách sự kiện
        events = env["event.event"].search(
            domain, order="date_begin asc"
        )

        # Chuyển đổi sang định dạng response
        items = []
        for event in events:
            event_data = event.get_event_data()
            # Chỉ lấy các trường cần thiết cho danh sách công khai
            items.append({
                "id": event_data["id"],
                "name": event_data["name"],
                "is_workshop": event_data["is_workshop"],
                "workshop_type": event_data["workshop_type"],
                "date_begin": event_data["date_begin"],
                "date_end": event_data["date_end"],
                "duration_hours": event_data["duration_hours"],
                "organizer": event_data["organizer"],
                "address": event_data["address"],
                "seats_available": event_data["seats_available"],
                "description": event_data["description"],
            })

        # Trả về response
        return PublicEventListResponse(
            success=True,
            message=f"Tìm thấy {total} sự kiện",
            data=items,
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error getting public events: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy danh sách sự kiện: {str(e)}",
        )
