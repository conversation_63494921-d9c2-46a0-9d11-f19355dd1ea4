# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Endpoint công khai cho danh sách loại workshop

Cung cấp API endpoint công khai để lấy danh sách loại workshop:
- GET /public/workshop-types: L<PERSON>y danh s<PERSON>ch loại workshop công khai
"""

from typing import Annotated

from fastapi import APIRouter, Depends

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.trace_utils import trace, add_span_attributes
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils.request import get_meta_data

# Import từ schemas
from odoo.addons.eb_events.public_api.schemas.workshop_type_schema import (
    PublicWorkshopTypeListResponse,
)

_logger = get_request_logger(__name__)

# Router cho API công khai
workshop_type_list_router = APIRouter()


@workshop_type_list_router.get("/workshop-types", response_model=PublicWorkshopTypeListResponse)
@auto_error_response([500])
@trace()
async def get_public_workshop_types(
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy toàn bộ danh sách loại workshop công khai"""
    _logger.info(
        f"Getting all public workshop types, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes()

        # Xây dựng domain - chỉ lấy các loại workshop đang active
        domain = [('active', '=', True)]

        try:
            # Đếm tổng số bản ghi
            total = env["eb.workshop.type"].sudo().search_count(domain)

            # Lấy toàn bộ danh sách loại workshop
            workshop_types = env["eb.workshop.type"].sudo().search(
                domain, order="sequence, name"
            )
        except Exception as e:
            _logger.error(f"Error accessing workshop types: {str(e)}, requestId={request_id}")
            raise api_exception(
                ErrorCode.PERMISSION_DENIED,
                f"Không có quyền truy cập vào danh sách loại workshop: {str(e)}",
            )

        # Chuyển đổi sang định dạng response
        items = []
        for wtype in workshop_types:
            items.append({
                "id": wtype.id,
                "name": wtype.name,
                "code": wtype.code,
                "description": wtype.description,
                "color": wtype.color,
            })

        # Trả về response
        return PublicWorkshopTypeListResponse(
            success=True,
            message=f"Tìm thấy {total} loại workshop",
            data=items,
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error getting public workshop types: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể lấy danh sách loại workshop: {str(e)}",
        )
