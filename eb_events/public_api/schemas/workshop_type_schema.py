# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import List, Optional, Any
from pydantic import BaseModel, Field

from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_api_core.utils.request import get_meta_data


class PublicWorkshopType(BaseModel):
    """Schema công khai cho loại workshop"""

    id: int = Field(..., description="ID loại workshop")
    name: str = Field(..., description="Tên loại workshop")
    code: str = Field(..., description="Mã loại workshop")
    description: Optional[str] = Field(None, description="Mô tả loại workshop")
    color: Optional[int] = Field(None, description="Màu sắc")


# Response models
class PublicWorkshopTypeListResponse(ResponseBase[List[PublicWorkshopType]]):
    """Response công khai cho danh sách loại workshop"""
    pass
