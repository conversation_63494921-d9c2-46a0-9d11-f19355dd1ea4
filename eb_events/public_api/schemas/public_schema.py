# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field

from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_api_core.utils.request import get_meta_data


class PublicPresenter(BaseModel):
    """Schema công khai cho người trình bày"""

    id: int = Field(..., description="ID người trình bày")
    name: str = Field(..., description="Tên người trình bày")


class PublicWorkshopType(BaseModel):
    """Schema công khai cho loại workshop"""

    id: int = Field(..., description="ID loại workshop")
    name: str = Field(..., description="Tên loại workshop")
    code: str = Field(..., description="Mã loại workshop")


class PublicEventBase(BaseModel):
    """Schema cơ bản công khai cho sự kiện"""

    id: int = Field(..., description="ID sự kiện")
    name: str = Field(..., description="Tên sự kiện")
    is_workshop: bool = Field(..., description="Là workshop hay không")
    workshop_type: Optional[str] = Field(None, description="Tên loại workshop")
    date_begin: datetime = Field(..., description="Thời gian bắt đầu")
    date_end: datetime = Field(..., description="Thời gian kết thúc")
    duration_hours: float = Field(..., description="Thời lượng (giờ)")
    organizer: Optional[str] = Field(None, description="Tổ chức")
    address: Optional[str] = Field(None, description="Địa chỉ")
    seats_available: Optional[int] = Field(None, description="Số chỗ còn trống")
    description: Optional[str] = Field(None, description="Mô tả chi tiết")


class PublicEventDetail(PublicEventBase):
    """Schema chi tiết công khai sự kiện"""

    presenters: List[PublicPresenter] = Field([], description="Danh sách người trình bày")


# Response models
class PublicEventListResponse(ResponseBase[List[PublicEventBase]]):
    """Response công khai cho danh sách sự kiện"""

    @classmethod
    def paginated_response(
        cls,
        data: List[Any],
        page: int,
        per_page: int,
        total: int,
        message: Optional[str] = "Dữ liệu được lấy thành công",
    ):
        """Tạo paginated response"""
        pages = (total + per_page - 1) // per_page if per_page > 0 else 0

        # Tạo meta data với thông tin phân trang
        meta_data = get_meta_data()
        meta_data.update({
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": pages
            }
        })

        return cls(
            success=True,
            message=message,
            data=data,
            meta=meta_data,
        )


class PublicEventDetailResponse(ResponseBase[PublicEventDetail]):
    """Response công khai cho chi tiết sự kiện"""
    pass
